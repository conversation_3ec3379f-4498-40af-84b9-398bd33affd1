<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('merchant_payment_methods', function (Blueprint $table) {
            $table->boolean('is_default')->default(false)->after('is_postpay');
        });

        // Update existing default payment methods
        DB::table('merchant_payment_methods')
            ->whereIn('name', ['شبكة', 'كاش', 'دفع بالآجل'])
            ->update(['is_default' => true]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('merchant_payment_methods', function (Blueprint $table) {
            $table->dropColumn('is_default');
        });
    }
};
