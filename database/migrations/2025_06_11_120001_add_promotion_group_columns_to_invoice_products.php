<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('merchant_sale_invoice_products', function (Blueprint $table) {
            $table->string('promotion_group_id')->nullable()->after('promotion_id');

            $table->string('promotion_product_type')->nullable()->after('promotion_group_id');

            $table->integer('promotion_group_number')->nullable()->after('promotion_product_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('merchant_sale_invoice_products', function (Blueprint $table) {
            $table->dropColumn([
                'promotion_group_id',
                'promotion_product_type',
                'promotion_group_number'
            ]);
        });
    }
};
