<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchant_sale_invoice_promotion_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('merchant_id')->nullable();
            $table->foreignId('business_id')->nullable();
            $table->foreignId('invoice_id')->nullable();
            $table->string('group_id')->unique()->nullable();
            $table->foreignId('original_promotion_id')->nullable();
            $table->json('promotion_snapshot')->nullable();
            $table->integer('buy_x_quantity_required')->nullable();
            $table->integer('get_y_quantity_given')->nullable();
            $table->string('promotion_type')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchant_sale_invoice_promotion_groups');
    }
};
