<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\Application\Web\Auth\ForgotPasswordController;
use App\Http\Controllers\Application\Web\Auth\LoginController;
use App\Http\Controllers\Application\Web\Auth\RegisterController;
use App\Http\Controllers\Application\Web\Auth\ResetPasswordController;
use App\Http\Controllers\Application\Web\CategoriesController;
use App\Http\Controllers\Application\Web\CustomersController;
use App\Http\Controllers\Application\Web\BusinessEmployeeController;
use App\Http\Controllers\Application\Web\EmployeesController;
use App\Http\Controllers\Application\Web\ExpensesController;
use App\Http\Controllers\Application\Web\GroupsController;
use App\Http\Controllers\Application\Web\HomeController;
use App\Http\Controllers\Application\Web\InventoryController;
use App\Http\Controllers\Application\Web\InvoicesController;
use App\Http\Controllers\Application\Web\LocaleController;
use App\Http\Controllers\Application\Web\MarketplaceController;
use App\Http\Controllers\Application\Web\NotificationController;
use App\Http\Controllers\Application\Web\PoSController;
use App\Http\Controllers\Application\Web\ProductsController;
use App\Http\Controllers\Application\Web\ReportsController;
use App\Http\Controllers\Application\Web\SettingsController;
use App\Http\Controllers\Application\Web\SubscriptionController;
use App\Http\Controllers\Application\Web\SuppliersController;
use App\Http\Controllers\Application\Web\TestController;
use App\Http\Controllers\Application\Web\ZatcaController;
use App\Http\Controllers\Application\Web\CustomerDisplayController;
use App\Http\Controllers\Application\Web\FulfillmentController;
use App\Livewire\Pos;
use App\Livewire\PosCustomerScreen;
use App\Livewire\FulfillmentUpdate;
use App\Livewire\FulfillmentsList;
use App\Http\Controllers\Application\Web\BusinessController;
use App\Http\Controllers\Application\Web\BusinessSelectionController;
use App\Http\Controllers\Application\Web\PromotionsController;
use Illuminate\Support\Facades\Route;

// Marble Routes
Route::get('setlocale/{locale}',[LocaleController::class,'setLocale'])->name('setlocale');

// Unregister customer display when page is closed (no auth required as it's called via beacon)
Route::get('/customer-display/unregister/{sessionId}', [CustomerDisplayController::class, 'unregister'])
	->name('customer-display.unregister');

Route::get('/', [HomeController::class, 'index'])->name("home");
Route::get('/home', [HomeController::class, 'index']);

Route::middleware(['auth:merchants'])->group(function () {

	// Route::get('/pos', [PoSController::class,'index']);
	Route::get('/pos', Pos::class);
	Route::get('/pos/{branchId?}/{registeryId?}', Pos::class);
	Route::get('/pos/{branchId?}/{registeryId?}/customer-screen', PosCustomerScreen::class);

	// Customer-facing display screen
	Route::get('/pos/{branchId}/{registerId}/customer-display', [CustomerDisplayController::class, 'show'])
		->name('pos.customer-display');

});

Route::middleware(['auth:merchants'])->group(function () {

	Route::get('/imp',[TestController::class,'imp'])->name("imp");
});
Route::get('/pos/{branchId?}/{registeryId?}/open', [PoSController::class, 'open'])->name("pos.open");
Route::post('/pos/{branchId?}/{registeryId?}/open/store', [PoSController::class, 'open_store'])->name("pos.open_store");
Route::get('/pos/{branchId?}/{registeryId?}/close', [PoSController::class, 'close'])->name("pos.close");
Route::post('/pos/{branchId?}/{registeryId?}/close/store', [PoSController::class, 'close_store'])->name("pos.close_store");

// terms
Route::get('/register/terms', [RegisterController::class, 'terms'])->name("terms");
Route::get('/ttt', [TestController::class, 'ttt'])->name("ttt");

// Reports
Route::get('/reports/sales/summary', [ReportsController::class, 'sales_summary'])->name("reports.sales_summary");
Route::get('/reports/sales/summary/export', [ReportsController::class, 'sales_summary_export'])->name("reports.sales_summary_export");
Route::get('/reports/sales/products', [ReportsController::class, 'sales_products'])->name("reports.sales_products");
Route::get('/reports/sales/products/export', [ReportsController::class, 'sales_products_export'])->name("reports.sales_products_export");
Route::get('/reports/sales/payment-methods', [ReportsController::class, 'sales_payment_methods'])->name("reports.sales_payment_methods");
Route::get('/reports/sales/payment-methods/export', [ReportsController::class, 'sales_payment_methods_export'])->name("reports.sales_payment_methods_export");

Route::get('/reports/tax/', [ReportsController::class, 'tax'])->name("reports.tax");
Route::get('/reports/tax/export', [ReportsController::class, 'tax_export'])->name("reports.tax_export");

Route::get('/reports/expenses/', [ReportsController::class, 'expenses'])->name("reports.expenses");
Route::get('/reports/expenses/export', [ReportsController::class, 'expenses_export'])->name("reports.expenses_export");

Route::get('/reports/reconciliation', [ReportsController::class, 'reconciliation'])->name("reports.reconciliation");



// expenses add
Route::get('/expenses', [ExpensesController::class, 'index'])->name("expenses");
Route::get('/expenses/add', [ExpensesController::class, 'add'])->name("expenses.add.get");
Route::post('/expenses/add/store', [ExpensesController::class, 'store'])->name("expenses.add.store");
Route::get('/expenses/barcode/{id}', [ExpensesController::class, 'barcode'])->name("expenses.barcode");
/// expenses edit
Route::get('/expenses/edit/{id}', [ExpensesController::class, 'edit'])->name("expenses.edit");
Route::post('/expenses/edit/{id}/store', [ExpensesController::class, 'edit_store'])->name("expenses.edit_store");
Route::get('/expenses/delete/{id}', [ExpensesController::class, 'delete'])->name("expenses.delete");


// marketplace
Route::middleware(['auth:merchants', 'throttle:60,1'])->group(function () {
    Route::controller(MarketplaceController::class)->group(function () {
        Route::get('/marketplace', 'index')->name("marketplace");
        Route::get('/marketplace/details/{marketplace}', 'details')->name("marketplace.details");
        Route::get('/marketplace/details/{marketplace}/install', 'install')->name("marketplace.install");
        Route::delete('/marketplace/details/{marketplace}/deactivate', 'deactivate')->name("marketplace.deactivate");
    });

    //zatca
    Route::post('/marketplace/{marketplace}/install', [ZatcaController::class, 'onboarding'])->name("zatca.onboarding");
    Route::post('/marketplace/{marketplace}/send-old-invoices', [ZatcaController::class, 'sendOldInvoices'])->name("zatca.send_old_invoices");

    Route::controller(ProductsController::class)
        ->prefix('/products')
        ->group(function () {
            // products add
            Route::get('',  'index')->name("products");
            Route::get('/add',  'add')->name("products.add.get");
            Route::get('/weighted/add',  'add')->name("products.weighted.add.get");
            Route::get('/service/add',  'add')->name("products.service.add.get");
            Route::get('/composite/add',  'add_composite')->name("products.composite.add.get");
            Route::post('/add/store',  'store')->name("products.add.store");
            Route::post('/weighted/add/store',  'store')->name("products.weighted.add.store");
            Route::post('/service/add/store',  'store')->name("products.service.add.store");
            Route::post('/composite/add/store',  'store_composite')->name("products.composite.add.store");
            Route::get('/variant/add',  'add_variant')->name("products.variant.add.get");
            Route::post('/variant/add/store',  'store_variant')->name("products.variant.add.store");
            Route::get('/barcode/{product}',  'barcode')->name("products.barcode");
            Route::get('/export',  'export')->name("products.export");
            Route::get('/template-export',  'templateExport')->name("products.template-export");
            Route::get('/import',  'import')->name("products.import");
            Route::post('/import/store',  'import_store')->name("products.import_store");

            // products edit
            Route::get('/edit/{product}',  'edit')->name("products.edit");
            Route::get('/edit/composite/{product}',  'edit_composite')->name("products.composite.edit");
            Route::get('/edit/variant/{product}',  'edit_variant')->name("products.variant.edit");
            Route::post('/edit/{product}/store',  'update')->name("products.edit_store");
            Route::post('/edit/{product}/composite/store', 'update_composite')->name("products.composite.edit.store");
            Route::post('/edit/{product}/variant/store', 'update_variant')->name("products.variant.edit.store");
            Route::get('/delete/{product}',  'delete')->name("products.delete");

            Route::controller(CategoriesController::class)
                ->prefix('/categories')
                ->group(function () {
                    // Catagories
                    Route::get('', 'index')->name("categories");
                    Route::get('/add', 'add')->name("categories.add.get");
                    Route::post('/add/store', 'store')->name("categories.add.store");
                    Route::get('/view/', 'add')->name("categories.add");
                    Route::get('/edit/{category}', 'edit')->name("categories.edit");
                    Route::post('/edit/{category}/store', 'edit_store')->name("categories.edit.store");
                });
        });

    Route::controller(CustomersController::class)
        ->prefix('/customers')
        ->group(function () {

            // customers add
            Route::get('/', 'index')->name("customers");
            Route::get('/display/{id}', 'display')->name("customers.display");
            Route::get('/add', 'add')->name("customers.add.get");
            Route::post('/add/store', 'store')->name("customers.add.store");

            // customers edit
            Route::get('/edit/{id}', 'edit')->name("customers.edit");
            Route::post('/edit/{id}/store', 'update')->name("customers.edit_store");
//            Route::get('/delete/{id}', 'delete')->name("customers.delete");
        });

    Route::controller(SuppliersController::class)
        ->prefix('/suppliers')
        ->group(function () {

            // suppliers add
            Route::get('', 'index')->name("suppliers");
            Route::get('/display/{id}', 'display')->name("suppliers.display");
            Route::get('/add', 'add')->name("suppliers.add.get");
            Route::post('/add/store', 'store')->name("suppliers.add.store");

            // suppliers edit
            Route::get('/edit/{id}', 'edit')->name("suppliers.edit");
            Route::post('/edit/{id}/store', 'edit_store')->name("suppliers.edit_store");
            Route::get('/delete/{id}', 'delete')->name("suppliers.delete");
        });

    // subscription
    Route::controller(SubscriptionController::class)
        ->prefix('/subscription')
        ->group(function () {
            Route::get('',  'index')->name("subscription");
            Route::get('/subscribe/{id}',  'subscribe')->name("subscription.subscribe");
            Route::get('/addons',  'addons')->name("subscription.addons");
            Route::get('/addons/result',  'addons_result')->name("subscription.addons_result");
            Route::get('/addons/pay',  'addons_pay')->name("subscription.addons_pay");
            Route::post('/subscribe/{id}/pay',  'pay')->name("subscription.pay");
            Route::get('/subscribe/{id}/result',  'result')->name("subscription.result");
            Route::get('/invoices/print/{id}','print')->name("point-invoices.print");
        });

    Route::controller(SettingsController::class)
        ->prefix('/settings')
        ->group(function () {
            // settings
            Route::get('', 'index')->name("settings");
            Route::get('/main', 'main')->name("settings.main");
            Route::get('/main/edit', 'main_edit')->name("settings_main");
            Route::post('/main/edit/store', 'main_edit_store')->name("settings_main_store");

            Route::get('/payments/add', 'payments_add')->name("settings.payments.add");
            Route::post('/payments/add/store', 'payments_add_store')->name("settings.payments.add_store");
            Route::get('/payments/edit/{paymentMethod}', 'payments_edit')->name("payments_edit");
            Route::post('/payments/edit/{paymentMethod}/store', 'payments_edit_store')->name("payments_edit_store");

            Route::get('/tags/add', 'tags_add')->name("settings.tags.add");
            Route::post('/tags/add/store', 'tags_add_store')->name("settings.tags.add_store");
            Route::get('/tags/edit/{id}', 'tags_edit')->name("tags_edit");
            Route::post('/tags/edit/{id}/store', 'tags_edit_store')->name("tags_edit_store");

            Route::get('/invoices/edit/', 'invoices_edit')->name("invoices_edit");
            Route::post('/invoices/edit/store', 'invoices_edit_store')->name("invoices_edit_store");
            Route::get('/products/edit/', 'products_edit')->name("products_edit");
//            Route::post('/products/edit/store', 'products_edit_store')->name("products_edit_store");

            Route::get('/taxes/add', 'taxes_add')->name("settings.taxes.add");
            Route::post('/taxes/add/store', 'taxes_add_store')->name("settings.taxes.add_store");
            Route::get('/taxes/edit/{id}', 'taxes_edit')->name("taxes_edit");
            Route::post('/taxes/edit/{id}/store', 'taxes_edit_store')->name("taxes_edit_store");

            Route::get('/branches', 'branches_show')->name("branches.show");
            Route::get('/branches/{id}', 'branches_settings')->name("branches.settings");
            Route::post('/branches/{id}/store', 'branches_settings_store')->name("branches.settings_store");
            Route::post('/branches/store', 'branches_store')->name("branches.store");
            Route::post('/registers/store', [SettingsController::class, 'registers_store'])->name('registers.store');

            Route::get('/branches/{branchId}/registers/{registerId}', 'registers_settings')->name("branches.registers_settings");
            Route::post('/branches/{branchId}/registers/{registerId}/store', 'registers_settings_store')->name("registers_settings_store");

            Route::post('/weighted-product-settings', 'updateWeightedProductSettings')->name("weighted_product_settings");
        });

    Route::controller(PromotionsController::class)
        ->prefix('/promotions')
        ->group(function () {
            Route::get('', 'index')->name('promotions');
            Route::get('/add', 'add')->name('promotions.add');
            Route::post('/add/store', 'store')->name('promotions.add.store');
            Route::get('/detail/{promotion}', 'show')->name('promotions.detail');
            Route::get('/edit/{promotion}', 'edit')->name('promotions.edit');
            Route::post('/edit/{promotion}/store', 'update')->name('promotions.edit.store');
            Route::delete('/{promotion}', 'destroy')->name('promotions.destroy');
        });

});


Route::controller(NotificationController::class)->group(function () {
    Route::get('/notifications', 'index')->name("notifications");
    Route::post('/notifications/reed', 'read')->name("notifications.read");
    Route::get('/notifications/count', 'count')->name("notifications.count");
});

//

Route::get('/groups', [GroupsController::class, 'index'])->name("groups.index");
Route::get('/groups/add', [GroupsController::class, 'add'])->name("groups.add");
Route::get('/groups/edit/{id}', [GroupsController::class, 'edit'])->name("groups.edit");
Route::post('/groups/add/store', [GroupsController::class, 'store'])->name("groups.store");
Route::post('/groups/edit/{id}/store', [GroupsController::class, 'edit_store'])->name("groups.edit_store");




Route::get('/employees', [EmployeesController::class, 'index'])->name("employees.index");
Route::get('/employees/add', [EmployeesController::class, 'add'])->name("employees.add");
Route::get('/employees/edit/{id}', [EmployeesController::class, 'edit'])->name("employees.edit");
Route::post('/employees/add/store', [EmployeesController::class, 'store'])->name("employees.store");
Route::post('/employees/edit/{id}/store', [EmployeesController::class, 'edit_store'])->name("employees.edit_store");



// Purchase

Route::get('inventory/purchases', [InventoryController::class, 'purchase_index'])->name("inventory.purchase");
Route::get('inventory/purchases/add', [InventoryController::class, 'purchase_add'])->name("inventory.purchase.add");
Route::get('inventory/purchases/show/{id}', [InventoryController::class, 'purchase_show'])->name("inventory.purchase.show");

Route::get('inventory/counts/', [InventoryController::class, 'count_index'])->name("inventory.count.index");
Route::get('inventory/counts/add', [InventoryController::class, 'count_add'])->name("inventory.count.add");
Route::get('inventory/counts/show/{id}', [InventoryController::class, 'count_show'])->name("inventory.count.show");

Route::get('inventory/transfers/', [InventoryController::class, 'transfer_index'])->name("inventory.transfer.index");
Route::get('inventory/transfers/add', [InventoryController::class, 'transfer_add'])->name("inventory.transfer.add");
Route::get('inventory/transfers/show/{id}', [InventoryController::class, 'transfer_show'])->name("inventory.transfer.show");

Route::get('inventory/removes/', [InventoryController::class, 'remove_index'])->name("inventory.remove.index");
Route::get('inventory/removes/add', [InventoryController::class, 'remove_add'])->name("inventory.remove.add");
Route::get('inventory/removes/show/{id}', [InventoryController::class, 'remove_show'])->name("inventory.remove.show");


Route::get('/subscription/invoices', [SubscriptionController::class, 'invoices'])->name("subscription.invoices");


Route::get('/subscription/invoices/plans/{id}/', [SubscriptionController::class, 'invoices_show_plans'])->name("subscription.invoices_show_plans");
Route::get('/subscription/invoices/addons/{id}/', [SubscriptionController::class, 'invoices_show_addons'])->name("subscription.invoices_show_addons");


//apps

//Route::get('apps', [AppsController::class, 'index'])->name("apps.index");



// users

Route::get('/settings/users/add', [SettingsController::class, 'users_add'])->name("settings.users.add");
Route::post('/settings/users/add/store', [SettingsController::class, 'users_add_store'])->name("settings.users.add_store");
Route::get('/settings/users/edit/{id}', [SettingsController::class, 'users_edit'])->name("users_edit");
Route::post('/settings/users/edit/{id}/store', [SettingsController::class, 'users_edit_store'])->name("users_edit_store");
// currencies
Route::get('/settings/currencies/add', [SettingsController::class, 'currencies_add'])->name("settings.currencies.add");
Route::post('/settings/currencies/add/store', [SettingsController::class, 'currencies_add_store'])->name("settings.currencies.add_store");
Route::get('/settings/currencies/edit/{id}', [SettingsController::class, 'currencies_edit'])->name("currencies_edit");
Route::post('/settings/currencies/edit/{id}/store', [SettingsController::class, 'currencies_edit_store'])->name("currencies_edit_store");

//invoices
Route::get('/invoices', [InvoicesController::class, 'index'])->name("Invoices.index");
Route::get('/invoices/export', [InvoicesController::class, 'export'])->name("Invoices.export");




Route::get('/invoice',[TestController::class,'invoice'])->name("invoice");
Route::get('/invoices/display/{id}',[InvoicesController::class,'display'])->name("invoices.display");
Route::get('/invoices/receive/{id}',[InvoicesController::class,'receive'])->name("invoices.receive");
Route::post('/invoices/receive/{id}/store',[InvoicesController::class,'receive_store'])->name("invoices.receive_store");
Route::get('/invoices/print/{id}',[InvoicesController::class,'print'])->name("invoices.print");
Route::get('/invoices/receive/print/{id}',[InvoicesController::class,'receive_print'])->name("invoices.receive.print");
Route::get('/invoices/return/{id}',[InvoicesController::class,'return'])->name("invoices.return");
Route::get('/invoices/print/a4/{id}',[InvoicesController::class,'printa4'])->name("invoices.printa4");
Route::post('/invoices/return/{id}/store',[InvoicesController::class,'return_store'])->name("invoices.return_store");

// Fulfillment routes
Route::middleware(['auth:merchants'])->group(function () {
    // Controller routes for fulfillment pages
    Route::controller(FulfillmentController::class)
        ->prefix('/fulfillments')
        ->group(function () {
            Route::get('', 'index')->name('fulfillments.index');
            Route::get('/{id}', 'show')->name('fulfillments.show');
            Route::get('/{id}/update', 'update')->name('fulfillments.update');
            Route::post('/{id}/cancel', 'cancel')->name('fulfillments.cancel');
        });
});

// Authentication routes
Route::post('register/verify', [RegisterController::class,'complete'])->name('verify_number');
//



// Additional Auth Routes
Route::get('login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login']);
Route::get('logout', [LoginController::class,'logout'])->name('logout');

Route::get('register', [RegisterController::class,'showRegistrationForm'])->name('register');
Route::post('register', [RegisterController::class,'register']);
Route::post('register/complete', [RegisterController::class, 'complete'])->name('register-complete');
Route::get('email/confirmation/{token}', [RegisterController::class, 'emailConfirmation'])->name('email-confirmation');

Route::get('password/email', [ForgotPasswordController::class,'showResetEmail'])->name('password.email');
Route::post('password/send-email', [ForgotPasswordController::class,'sendResetLinkEmail'])->name('password.send-email');
Route::get('password/reset/{token}', [ResetPasswordController::class,'showResetForm'])->name('password.reset');
Route::post('password/change-password', [ResetPasswordController::class,'resetPassword'])->name('password.change-password');

// // Promotions Routes
// Route::middleware(['auth', 'web'])->group(function () {
//     Route::get('/promotions', [PromotionsController::class, 'index'])->name('promotions');
//     Route::get('/promotions/add', [PromotionsController::class, 'add'])->name('promotions.add');
//     Route::post('/promotions/add', [PromotionsController::class, 'store'])->name('promotions.add.store');
//     Route::get('/promotions/edit/{id}', [PromotionsController::class, 'edit'])->name('promotions.edit');
//     Route::put('/promotions/edit/{id}', [PromotionsController::class, 'update'])->name('promotions.edit.update');
//     Route::delete('/promotions/{id}', [PromotionsController::class, 'destroy'])->name('promotions.delete');
// });


// Business selection route (after login)
Route::middleware(['auth:merchants'])->group(function () {
    Route::get('/business/select', [BusinessSelectionController::class, 'select'])
        ->name('business.select');


    // Routes only accessible to main account (owner)
    Route::controller(BusinessController::class)
        ->group(function () {

            Route::get('/business/set-default', 'setDefault')
                ->name('business.set-default');
            Route::get('/businesses', 'index')
                ->name('business.index');
            Route::get('/business/switch', 'switch')
                ->name('business.switch');

            Route::middleware(['require.main.account'])->group(function (){
                  Route::get('/business/create', 'create')
                      ->name('business.create');
                  Route::post('/business/store', 'store')
                      ->name('business.store');
                  Route::get('/employees/businesses', function() {
                      return view('business.employee-index');
                  })->name('employees.businesses.index');
            });
    });
});
