@extends('theme::marble.layout')

@section('content')
@include('theme::marble.branch-sidebar')


					<!--begin::Main-->
					<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
						<!--begin::Content wrapper-->
						<div class="d-flex flex-column flex-column-fluid">
							<!--begin::Content-->
							<div id="kt_app_content" class="app-content flex-column-fluid">
								<!--begin::Toolbar-->
								<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
									<!--begin::Toolbar container-->
									<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
										<!--begin::Page title-->
										<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
											<!--begin::Title-->
											<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.branches_registeries')}}</h1>
											<!--end::Title-->
											<!--begin::Breadcrumb-->
											<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
												<!--begin::Item-->
												<li class="breadcrumb-item text-muted">
													<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
												</li>
												<!--end::Item-->
												<!--begin::Item-->
												<li class="breadcrumb-item">
													<span class="bullet bg-gray-400 w-5px h-2px"></span>
												</li>
												<!--end::Item-->
												<!--begin::Item-->
												<li class="breadcrumb-item text-muted">{{__('messages.branches_registeries')}}</li>
												<!--end::Item-->
											</ul>
											<!--end::Breadcrumb-->
										</div>
										<!--end::Page title-->
									</div>
									<!--end::Toolbar container-->
								</div>
								<!--end::Toolbar-->

								<!--begin::Content container-->
								<div id="kt_app_content_container" class="app-container container-xxl">
									@if ($errors->any())
										<div class="alert alert-danger">
											<ul>
												@foreach ($errors->all() as $error)
													<li>{{ $error }}</li>
												@endforeach
											</ul>
										</div>
									@endif
									@if (session('success'))
										<div class="alert alert-success">
											<ul>
													<li>{{ session('success') }}</li>
											</ul>
										</div>
									@endif

									<!--begin::Followers toolbar-->
									<div class="d-flex flex-wrap flex-stack mb-6">
										<!--begin::Title-->
										<h3 class="text-gray-800 fw-bold my-2">{{__('messages.show_branches')}}
										<span class="fs-6 text-gray-400 fw-semibold ms-1">({{$branches->count()}})</span></h3>
										<!--end::Title-->
									</div>
									<!--end::Followers toolbar-->
									<!--begin::Row-->
									<div class="row g-6 mb-6 g-xl-9 mb-xl-9">
										<!--begin::Followers-->
										<!--begin::Col-->
                                        @foreach ($branches as $branch )
                                        <div class="col-md-6 col-xxl-4">
											<!--begin::Card-->
											<a href="{{url("settings/branches/{$branch['id']}")}}">
												<div class="card">

													<!--begin::Card body-->
													<div class="card-body d-flex flex-center flex-column py-9 px-5">
														<!--begin::Avatar-->
														<div class="symbol symbol-65px symbol-circle mb-5">
															<a href="{{url("settings/branches/{$branch['id']}")}}">
																<span class="symbol-label fs-2x fw-semibold text-warning bg-light-warning">{{$branch['branch_name']}}</span>
															</a>
														</div>
														<!--end::Avatar-->
														<!--begin::Name-->
														<a href="{{url("settings/branches/{$branch['id']}")}}" class="fs-4 text-gray-800 text-hover-primary fw-bold mb-0">{{$branch['branch_name']}}</a>
														<!--end::Name-->
														<!--begin::Position-->
														<div class="fw-semibold text-gray-400 mb-6">{{$branch['branch_address']}}</div>
														<!--end::Position-->
														<!--begin::Info-->
														<div class="d-flex flex-center flex-wrap mb-5">
															<!--begin::Stats-->
															<a href="{{url("settings/branches/{$branch['id']}")}}">
																<div class="border border-dashed rounded min-w-90px py-3 px-4 mx-2 mb-3">
																	<div class="fs-6 fw-bold text-gray-700 text-center">{{$branch['register_count']}}</div>
																	<div class="fw-semibold text-gray-400 text-center">{{__('messages.registers')}}</div>
																</div>
															</a>
															<!--end::Stats-->
															<!--begin::Stats-->
														</div>
														<!--end::Info-->
													</div>
													<!--begin::Card body-->
												</div>
											</a>
											<!--begin::Card-->
										</div>

                                        @endforeach

										<!--end::Col-->
										<!--end::Followers-->
									</div>
									<!--end::Row-->
								</div>
								<!--end::Content container-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Content wrapper-->
						<!--begin::Footer-->
						@include('theme::marble.footer')
						<!--end::Footer-->
					</div>
					<!--end:::Main-->

        <script>
        </script>

@endsection

