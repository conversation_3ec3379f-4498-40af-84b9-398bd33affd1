@extends('theme::marble.emails.layout')

@section('title', $title ?? (app()->getLocale() === 'ar' ? 'إشعار من النظام' : 'System Notification'))

@section('header')
    {{ $header ?? (app()->getLocale() === 'ar' ? 'إشعار مهم' : 'Important Notification') }}
@endsection

@section('content')
    @if(app()->getLocale() === 'ar')
        <p>مرحبًا {{ $user->name ?? 'عزيزي المستخدم' }}،</p>
        
        @if(isset($message))
            <p>{{ $message }}</p>
        @endif
        
        @if(isset($details))
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; border-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }}: 4px solid #009ef7;">
                {!! $details !!}
            </div>
        @endif
        
        @if(isset($actionUrl) && isset($actionText))
            @component('theme::marble.emails.button', ['url' => $actionUrl])
                {{ $actionText }}
            @endcomponent
        @endif
        
        @if(isset($footerMessage))
            <p>{{ $footerMessage }}</p>
        @endif
        
        <p>مع تحيات،<br>فريق {{ config('app.name') }}</p>
    @else
        <p>Hello {{ $user->name ?? 'there' }},</p>
        
        @if(isset($message))
            <p>{{ $message }}</p>
        @endif
        
        @if(isset($details))
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #009ef7;">
                {!! $details !!}
            </div>
        @endif
        
        @if(isset($actionUrl) && isset($actionText))
            @component('theme::marble.emails.button', ['url' => $actionUrl])
                {{ $actionText }}
            @endcomponent
        @endif
        
        @if(isset($footerMessage))
            <p>{{ $footerMessage }}</p>
        @endif
        
        <p>Best regards,<br>{{ config('app.name') }} Team</p>
    @endif
@endsection
