@extends('theme::marble.emails.layout')

@section('title')
    @if(app()->getLocale() === 'ar')
        مرحبًا بك في {{ config('app.name') }}
    @else
        Welcome to {{ config('app.name') }}
    @endif
@endsection

@section('header')
    @if(app()->getLocale() === 'ar')
        مرحبًا بك!
    @else
        Welcome!
    @endif
@endsection

@section('content')
    @if(app()->getLocale() === 'ar')
        <p>مرحبًا {{ $user->name ?? 'عزيزي المستخدم' }}،</p>
        
        <p>مرحبًا بك في {{ config('app.name') }}! نحن متحمسون لانضمامك إلى منصتنا.</p>
        
        <p>يمكنك الآن البدء في استخدام جميع الميزات المتاحة لك:</p>
        
        <ul style="text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }}; padding-{{ app()->getLocale() === 'ar' ? 'right' : 'left' }}: 20px;">
            <li>إدارة المنتجات والمخزون</li>
            <li>معالجة المبيعات والفواتير</li>
            <li>تتبع التقارير والتحليلات</li>
            <li>إدارة العملاء والموردين</li>
        </ul>
        
        @if(isset($dashboardUrl))
            @component('theme::marble.emails.button', ['url' => $dashboardUrl])
                ابدأ الآن
            @endcomponent
        @endif
        
        <p>إذا كان لديك أي أسئلة أو تحتاج إلى مساعدة، فريق الدعم لدينا هنا لمساعدتك.</p>
        
        <p>مرة أخرى، مرحبًا بك!<br>فريق {{ config('app.name') }}</p>
    @else
        <p>Hello {{ $user->name ?? 'there' }},</p>
        
        <p>Welcome to {{ config('app.name') }}! We're excited to have you join our platform.</p>
        
        <p>You can now start using all the features available to you:</p>
        
        <ul style="text-align: left; padding-left: 20px;">
            <li>Manage products and inventory</li>
            <li>Process sales and invoices</li>
            <li>Track reports and analytics</li>
            <li>Manage customers and suppliers</li>
        </ul>
        
        @if(isset($dashboardUrl))
            @component('theme::marble.emails.button', ['url' => $dashboardUrl])
                Get Started
            @endcomponent
        @endif
        
        <p>If you have any questions or need assistance, our support team is here to help you.</p>
        
        <p>Welcome aboard!<br>{{ config('app.name') }} Team</p>
    @endif
@endsection
