# Point Email Template System

This directory contains the standardized email templates for the Point application. All email templates follow a consistent design pattern to ensure a cohesive user experience.

## Template Structure

### Base Layout (`layout.blade.php`)
The main layout template that provides:
- Consistent branding with Point logo and colors
- Responsive design for mobile and desktop
- RTL/LTR support for Arabic and English
- Dark mode support
- Professional styling with Point brand colors (#009ef7, #181c32)

### Components

#### Button Component (`button.blade.php`)
Reusable button component for call-to-action buttons in emails.

**Usage:**
```blade
@component('theme::marble.emails.button', ['url' => $actionUrl])
    Button Text
@endcomponent
```

## Available Templates

### 1. Account Confirmation (`confirmation-email.blade.php`)
Used for email verification when users register.

**Variables:**
- `$verificationLink` - The verification URL

### 2. Password Reset (`password-reset.blade.php`)
Used when users request password reset.

**Variables:**
- `$resetUrl` - The password reset URL
- `$user` - The user object
- `$token` - The reset token

### 3. Export Completed (`export-completed.blade.php`)
Used when data exports are completed.

**Variables:**
- `$exportType` - Type of export (e.g., "Products", "Invoices")

### 4. Welcome Email (`welcome.blade.php`)
Template for welcoming new users.

**Variables:**
- `$user` - The user object
- `$dashboardUrl` - Optional dashboard URL

### 5. System Notification (`notification.blade.php`)
Generic template for system notifications.

**Variables:**
- `$title` - Email title (optional)
- `$header` - Header text (optional)
- `$message` - Main message
- `$details` - Additional details (optional, supports HTML)
- `$actionUrl` - Action button URL (optional)
- `$actionText` - Action button text (optional)
- `$footerMessage` - Footer message (optional)
- `$user` - The user object

## Creating New Email Templates

To create a new email template:

1. Create a new Blade file in this directory
2. Extend the base layout:
```blade
@extends('theme::marble.emails.layout')
```

3. Define sections:
```blade
@section('title', 'Your Email Title')

@section('header')
    Your Header Text
@endsection

@section('content')
    Your email content here...
@endsection
```

## Localization Support

All templates support both Arabic and English:

```blade
@if(app()->getLocale() === 'ar')
    Arabic content
@else
    English content
@endif
```

## Design Guidelines

### Colors
- Primary: #009ef7 (Point Blue)
- Dark: #181c32 (Point Dark)
- Background: #f4f4f4
- Text: #333333
- Footer: #6c757d

### Typography
- Arabic: IBM Plex Sans Arabic
- English: Inter
- Responsive font sizes

### Layout
- Max width: 600px
- Responsive design
- Consistent padding and margins
- Professional appearance

## Usage in Notifications

### Creating a Custom Notification

```php
<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class YourCustomNotification extends Notification
{
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from(config('mail.from.address'), config('mail.from.name'))
            ->view('theme::marble.emails.your-template', [
                'user' => $notifiable,
                'customVariable' => $this->customData,
            ]);
    }
}
```

### Using in Mailable Classes

```php
<?php

namespace App\Mail;

use Illuminate\Mail\Mailable;

class YourMailable extends Mailable
{
    public function build()
    {
        return $this->view('theme::marble.emails.your-template', [
            'data' => $this->data,
        ]);
    }
}
```

## Best Practices

1. Always use the base layout for consistency
2. Include both Arabic and English content
3. Use the button component for actions
4. Keep content concise and clear
5. Test emails in both languages
6. Ensure responsive design works on mobile
7. Use semantic HTML structure
8. Include fallback text for action buttons
