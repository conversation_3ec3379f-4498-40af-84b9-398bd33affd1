<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', config('app.name'))</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@400;500;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

        body {
            font-family: {{ app()->getLocale() === 'ar' ? "'IBM Plex Sans Arabic', sans-serif" : "'Inter', sans-serif" }};
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            direction: {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }};
            padding: 20px;
            box-sizing: border-box;
        }
        
        .container {
            width: 100%;
            max-width: 600px;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
            text-align: center;
        }
        
        .logo-container {
            background-color: #181c32;
            padding: 20px;
        }
        
        .logo {
            max-width: 250px;
            height: auto;
        }
        
        .header {
            background-color: #009ef7;
            padding: 20px;
            color: #ffffff;
            font-size: 24px;
            font-weight: 700;
        }
        
        .content {
            padding: 30px 20px;
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
            text-align: {{ app()->getLocale() === 'ar' ? 'right' : 'left' }};
        }
        
        .content p {
            margin: 0 0 15px 0;
        }
        
        .content p:last-child {
            margin-bottom: 0;
        }
        
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .button {
            display: inline-block;
            background-color: #009ef7;
            color: #ffffff !important;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        
        .button:hover {
            background-color: #0084d1;
        }
        
        .footer {
            background-color: #f8f9fa;
            text-align: center;
            padding: 20px;
            font-size: 14px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        /* Responsive design */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .container {
                border-radius: 0;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .header {
                font-size: 20px;
                padding: 15px;
            }
            
            .logo {
                max-width: 200px;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
            }
            
            .container {
                background-color: #2d2d2d;
                box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
            }
            
            .content {
                color: #e0e0e0;
            }
            
            .footer {
                background-color: #1f1f1f;
                color: #a0a0a0;
                border-top-color: #404040;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo-container">
            <img src="{{ asset('marble/src/media/logos/point-white.svg') }}" alt="{{ config('app.name') }} Logo" class="logo">
        </div>
        
        @hasSection('header')
        <div class="header">
            @yield('header')
        </div>
        @endif
        
        <div class="content">
            @yield('content')
        </div>
        
        <div class="footer">
            @if(app()->getLocale() === 'ar')
                &copy; {{ date('Y') }} جميع الحقوق محفوظة لبوينت
            @else
                &copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
            @endif
        </div>
    </div>
</body>
</html>
