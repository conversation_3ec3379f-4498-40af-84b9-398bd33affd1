@extends('theme::marble.layout')

@section('content')
    <!--begin::Main-->
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <!--begin::Content wrapper-->
        <div class="d-flex flex-column flex-column-fluid">
            <!--begin::Toolbar-->
            <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                <!--begin::Toolbar container-->
                <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                    <!--begin::Page title-->
                    <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                        <!--begin::Title-->
                        <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.settings')}}</h1>
                        <!--end::Title-->
                        <!--begin::Breadcrumb-->
                        <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                            <!--begin::Item-->
                            <li class="breadcrumb-item text-muted">
                                <a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
                            </li>
                            <!--end::Item-->
                            <!--begin::Item-->
                            <li class="breadcrumb-item">
                                <span class="bullet bg-gray-400 w-5px h-2px"></span>
                            </li>
                            <!--end::Item-->
                            <!--begin::Item-->
                            <li class="breadcrumb-item text-muted">{{__('messages.settings')}}</li>
                            <!--end::Item-->
                        </ul>
                        <!--end::Breadcrumb-->
                    </div>
                    <!--end::Page title-->
                </div>
                <!--end::Toolbar container-->
            </div>
            <!--end::Toolbar-->
            <!--begin::Content-->

            <div id="kt_app_content" class="app-content flex-column-fluid">
                <!--begin::Content container-->
                <div id="kt_app_content_container" class="app-container container-xxl">
                    <!--begin::Row-->

                    <div class="row g-5 g-xl-8">
                        <div class="col-xl-12">

                            <form method="post">
                                <div class="card card-xl-stretch mb-xl-8">
                                    <!-- TABS -->
                                    <div class="card-body pt-5">


                                        <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6">
                                            @if($main_account || $permissions['settings_main_show'])
                                                <li class="nav-item">
                                                    <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_pane_4">{{__('messages.main_settings')}}</a>
                                                </li>
                                            @endif
                                            {{-- @if($main_account || $permissions['invoice_settings_show'])
                                                <li class="nav-item">
                                                    <a class="nav-link" data-bs-toggle="tab" href="#product_settings">{{__('messages.product_settings')}}</a>
                                                </li>
                                            @endif --}}
                                            @if($main_account || $permissions['invoice_settings_show'])
                                                <li class="nav-item">
                                                    <a class="nav-link" data-bs-toggle="tab" href="#invoice_settings">{{__('messages.invoice_settings')}}</a>
                                                </li>
                                            @endif
                                            @if($main_account || $permissions['invoice_settings_show'])
                                                <li class="nav-item">
                                                    <a class="nav-link" data-bs-toggle="tab" href="#tags">{{__('messages.tags')}}</a>
                                                </li>
                                            @endif
                                            @if($main_account || $permissions['settings_show_payment_methods'])
                                                <li class="nav-item">
                                                    <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_5">{{__('messages.payment_methods')}}</a>
                                                </li>
                                            @endif
                                            @if($main_account || $permissions['settings_show_taxes'])
                                                <li class="nav-item">
                                                    <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_6">{{__('messages.tax_types')}}</a>
                                                </li>
                                            @endif
                                            @if($main_account || $permissions['settings_show_currencies'])
                                                <li class="nav-item">
                                                    <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_8">{{__('messages.currencies')}}</a>
                                                </li>
                                            @endif
                                        </ul>

                                        <div class="tab-content" id="myTabContent">
                                            @if($main_account || $permissions['settings_main_show'])

                                                <div class="tab-pane fade show active" id="kt_tab_pane_4" role="tabpanel">
                                                    <div class="card-header border-0">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.main_settings')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.main_settings_msg')}}</span>
                                                        </h3>
                                                        @if($main_account || $permissions['settings_main_edit'])
                                                        <div class="card-toolbar">
                                                                <!--begin::Menu-->
                                                                <a href="{{url('settings/main/edit')}}" class="btn btn-color-primary btn-active-light-primary">
                                                                {{__('messages.edit_main_setting')}}
                                                                </a>
                                                                <!--begin::Menu 3-->
                                                            </div>
                                                        @endif

                                                    </div>
                                                    <div class="card-body">
                                                        <label class="form-label">{{__('messages.company_logo')}}</label>
                                                        <div class="pt-0">
                                                            <!--begin::Input group-->
                                                            <div class="mb-10 fv-row">
                                                                <!--begin::Image input placeholder-->
                                                                @if(!empty($settings->logo))
                                                                <style>
                                                                    .image-input-placeholder {
                                                                        background-image: url('{{asset("storage/$settings->logo")}}');
                                                                    }

                                                                    [data-bs-theme="dark"] .image-input-placeholder {
                                                                        background-image: url('{{asset("storage/$settings->logo")}}');
                                                                    }
                                                                </style>
                                                                @else
                                                                <style>
                                                                    .image-input-placeholder {
                                                                        background-image: url('{{asset('marble/src/')}}/media/svg/files/blank-image.svg');
                                                                    }

                                                                    [data-bs-theme="dark"] .image-input-placeholder {
                                                                        background-image: url('{{asset('marble/src/')}}/media/svg/files/blank-image-dark.svg');
                                                                    }
                                                                </style>
                                                                @endif
                                                            </div>
                                                            <!--end::Image input placeholder-->

                                                            <!--begin::Image input-->
                                                            <div class="image-input image-input-empty image-input-outline image-input-placeholder" data-kt-image-input="true">
                                                                <!--begin::Image preview wrapper-->
                                                                <div class="image-input-wrapper w-125px h-125px"></div>

                                                            </div>
                                                            <!--end::Image input-->
                                                            <div class="mb-10 fv-row mt-10">
                                                                <div class="row">
                                                                    <div class="col">
                                                                        <!--begin::Label-->
                                                                        <label class="form-label">{{__('messages.company_name')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="name" class="form-control" placeholder="Company Name" disabled value="{{$settings->name}}" />
                                                                        <!--end::Input-->
                                                                        <!--begin::Description-->
                                                                        <div class="text-muted fs-7">{{__('messages.company_name_msg')}}.</div>
                                                                        <!--end::Description-->
                                                                    </div>
                                                                    <div class="col">
                                                                        <!--begin::Label-->
                                                                        <label class="form-label">{{__('validation.attributes.business_category')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="business_category" class="form-control" placeholder="{{__('validation.attributes.business_category')}}" disabled value="{{$settings->business_category}}" />
                                                                        <!--end::Input-->
                                                                        <!--begin::Description-->
                                                                        <div class="text-muted fs-7">{{__('messages.business_category')}}.</div>
                                                                        <!--end::Description-->
                                                                    </div>

                                                                </div>
                                                            </div>
                                                            <div class="mb-10 row">
                                                                <div class="col">
                                                                <!--begin::Label-->
                                                                <label class="form-label">{{__('messages.CR')}}</label>
                                                                <!--end::Label-->
                                                                <!--begin::Input-->
                                                                <input type="text" name="CR" class="form-control" placeholder="{{__('messages.CR')}}" disabled value="{{$settings->CR}}" />
                                                                <!--end::Input-->
                                                                <!--begin::Description-->
                                                                <div class="text-muted fs-7">{{__('messages.CR_msg')}}</div>
                                                                <!--end::Description-->
                                                                </div>
                                                                <div class="col">
                                                                <!--begin::Label-->
                                                                <label class="form-label">{{__('messages.vat_number')}}</label>
                                                                <!--end::Label-->
                                                                <!--begin::Input-->
                                                                <input type="text" name="vat" class="form-control" placeholder="{{__('messages.vat_number')}}" disabled value="{{$settings->VAT}}" />
                                                                <!--end::Input-->
                                                                <!--begin::Description-->
                                                                <div class="text-muted fs-7">{{__('messages.vat_msg')}}</div>
                                                                <!--end::Description-->
                                                                </div>
                                                            </div>
                                                            <div class="mb-10 fv-row">
                                                                <!--begin::Label-->
                                                                <label class="form-label">{{__('messages.phone_number')}}</label>
                                                                <!--end::Label-->
                                                                <!--begin::Input-->
                                                                <input type="text" name="phone" class="form-control" placeholder="{{__('messages.phone_number')}}" disabled value="{{$settings->phone}}" />
                                                                <!--end::Input-->
                                                                <!--begin::Description-->
                                                                <div class="text-muted fs-7">{{__('messages.phone_number_msg')}}</div>
                                                                <!--end::Description-->
                                                            </div>

                                                            <div class="mb-10 fv-row">
                                                                <div class="mb-3">
                                                                    <!--begin::Label-->
                                                                    <label class="form-label">{{__('messages.street_name')}}</label>
                                                                    <!--end::Label-->
                                                                    <!--begin::Input-->
                                                                    <input type="text" name="street" class="form-control" placeholder="{{__('messages.street_name')}}" disabled value="{{$address?->street}}" />
                                                                    <!--end::Input-->
                                                                </div>
                                                                <div class="row mb-3">
                                                                    <div class="col">
                                                                        <!-- Building Number -->
                                                                        <!--begin::Label-->
                                                                        <label class="form-label">{{__('messages.building_number')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="building_number" class="form-control" placeholder="{{__('messages.building_number')}}" disabled value="{{$address?->building_number}}" />
                                                                        <!--end::Input-->
                                                                    </div>

                                                                    <div class="col">
                                                                        <!-- Postal Code -->
                                                                        <!--begin::Label-->
                                                                        <label class="form-label">{{__('messages.postal_code')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="postal_code" class="form-control" placeholder="{{__('messages.postal_code')}}" disabled value="{{$address?->postal_code}}" />
                                                                        <!--end::Input-->
                                                                    </div>
                                                                </div>
                                                                <div class="row mb-3">
                                                                    <div class="col">
                                                                        <!-- Building Number -->
                                                                        <!--begin::Label-->
                                                                        <label class="form-label">{{__('messages.province')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="state" class="form-control" placeholder="{{__('messages.province')}}" disabled value="{{$address?->state?->name_ar}}" />
                                                                        <!--end::Input-->
                                                                    </div>
                                                                    <div class="col">
                                                                        <!-- Postal Code -->
                                                                        <!--begin::Label-->
                                                                        <label class="form-label">{{__('messages.city')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="city" class="form-control" placeholder="{{__('messages.city')}}" disabled value="{{$address?->city?->name_ar}}" />
                                                                        <!--end::Input-->
                                                                    </div>
                                                                </div>
                                                                {{-- @livewire('address-selector') --}}
                                                                <div class="mb-3">
                                                                    <!--begin::Label-->
                                                                    <label class="form-label">{{__('messages.district')}}</label>
                                                                    <!--end::Label-->
                                                                    <!--begin::Input-->
                                                                    <input type="text" name="district" class="form-control" placeholder="{{__('messages.district_name')}}" disabled value="{{$address?->district}}" />
                                                                    <!--end::Input-->
                                                                </div>
                                                            </div>
                                                            <!--end::Input group-->
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif

                                            @if($main_account)
                                                <div class="tab-pane fade" id="invoice_settings" role="tabpanel">
                                                    <div class="card-header border-0">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.invoice_settings')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.invoice_settings_desc')}}</span>
                                                        </h3>
                                                        @if($main_account || $permissions['invoice_settings_edit'])
                                                        <div class="card-toolbar">
                                                            <!--begin::Menu-->
                                                            <a href="{{url('settings/invoices/edit')}}" class="btn btn-color-primary btn-active-light-primary">
                                                            {{__('messages.edit_invoice_settings')}}
                                                            </a>
                                                            <!--begin::Menu 3-->
                                                        </div>
                                                        @endif
                                                    </div>
                                                    <div class="card-body pt-5">
                                                        <div class="mb-10 fv-row">
                                                            <div class="row">
                                                                <div class="col">
                                                                    <!--begin::Label-->
                                                                    <label class="required form-label">{{__('messages.invoice_notes')}}</label>
                                                                    <!--begin::Input group-->
                                                                    <div class="input-group">
                                                                        <textarea disabled class="form-control" aria-label="With textarea">{{isset($settings['invoice_notes']) ? $settings['invoice_notes']:''}}</textarea>
                                                                    </div>
                                                                    <!--end::Input group-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endif
                                            @if($main_account)
                                                {{-- <div class="tab-pane fade" id="product_settings" role="tabpanel">
                                                    <div class="card-header border-0">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.product_settings')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.product_settings_desc')}}</span>
                                                        </h3>
                                                        @if($main_account || $permissions['invoice_settings_edit'])
                                                        <div class="card-toolbar">
                                                            <!--begin::Menu-->
                                                            <a href="{{url('settings/products/edit')}}" class="btn btn-color-primary btn-active-light-primary">
                                                            {{__('messages.edit_product_settings')}}
                                                            </a>
                                                            <!--begin::Menu 3-->
                                                        </div>
                                                        @endif
                                                    </div>
                                                    <div class="card-body pt-5">
                                                        <div class="mb-10 fv-row">
                                                            <div class="row">
                                                                <div class="col">
                                                                    <!--begin::Label-->
                                                                    <label class="form-label">{{__('messages.extra_product_types')}}</label>
                                                                    <!--begin::Input group-->
                                                                    <div class="form-check form-switch form-check-custom form-check-solid">
                                                                        <input class="form-check-input" disabled type="checkbox" value="" id="flexSwitchDefault"/>
                                                                        <label class="form-check-label" style="color:black;" for="flexSwitchDefault">
                                                                            {{__('messages.enable_weighted_product_type')}}
                                                                        </label>
                                                                    </div>
                                                                    <!--end::Input group-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div> --}}
                                            @endif
                                            @if($main_account || $permissions['settings_show_payment_methods'])
                                                <div class="tab-pane fade" id="tags" role="tabpanel">
                                                    <div class="card-header border-0">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.tags')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.tags_desc')}}</span>
                                                        </h3>
                                                        @if($main_account || $permissions['settings_add_payment_methods'])
                                                        <div class="card-toolbar">
                                                            <!--begin::Menu-->
                                                            <a href="{{url('settings/tags/add')}}" class="btn btn-color-primary btn-active-light-primary">
                                                            {{__('messages.add_tag')}}
                                                            </a>
                                                            <!--begin::Menu 3-->
                                                        </div>
                                                        @endif
                                                    </div>
                                                    <div class="card-body pt-5">
                                                        @if(count($tags) > 0)
                                                            <!--begin::Item-->
                                                            @foreach($tags as $tag)
                                                                <div class="d-flex align-items-sm-center mb-7">
                                                                    <!--begin::Symbol-->
                                                                    <div class="symbol symbol-50px me-5">
                                                                        <span class="symbol-label">
                                                                            <img src="assets/media/svg/brand-logos/telegram.svg" class="h-50 align-self-center" alt="" />
                                                                        </span>
                                                                    </div>
                                                                    <!--end::Symbol-->
                                                                    <!--begin::Section-->
                                                                    <div class="d-flex align-items-center flex-row-fluid flex-wrap">
                                                                        <div class="flex-grow-1 me-2">
                                                                            @if($main_account || $permissions['settings_edit_tags'])
                                                                                <a href="{{url("settings/tags/edit/{$tag['id']}")}}" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$tag['name']}}</a>
                                                                            @else
                                                                                <a class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$tag['name']}}</a>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                    <!--end::Section-->
                                                                </div>
                                                            @endforeach
                                                            <!--end::Item-->
                                                        @else
                                                            <!--begin::Empty state-->
                                                            <div class="text-center py-10">
                                                                <div class="symbol symbol-100px mb-5">
                                                                    <span class="symbol-label bg-light-primary">
                                                                        <i class="ki-duotone ki-tag fs-2x text-primary">
                                                                            <span class="path1"></span>
                                                                            <span class="path2"></span>
                                                                            <span class="path3"></span>
                                                                        </i>
                                                                    </span>
                                                                </div>
                                                                <h3 class="fs-4 fw-bold text-gray-800 mb-2">{{__('messages.no_tags_found')}}</h3>
                                                                <div class="text-muted mb-5">{{__('messages.no_tags_description')}}</div>
                                                                @if($main_account || $permissions['settings_add_payment_methods'])
                                                                    <a href="{{url('settings/tags/add')}}" class="btn btn-sm btn-primary">
                                                                        {{__('messages.add_tag')}}
                                                                    </a>
                                                                @endif
                                                            </div>
                                                            <!--end::Empty state-->
                                                        @endif
                                                    </div>

                                                </div>
                                            @endif
                                            @if($main_account || $permissions['settings_show_payment_methods'])

                                                <div class="tab-pane fade" id="kt_tab_pane_5" role="tabpanel">
                                                    <div class="card-header border-0">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.payment_methods')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.payment_methods_msg')}}</span>
                                                        </h3>
                                                        @if($main_account || $permissions['settings_add_payment_methods'])
                                                        <div class="card-toolbar">
                                                            <!--begin::Menu-->
                                                            <a href="{{url('settings/payments/add')}}" class="btn btn-color-primary btn-active-light-primary">
                                                            {{__('messages.add_payment_method')}}
                                                            </a>
                                                            <!--begin::Menu 3-->
                                                        </div>
                                                        @endif
                                                    </div>
                                                    <div class="card-body pt-5">
                                                        <!--begin::Item-->
                                                        @foreach($payments as $payment)
                                                        @if($payment['is_postpay'] == 0)
                                                            <div class="d-flex align-items-sm-center mb-7">
                                                                <!--begin::Symbol-->
                                                                <div class="symbol symbol-50px me-5">
                                                                    <span class="symbol-label">
                                                                        <img src="assets/media/svg/brand-logos/telegram.svg" class="h-50 align-self-center" alt="" />
                                                                    </span>
                                                                </div>
                                                                <!--end::Symbol-->
                                                                <!--begin::Section-->
                                                                <div class="d-flex align-items-center flex-row-fluid flex-wrap">
                                                                    <div class="flex-grow-1 me-2">
                                                                        @if($main_account || $permissions['settings_edit_payment_methods'])
                                                                            <a href="{{url("settings/payments/edit/{$payment['id']}")}}" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$payment['name']}}</a>
                                                                        @else
                                                                            <a class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$payment['name']}}</a>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                                <!--end::Section-->
                                                            </div>
                                                        @endif
                                                        @endforeach
                                                        <!--end::Item-->
                                                    </div>

                                                </div>
                                            @endif
                                            @if($main_account || $permissions['settings_show_taxes'])
                                                <div class="tab-pane fade" id="kt_tab_pane_6" role="tabpanel">
                                                    <div class="card-header border-0 pt-5">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.tax_types')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.tax_types_msg')}}</span>
                                                        </h3>
                                                        @if($main_account || $permissions['settings_add_taxes'])
                                                            <div class="card-toolbar">
                                                                <!--begin::Menu-->
                                                                <a href="{{url('settings/taxes/add')}}" class="btn btn-color-primary btn-active-light-primary">
                                                                {{__('messages.add_tax_type')}}
                                                                </a>
                                                                <!--begin::Menu 3-->
                                                            </div>
                                                        @endif
                                                    </div>

                                                    <div class="card-body pt-5">
                                                        <!--begin::Item-->
                                                        @foreach($taxes as $tax)
                                                            @if($tax['code'] !== \App\Enums\Application\TaxCodeEnum::NONE->value)
                                                                <div class="d-flex align-items-sm-center mb-7">
                                                                    <!--begin::Symbol-->
                                                                    <div class="symbol symbol-50px me-5">
                                                                <span class="symbol-label">
                                                                    <img src="assets/media/svg/brand-logos/telegram.svg" class="h-50 align-self-center" alt="" />
                                                                </span>
                                                                    </div>
                                                                    <!--end::Symbol-->
                                                                    <!--begin::Section-->
                                                                    <div class="d-flex align-items-center flex-row-fluid flex-wrap">
                                                                        <div class="flex-grow-1 me-2">
                                                                            @if($main_account || $permissions['settings_edit_taxes'])

                                                                                <a href="{{url("settings/taxes/edit/{$tax['id']}")}}" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$tax['name']}}</a>
                                                                            @else
                                                                                <a class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$tax['name']}}</a>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                    <!--end::Section-->
                                                                </div>
                                                            @endif
                                                        @endforeach
                                                        <!--end::Item-->
                                                    </div>

                                                </div>
                                            @endif
                                            @if($main_account || $permissions['settings_show_currencies'])
                                            <div class="tab-pane fade" id="kt_tab_pane_8" role="tabpanel">
                                                <div class="card-header border-0 pt-5">
                                                    <h3 class="card-title align-items-start flex-column">
                                                        <span class="card-label fw-bold text-dark">{{__('messages.currencies')}}</span>
                                                        <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.currencies_desc')}}</span>
                                                    </h3>
                                                    @if($main_account || $permissions['settings_add_currencies'])
                                                        <div class="card-toolbar">
                                                            <!--begin::Menu-->
                                                            <a href="{{url('settings/currencies/add')}}" class="btn btn-color-primary btn-active-light-primary">
                                                            {{__('messages.currencies_add')}}
                                                            </a>
                                                            <!--begin::Menu 3-->
                                                        </div>
                                                    @endif
                                                </div>
                                                <!--end::Header-->
                                                <!--begin::Body-->
                                                <div class="card-body pt-5">
                                                    <!--begin::Item-->
                                                    @foreach($currencies as $currency)
                                                    <div class="d-flex align-items-sm-center mb-7">
                                                        <!--begin::Symbol-->
                                                        <div class="symbol symbol-50px me-5">
                                                            <span class="symbol-label">
                                                                <img src="assets/media/svg/brand-logos/telegram.svg" class="h-50 align-self-center" alt="" />
                                                            </span>
                                                        </div>
                                                        <!--end::Symbol-->
                                                        <!--begin::Section-->
                                                        <div class="d-flex align-items-center flex-row-fluid flex-wrap">
                                                            <div class="flex-grow-1 me-2">
                                                                @if($main_account || $permissions['settings_edit_currencies'])
                                                                    <a href="{{url("settings/currencies/edit/{$currency['id']}")}}" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$currency['name']}}</a>
                                                                @else
                                                                    <a  class="text-gray-800 text-hover-primary fs-6 fw-bold">{{$currency['name']}}</a>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <!--end::Section-->
                                                    </div>
                                                    @endforeach
                                                    <!--end::Item-->
                                                </div>
                                            </div>
                                            @endif
                                        </div>
                                    </div>

                                    <!--begin::Header-->
                                    <!--end::Header-->
                                    <!--begin::Body-->
                                    <!--end::Body-->
                                </div>
                                <!--end::List Widget 1-->
                        </form>
                        </div>
                    </div>
                    <!--end::Row-->
                </div>
                <!--end::Content container-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Content wrapper-->
        <!--begin::Footer-->
        @include('theme::marble.footer')
        <!--end::Footer-->
    </div>

@endsection
