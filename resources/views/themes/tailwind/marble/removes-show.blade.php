@extends('theme::marble.layout')

@section('content')

					<!--begin::Main-->
					<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
						<!--begin::Content wrapper-->
						<div class="d-flex flex-column flex-column-fluid">
							<!--begin::Toolbar-->
							<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
								<!--begin::Toolbar container-->
								<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
									<!--begin::Page title-->
									<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
										<!--begin::Title-->
										<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.remove_order_details')}}</h1>
										<!--end::Title-->
										<!--begin::Breadcrumb-->
										<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.remove_list_path')}}</a>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">{{__('messages.remove_order_details')}}</li>
											<!--end::Item-->
										</ul>
										<!--end::Breadcrumb-->
									</div>
									<!--end::Page title-->
								</div>
								<!--end::Toolbar container-->
							</div>
							<!--end::Toolbar-->
							<!--begin::Content-->
							<div id="kt_app_content" class="app-content flex-column-fluid">
								<!--begin::Content container-->
								<div id="kt_app_content_container" class="app-container container-xxl">
									<!--begin::Order details page-->
									<div class="d-flex flex-column gap-7 gap-lg-10">
										<!--begin::Order summary-->
										<div class="d-flex flex-column col-xl-6 gap-7 gap-lg-10">
											<!--begin::Order details-->
											<div class="card card-flush py-4 flex-row-fluid">
												<!--begin::Card header-->
												<div class="card-header">
													<div class="card-title">
														<h2>{{__('messages.remove_order')}} ({{$invoice->pretty_id}})</h2>
													</div>
												</div>
												<!--end::Card header-->
												<!--begin::Card body-->
												<div class="card-body pt-0">
													<div class="table-responsive">
														<!--begin::Table-->
														<table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
															<tbody class="fw-semibold text-gray-600">
																@if($invoice->invoice_date)
																	<tr>
																		<td class="text-muted">
																			<div class="d-flex align-items-center">
																			<i class="ki-duotone ki-calendar fs-2 me-2">
																				<span class="path1"></span>
																				<span class="path2"></span>
																			</i>{{__('messages.invoice_date')}}</div>
																		</td>
																		<td class="fw-bold text-start">{{$invoice->invoice_date}}</td>
																	</tr>
																@endif
																<tr>
																	<td class="text-muted">
																		<div class="d-flex align-items-center">
																		<i class="ki-duotone ki-wallet fs-2 me-2">
																			<span class="path1"></span>
																			<span class="path2"></span>
																			<span class="path3"></span>
																			<span class="path4"></span>
																		</i>{{__('messages.date_created')}}</div>
																	</td>
																	<td class="fw-bold text-start">{{$invoice->created_at}}</td>
																</tr>
																<tr>
																	<td class="text-muted">
																		<div class="d-flex align-items-center">
																		<i class="ki-duotone ki-home fs-2 me-2">
																		</i>{{__('messages.thebranch')}}</div>
																	</td>
																			<td class="fw-bold text-start">{{$invoice->branch->branch_name}}</td>
																</tr>

																<tr>
																	<td class="text-muted">
																		<div class="d-flex align-items-center">
																		<i class="ki-duotone ki-truck fs-2 me-2">
																			<span class="path1"></span>
																			<span class="path2"></span>
																			<span class="path3"></span>
																			<span class="path4"></span>
																			<span class="path5"></span>
																		</i>{{__('messages.quantity_before_remove')}}</div>
																	</td>
																	<td class="fw-bold text-start">{{$sumBefore = $invoice->products->sum('quantity_before') - $invoice->products->where('is_composite', true)->sum('quantity_before')}}</td>
																</tr>
																<tr>
																	<td class="text-muted">
																		<div class="d-flex align-items-center">
																		<i class="ki-duotone ki-truck fs-2 me-2">
																			<span class="path1"></span>
																			<span class="path2"></span>
																			<span class="path3"></span>
																			<span class="path4"></span>
																			<span class="path5"></span>
																		</i>{{__('messages.quantity_after_remove')}}</div>
																	</td>
																	<td class="fw-bold text-start">{{$sumAfter = $invoice->products->sum('quantity_after') - $invoice->products->where('is_composite', true)->sum('quantity_after')}}</td>
																</tr>
																@if($invoice->products->where('is_composite', true)->count() > 0)
																<tr>
																	<td class="text-muted">
																		<div class="d-flex align-items-center">
																		<i class="ki-duotone ki-package fs-2 me-2">
																			<span class="path1"></span>
																			<span class="path2"></span>
																			<span class="path3"></span>
																		</i>{{__('messages.quantity_before_remove')}} - {{__('messages.composite_product')}}</div>
																	</td>
																	<td class="fw-bold text-start">{{$compositeBeforeSum = $invoice->products->where('is_composite', true)->sum('quantity_before')}}</td>
																</tr>
																<tr>
																	<td class="text-muted">
																		<div class="d-flex align-items-center">
																		<i class="ki-duotone ki-package fs-2 me-2">
																			<span class="path1"></span>
																			<span class="path2"></span>
																			<span class="path3"></span>
																		</i>{{__('messages.quantity_after_remove')}} - {{__('messages.composite_product')}}</div>
																	</td>
																	<td class="fw-bold text-start">{{$compositeAfterSum = $invoice->products->where('is_composite', true)->sum('quantity_after')}}</td>
																</tr>
																@endif
															</tbody>
														</table>
														<!--end::Table-->
													</div>
												</div>
												<!--end::Card body-->
											</div>
											<!--end::Order details-->
										</div>
										<!--end::Order summary-->
										<!--begin::Tab content-->
										<div class="tab-content">
											<!--begin::Tab pane-->
											<div class="tab-pane fade show active" id="kt_ecommerce_sales_order_summary" role="tab-panel">
												<!--begin::Orders-->
												<div class="d-flex flex-column gap-7 gap-lg-10">
													<!--begin::Product List-->
													<div class="card card-flush py-4 flex-row-fluid overflow-hidden">
														<!--begin::Card header-->
														<div class="card-header">
															<div class="card-title">
																<h2>{{__('messages.invoice_id')}}: {{$invoice->pretty_id}} </h2>
															</div>
														</div>
														<!--end::Card header-->
														<!--begin::Card body-->
														<div class="card-body pt-0">
															<div class="table-responsive">
																<!--begin::Table-->
																<table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
																	<thead>
																		<tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
																			<th class="min-w-175px">{{__('messages.product')}}</th>
																			<th class="min-w-100px text-end">{{__('messages.barcode')}}</th>
																			<th class="min-w-70px text-end">{{__('messages.quantity_before_remove')}}</th>
																			<th class="min-w-70px text-end">{{__('messages.quantity_after_remove')}}</th>
																		</tr>
																	</thead>
																	<tbody class="fw-semibold text-gray-600">
																		@foreach($invoice->products as $product)
																			@if(!$product->is_component)
																			<tr>
																				<td>
																					<div class="d-flex align-items-center">
																						<!--begin::Thumbnail-->
																						<a href="{{url("products/edit/")}}" class="symbol symbol-50px">
																							<span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$product->product['thumbnail']}});"></span>
																						</a>
																						<!--end::Thumbnail-->
																						<!--begin::Title-->
																						<div class="ms-5">
																							<a href="{{url("products/edit/{$product->product->id}")}}" class="fw-bold text-gray-600 text-hover-primary">{{$product->product->name}}</a>
																						</div>
																						<!--end::Title-->
																					</div>
																				</td>
																				<td class="text-end">{{$product->product->barcode}}</td>
																				<td class="text-end">{{$product['quantity_before']}}</td>
																				<td class="text-end">{{$product['quantity_after']}}</td>
																			</tr>
																			@if($product->is_composite)
																				@foreach($invoice->products as $component)
																					@if($component->is_component && $component->parent_product_id === $product->product_id)
																						<tr class="table-light">
																							<td>
																								<div class="d-flex align-items-center ps-8">
																									<!--begin::Thumbnail-->
																									<a href="{{url("products/edit/")}}" class="symbol symbol-40px">
																										<span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$component->product['thumbnail']}});"></span>
																									</a>
																									<!--end::Thumbnail-->
																									<!--begin::Title-->
																									<div class="ms-5">
																										<span class="text-gray-600">
																											<i class="ki-duotone ki-arrow-right fs-7 me-1"></i>
																											{{$component->product->name}}
																										</span>
																									</div>
																									<!--end::Title-->
																								</div>
																							</td>
																							<td class="text-end text-gray-600">{{$component->product->barcode}}</td>
																							<td class="text-end text-gray-600">{{$component['quantity_before']}}</td>
																							<td class="text-end text-gray-600">{{$component['quantity_after']}}</td>
																						</tr>
																					@endif
																				@endforeach
																			@endif
																			@endif
																		@endforeach

																		<tr>
																			<td colspan="2" class="fs-3 text-dark text-end">{{__('messages.grand_total')}}</td>
																			<td class="text-dark fs-3 fw-bolder text-end">{{$sumBefore}}</td>
																			<td class="text-dark fs-3 fw-bolder text-end">{{$sumAfter}}</td>
																		</tr>
																	</tbody>
																</table>
																<!--end::Table-->
															</div>
														</div>
														<!--end::Card body-->
													</div>
													<!--end::Product List-->
												</div>
												<!--end::Orders-->
											</div>
											<!--end::Tab pane-->
										</div>
										<!--end::Tab content-->
									</div>
									<!--end::Order details page-->
								</div>
								<!--end::Content container-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Content wrapper-->
						<!--begin::Footer-->
						@include('theme::marble.footer')
						<!--end::Footer-->
					</div>
					<!--end:::Main-->

@endsection