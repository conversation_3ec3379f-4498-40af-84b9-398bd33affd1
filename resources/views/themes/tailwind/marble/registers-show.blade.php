@extends('theme::marble.layout')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
@if (session('success'))
    <div class="alert alert-success">
        <ul>
                <li>{{ session('success') }}</li>
        </ul>
    </div>
@endif
					<!--begin::Main-->
					<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
						<!--begin::Content wrapper-->
						<div class="d-flex flex-column flex-column-fluid">
							<!--begin::Content-->
							<!--begin::Toolbar-->
							<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
								<!--begin::Toolbar container-->
								<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
									<!--begin::Page title-->
									<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
										<!--begin::Title-->
										<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.branches_registeries')}}</h1>
										<!--end::Title-->
										<!--begin::Breadcrumb-->
										<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
											</li>
											<!--end::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>

											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('settings/branches')}}" class="text-muted text-hover-primary">{{__('messages.branches_registeries')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">{{__('messages.show_registers')}}</li>
											<!--end::Item-->
										</ul>
										<!--end::Breadcrumb-->
									</div>
									<!--end::Page title-->
								</div>
								<!--end::Toolbar container-->
							</div>
							<!--end::Toolbar-->
							<div id="kt_app_content" class="app-content flex-column-fluid">
								<!--begin::Content container-->
								<div id="kt_app_content_container" class="app-container container-xxl">
									<!--begin::Followers toolbar-->
									<div class="d-flex flex-wrap flex-stack mb-6">
										<!--begin::Title-->
										<h3 class="text-gray-800 fw-bold my-2">{{__('messages.show_registers')}}
										<span class="fs-6 text-gray-400 fw-semibold ms-1">({{$registers->count()}})</span></h3>
										<!--end::Title-->
									</div>
									<!--end::Followers toolbar-->
									<!--begin::Row-->
									<div class="row g-6 mb-6 g-xl-9 mb-xl-9">
										<!--begin::Followers-->
										<!--begin::Col-->
                                        @foreach ($registers as $register )
                                        <div class="col-md-6 col-xxl-4">
											<!--begin::Card-->
											<div class="card">
												<!--begin::Card body-->
												<div class="card-body d-flex flex-center flex-column py-9 px-5">
													<!--begin::Avatar-->
													<a href="{{url("settings/branches/{$register['branch_id']}/registers/{$register['id']}")}}">
														<div class="symbol symbol-65px symbol-circle mb-5">
															<span class="symbol-label fs-2x fw-semibold text-warning bg-light-warning">{{$register['name']}}</span>
														</div>
													</a>
													<!--end::Avatar-->
													<!--begin::Name-->
													<a href="{{url("settings/branches/{$register['branch_id']}/registers/{$register['id']}")}}" class="fs-4 text-gray-800 text-hover-primary fw-bold mb-0">{{$register['name']}}</a>
													<!--end::Name-->
													<!--begin::Position-->
													<div class="fw-semibold text-gray-400 mb-6"></div>
													<!--end::Position-->
													<!--begin::Info-->
													<div class="d-flex flex-center flex-wrap mb-5">
														<!--begin::Stats-->
														<div class="border border-dashed rounded min-w-90px py-3 px-4 mx-2 mb-3">
															<div class="fs-6 fw-bold text-gray-700 text-center">
															<a href="{{url("settings/branches/{$register['branch_id']}/registers/{$register['id']}")}}" class="fw-bold text-gray-400 text-center">تعديل</a>
															</div>
														</div>
														<!--end::Stats-->
														<!--begin::Stats-->
													</div>
													<!--end::Info-->
												</div>
												<!--begin::Card body-->
											</div>
											<!--begin::Card-->
										</div>

                                        @endforeach

										<!--end::Col-->
										<!--end::Followers-->
									</div>
									<!--end::Row-->
								</div>
								<!--end::Content container-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Content wrapper-->
						<!--begin::Footer-->
						@include('theme::marble.footer')
						<!--end::Footer-->
					</div>
					<!--end:::Main-->
                    		<!--begin::Modal - Create App-->
		<div class="modal fade" id="kt_modal_create_app" tabindex="-1" aria-hidden="true">
			<!--begin::Modal dialog-->
			<div class="modal-dialog modal-dialog-centered mw-900px">
				<!--begin::Modal content-->
				<div class="modal-content">
					<!--begin::Modal header-->
					<div class="modal-header">
						<!--begin::Modal title-->
						<h2>{{__('messages.add_branch')}}</h2>
						<!--end::Modal title-->
						<!--begin::Close-->
						<div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
							<i class="ki-duotone ki-cross fs-1">
								<span class="path1"></span>
								<span class="path2"></span>
							</i>
						</div>
						<!--end::Close-->
					</div>
					<!--end::Modal header-->
					<!--begin::Modal body-->
					<div class="modal-body py-lg-10 px-lg-10">
						<!--begin::Stepper-->
						<div class="stepper stepper-pills stepper-column d-flex flex-column flex-xl-row flex-row-fluid" id="kt_modal_create_app_stepper">
							<!--begin::Aside-->
							<div class="d-flex justify-content-center justify-content-xl-start flex-row-auto w-100 w-xl-300px">
								<!--begin::Nav-->
								<div class="stepper-nav ps-lg-10">
									<!--begin::Step 1-->
									<div class="stepper-item current" data-kt-stepper-element="nav">
										<!--begin::Wrapper-->
										<div class="stepper-wrapper">
											<!--begin::Icon-->
											<div class="stepper-icon w-40px h-40px">
												<i class="ki-duotone ki-check stepper-check fs-2"></i>
												<span class="stepper-number">1</span>
											</div>
											<!--end::Icon-->
											<!--begin::Label-->
											<div class="stepper-label">
												<h3 class="stepper-title">{{__('messages.branch_details')}}</h3>
												<div class="stepper-desc">{{__('messages.branch_details_desc')}}</div>
											</div>
											<!--end::Label-->
										</div>
										<!--end::Wrapper-->
										<!--begin::Line-->
										<div class="stepper-line h-40px"></div>
										<!--end::Line-->
									</div>
									<!--end::Step 1-->
									<!--begin::Step 2-->
									<div class="stepper-item" data-kt-stepper-element="nav">
										<!--begin::Wrapper-->
										<div class="stepper-wrapper">
											<!--begin::Icon-->
											<div class="stepper-icon w-40px h-40px">
												<i class="ki-duotone ki-check stepper-check fs-2"></i>
												<span class="stepper-number">2</span>
											</div>
											<!--begin::Icon-->
											<!--begin::Label-->
											<div class="stepper-label">
												<h3 class="stepper-title">{{__('messages.register_details')}}</h3>
												<div class="stepper-desc">{{__('messages.register_details_desc')}}</div>
											</div>
											<!--begin::Label-->
										</div>
										<!--end::Wrapper-->
									</div>
									<!--end::Step 2-->
								</div>
								<!--end::Nav-->
							</div>
							<!--begin::Aside-->
							<!--begin::Content-->
							<div class="flex-row-fluid py-lg-5 px-lg-15">
								<!--begin::Form-->
								<form class="form" action="{{url('settings/branches/store')}}" method="post" novalidate="novalidate" id="kt_modal_create_app_form">
									<!--begin::Step 1-->
									<div class="current" data-kt-stepper-element="content">
										<div class="w-100">
											<!--begin::Input group-->
											<div class="fv-row mb-10">
												<!--begin::Label-->
												<label class="d-flex align-items-center fs-5 fw-semibold mb-2">
													<span class="required">{{__('messages.branch_name')}}</span>
													<span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.branch_name_tootltip')}}">
														<i class="ki-duotone ki-information-5 text-gray-500 fs-6">
															<span class="path1"></span>
															<span class="path2"></span>
															<span class="path3"></span>
														</i>
													</span>
												</label>
												<!--end::Label-->
												<!--begin::Input-->
												<input type="text" class="form-control form-control-lg form-control-solid" name="branch_name" placeholder="" value="" />
												<!--end::Input-->
											</div>
											<!--end::Input group-->
											<!--begin::Input group-->
											<div class="fv-row mb-10">
												<!--begin::Label-->
												<label class="d-flex align-items-center fs-5 fw-semibold mb-2">
													<span class="required">{{__('messages.branch_address')}}</span>
													<span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.branch_address_tooltip')}}">
														<i class="ki-duotone ki-information-5 text-gray-500 fs-6">
															<span class="path1"></span>
															<span class="path2"></span>
															<span class="path3"></span>
														</i>
													</span>
												</label>
												<!--end::Label-->
												<!--begin::Input-->
												<input type="text" class="form-control form-control-lg form-control-solid" name="branch_address" placeholder="" value="" />
												<!--end::Input-->
											</div>
											<!--end::Input group-->
										</div>
									</div>
									<!--end::Step 1-->
									<!--begin::Step 2-->
									<div data-kt-stepper-element="content">
										<div class="w-100">
                                            <div data-repeater-list='repeater'>
                                                <div data-repeater-item class="fv-row mb-10">
                                                    <!--begin::Label-->
                                                    <label class="d-flex align-items-center fs-5 fw-semibold mb-2">
                                                        <span class="required">{{__('messages.register_name')}}</span>
                                                    </label>
                                                    <!--end::Label-->
                                                    <!--begin::Input-->
                                                    <input type="text" class="form-control form-control-lg form-control-solid" name="register_name" placeholder="" value="" />
                                                    <!--end::Input-->
                                                    <a  data-repeater-delete="" class="btn btn-sm btn-flex flex-center btn-light-danger mt-3 mt-md-9">
                                                        <i class="ki-duotone ki-trash fs-5"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span></i> {{__('messages.delete')}}
                                                    </a>
                                                </div>
                                            </div>
                                            <div class="fv-row mb-10">
                                                <a href="javascript:;" data-repeater-create="" class="btn btn-flex flex-center btn-light-primary">
                                                    <i class="ki-duotone ki-plus fs-3"></i> {{__('messages.add_register')}}
                                                </a>

                                            </div>
										</div>
									</div>

									<!--end::Step 2-->
									<!--begin::Actions-->
									<div class="d-flex flex-stack pt-10">
										<!--begin::Wrapper-->
										<div class="me-2">
											<button type="button" class="btn btn-lg btn-light-primary me-3" data-kt-stepper-action="previous">
											<i class="ki-duotone ki-arrow-right fs-3 me-1">
												<span class="path1"></span>
												<span class="path2"></span>
											</i>{{__('messages.back')}}</button>
										</div>
										<!--end::Wrapper-->
										<!--begin::Wrapper-->
										<div>
											<button type="submit" class="btn btn-lg btn-primary" data-kt-stepper-action="submit">
												<span class="indicator-label">{{__('messages.add_branch')}}
                                            </span>
												<span class="indicator-progress">Please wait...
												<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
											</button>
											<button type="button" class="btn btn-lg btn-primary" data-kt-stepper-action="next">{{__('messages.next')}}
											<i class="ki-duotone ki-arrow-left fs-3 ms-1 me-0">
												<span class="path1"></span>
												<span class="path2"></span>
											</i></button>
										</div>
										<!--end::Wrapper-->
									</div>
									<!--end::Actions-->
                                    @csrf
								</form>
								<!--end::Form-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Stepper-->
					</div>
					<!--end::Modal body-->
				</div>
				<!--end::Modal content-->
			</div>
			<!--end::Modal dialog-->
		</div>
		<!--end::Modal - Create App-->

        <script>
            document.addEventListener("DOMContentLoaded", function() {
                $('.form').repeater({
                    initEmpty: false,

                    defaultValues: {
                        'text-input': 'foo'
                    },

                    show: function () {
                        $(this).slideDown();
                    },

                    hide: function (deleteElement) {
                        $(this).slideUp(deleteElement);
                    },

                    isFirstItemUndeletable: true

                });


            });

        </script>

@endsection
