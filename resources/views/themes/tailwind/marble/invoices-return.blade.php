@extends('theme::marble.layout')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
					<!--begin::Main-->
					<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
						<!--begin::Content wrapper-->
						<div class="d-flex flex-column flex-column-fluid">
							<!--begin::Toolbar-->
							<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
								<!--begin::Toolbar container-->
								<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
									<!--begin::Page title-->
									<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
										<!--begin::Title-->
										<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.invoice_return')}}</h1>
										<!--end::Title-->
										<!--begin::Breadcrumb-->
										<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('invoices')}}" class="text-muted text-hover-primary">{{__('messages.invoices_list')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">{{__('messages.invoice_return')}}</li>
											<!--end::Item-->
										</ul>
										<!--end::Breadcrumb-->
									</div>
									<!--end::Page title-->
									<!--begin::Actions-->
									<div class="d-flex align-items-center gap-2 gap-lg-3">
										<!--begin::Secondary button-->
										<!--end::Secondary button-->
									</div>
									<!--end::Actions-->
								</div>
								<!--end::Toolbar container-->
							</div>
							<!--end::Toolbar-->
							<!--begin::Content-->
							<div id="kt_app_content" class="app-content flex-column-fluid">
								<!--begin::Content container-->
								<div id="kt_app_content_container" class="app-container container-xxl">
									@if($remainingAmount > 0)
									<div class="alert alert-danger">
										<ul>
												<li>{{__('messages.invoice_remaining_amount_message')}}</li>
												<li> {{__('messages.remaining_amount')}} {{$remainingAmount}} SAR</li>
												<li>
													{{__('messages.get_amount_message')}}
													<a href="{{url("invoices/receive/{$invoice['id']}")}}">{{__('messages.here')}}</a>
												</li>
										</ul>
									</div>

									@endif
									<!--begin::Order details page-->
									<div class="d-flex flex-column gap-7 gap-lg-10">
										<div class="d-flex flex-wrap flex-stack gap-5 gap-lg-10">
											<!--begin:::Tabs-->
											<ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-lg-n2 me-auto">
												<!--begin:::Tab item-->
												<li class="nav-item">
													<a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab" href="#kt_ecommerce_sales_order_summary">{{__('messages.order_summary')}}</a>
												</li>
												<!--end:::Tab item-->
											</ul>
											<!--end:::Tabs-->
										</div>
										<!--begin::Tab content-->
										<div class="tab-content">
											<!--begin::Tab pane-->
											<div class="tab-pane fade show active" id="kt_ecommerce_sales_order_summary" role="tab-panel">
												<!--begin::Orders-->
												<div class="d-flex flex-column gap-7 gap-lg-10">
													<!--begin::Product List-->
                                                    <form  method="POST"  action="{{url("invoices/return/{$invoice['id']}/store")}}">
                                                        @if (!empty($backto))

                                                            <input type="hidden" name="backto" value="{{$backto}}" />
                                                            <input type="hidden" name="branchId" value="{{$_GET['branchId']}}" />
                                                            <input type="hidden" name="registeryId" value="{{$_GET['registeryId']}}" />

                                                        @endif
                                                        @csrf
                                                        <div class="card card-flush py-4 flex-row-fluid overflow-hidden">
                                                            <!--begin::Card header-->
                                                            <div class="card-header">
                                                                <div class="card-title">
                                                                    <h2>{{__('messages.invoice_id')}} {{$invoice->pretty_id}}</h2>
                                                                </div>

                                                            </div>
															{{-- Removed warning: Now allowing partial returns for discounted invoices --}}
                                                            <!--end::Card header-->
                                                            <!--begin::Card body-->
                                                            <div class="card-body pt-0">
                                                                <div class="table-responsive">
                                                                    <!--begin::Table-->
                                                                    <table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
                                                                        <thead>
                                                                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                                                <th class="min-w-175px">{{__('messages.product')}}</th>
                                                                                <th class="min-w-100px text-end">{{__('messages.barcode')}}</th>
                                                                                <th class="min-w-70px text-end">{{__('messages.qty')}}</th>
                                                                                <th class="min-w-70px text-center">{{__('messages.returned_qty')}}</th>
                                                                                <th class="min-w-100px text-end">{{__('messages.unit_price')}}</th>
                                                                                <th class="min-w-100px text-end">{{__('messages.total')}}</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody class="fw-semibold text-gray-600">
                                                                            {{-- Display Promotion Groups First --}}
                                                                            @if($hasPromotionGroups)
                                                                                @foreach($promotionGroups as $group)
                                                                                    @if($group['returnable'])
                                                                                        {{-- Promotion Group Header --}}
                                                                                        <tr class="bg-light-primary">
                                                                                            <td colspan="6" class="text-center fw-bold text-primary">
                                                                                                <i class="ki-duotone ki-gift fs-2 me-2">
                                                                                                    <span class="path1"></span>
                                                                                                    <span class="path2"></span>
                                                                                                    <span class="path3"></span>
                                                                                                </i>
                                                                                                مجموعة عرض: {{ $group['group_info']['description'] ?? 'عرض ترويجي' }}
                                                                                                <small class="text-muted d-block">يجب إرجاع المجموعة كاملة معاً</small>
                                                                                            </td>
                                                                                        </tr>

                                                                                        {{-- Group Products --}}
                                                                                        @foreach($group['products'] as $product)
																							@if($product['invoice_type'] === \App\Enums\Application\InvoiceTypeEnum::SELL->value)
                                                                                            <tr class="promotion-group-product">
                                                                                                <td>
                                                                                                    <div class="d-flex align-items-center">
                                                                                                        <!--begin::Thumbnail-->
                                                                                                        <a href="{{url("products/edit/$product->product_id")}}" class="symbol symbol-50px">
                                                                                                            <span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$product->product_thumbnail}});"></span>
                                                                                                        </a>
                                                                                                        <!--end::Thumbnail-->
                                                                                                        <!--begin::Title-->
                                                                                                        <div class="ms-5">
                                                                                                            <a href="{{url("products/edit/$product->product_id")}}" class="fw-bold text-gray-600 text-hover-primary">{{$product->product_name}}</a>
                                                                                                            @if($product->promotion_product_type === 'buy_x')
                                                                                                                <span class="badge badge-light-success ms-2">منتج مشترى</span>
                                                                                                            @elseif($product->promotion_product_type === 'get_y')
                                                                                                                <span class="badge badge-light-warning ms-2">منتج مجاني</span>
                                                                                                            @endif
                                                                                                        </div>
                                                                                                        <!--end::Title-->
                                                                                                    </div>
                                                                                                </td>
                                                                                                <td class="text-end">{{$product->product_barcode}}</td>
                                                                                                <td class="text-end">{{$product->remaining_qty ?? $product->cart_quantity}}</td>
                                                                                                @php
                                                                                                    // Construct cart_id based on product properties since it's not stored in DB:
                                                                                                    // 1. Regular product, no promotion: product_id
                                                                                                    // 2. Variant product, no promotion: product_id-variant_id
                                                                                                    // 3. Regular product, with promotion: product_id-promotion_id
                                                                                                    // 4. Variant product, with promotion: product_id-variant_id-promotion_id

                                                                                                    $productKey = $product->product_id; // Start with product_id

                                                                                                    // Add variant_id if it's a variant product
                                                                                                    if (isset($product->is_variant) && $product->is_variant && $product->product_variant_id) {
                                                                                                        $productKey .= '-' . $product->product_variant_id;
                                                                                                    }

                                                                                                    // Add promotion_id if it has promotion
                                                                                                    if (isset($product->has_promotion) && $product->has_promotion && $product->promotion_id) {
                                                                                                        $productKey .= '-' . $product->promotion_id;
                                                                                                    }
                                                                                                @endphp
                                                                                                <td class="text-end" style="display:flex;align-items:center;justify-content:center">
                                                                                                    {{-- Promotion group products use group-based quantity selection --}}
                                                                                                    <div class="input-group w-md-50 promotion-group-input"
                                                                                                    data-group-id="{{$group['group_id']}}"
                                                                                                    data-product-type="{{$product->promotion_product_type}}"
                                                                                                    data-kt-dialer="true"
                                                                                                    data-kt-dialer-min="0"
                                                                                                    data-kt-dialer-max="{{$product->remaining_qty ?? $product->cart_quantity}}"
                                                                                                    data-kt-dialer-step="1"
                                                                                                    data-kt-dialer-prefix="">

                                                                                                    <!--begin::Decrease control-->
                                                                                                    <button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="decrease">
                                                                                                        <i class="ki-duotone ki-minus fs-2"></i>
                                                                                                    </button>
                                                                                                    <!--end::Decrease control-->

                                                                                                    <!--begin::Input control-->
                                                                                                    <input type="text" class="form-control text-center quantities promotion-quantity" placeholder="Quantity" name="quantity[{{$productKey}}]" value="0" data-kt-dialer-control="input"/>
                                                                                                    <!--end::Input control-->

                                                                                                    <!--begin::Increase control-->
                                                                                                    <button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="increase">
                                                                                                        <i class="ki-duotone ki-plus fs-2"></i>
                                                                                                    </button>
                                                                                                    <!--end::Increase control-->
                                                                                                    </div>
                                                                                                </td>
                                                                                                <td class="text-end">SR{{$product->price_tax_inclusive}}</td>
                                                                                                <td class="text-end">SR{{$product->price_tax_inclusive * $product->cart_quantity}}</td>
                                                                                            </tr>
																							@endif
                                                                                        @endforeach

                                                                                        {{-- Group Separator --}}
                                                                                        <tr>
                                                                                            <td colspan="6" class="border-bottom border-2 border-primary"></td>
                                                                                        </tr>
                                                                                    @endif
                                                                                @endforeach
                                                                            @endif

                                                                            {{-- Display Regular Products --}}
                                                                            @foreach($regularProducts as $product)
																			@if($product['invoice_type'] === \App\Enums\Application\InvoiceTypeEnum::SELL->value)
                                                                            <tr>
                                                                                <td>
                                                                                    <div class="d-flex align-items-center">
                                                                                        <!--begin::Thumbnail-->
                                                                                        <a href="{{url("products/edit/$product->product_id")}}" class="symbol symbol-50px">
                                                                                            <span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$product->product_thumbnail}});"></span>
                                                                                        </a>
                                                                                        <!--end::Thumbnail-->
                                                                                        <!--begin::Title-->
                                                                                        <div class="ms-5">
                                                                                            <a href="{{url("products/edit/$product->product_id")}}" class="fw-bold text-gray-600 text-hover-primary">{{$product->product_name}}</a>
                                                                                        </div>
                                                                                        <!--end::Title-->
                                                                                    </div>
                                                                                </td>
                                                                                <td class="text-end">{{$product->product_barcode}}</td>
																				@if($product->returnable)
																					@php
																						// Construct cart_id based on product properties since it's not stored in DB:
																						// 1. Regular product, no promotion: product_id
																						// 2. Variant product, no promotion: product_id-variant_id
																						// 3. Regular product, with promotion: product_id-promotion_id
																						// 4. Variant product, with promotion: product_id-variant_id-promotion_id

																						$productKey = $product->product_id; // Start with product_id

																						// Add variant_id if it's a variant product
																						if (isset($product->is_variant) && $product->is_variant && $product->product_variant_id) {
																							$productKey .= '-' . $product->product_variant_id;
																						}

																						// Add promotion_id if it has promotion
																						if (isset($product->has_promotion) && $product->has_promotion && $product->promotion_id) {
																							$productKey .= '-' . $product->promotion_id;
																						}
																					@endphp
																					<td class="text-end">{{$sameProducts[$productKey] ?? $product->cart_quantity}}</td>
																				@else
																					<td class="text-end">{{$product['cart_quantity']}}</td>
																				@endif
																				{{-- Check if product is part of variant - disable return for variant products --}}
																				@if($product->returnable && !($product->is_variant ?? false))
																					<td class="text-end" style="display:flex;align-items:center;justify-content:center">
																						<!--begin::Dialer-->
																						<div class="input-group w-md-50 "
																						data-kt-dialer="true"
																						data-kt-dialer-min="0"
																						data-kt-dialer-max="{{$sameProducts[$productKey] ?? $product->cart_quantity}}"
																						data-kt-dialer-step="1"
																						data-kt-dialer-prefix="">

																						<!--begin::Decrease control-->
																						<button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="decrease">
																							<i class="ki-duotone ki-minus fs-2"></i>
																						</button>
																						<!--end::Decrease control-->

																						<!--begin::Input control-->
																						<input type="text" class="form-control text-center quantities" placeholder="Quantity" name="quantity[{{$productKey}}]" value="0" data-kt-dialer-control="input"/>
																						<!--end::Input control-->

																						<!--begin::Increase control-->
																						<button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="increase">
																							<i class="ki-duotone ki-plus fs-2"></i>
																						</button>
																						<!--end::Increase control-->
																						</div>
																						<!--end::Dialer-->
																					</td>
																				@elseif($product->is_variant ?? false)
																					<td class="text-end" style="display:flex;align-items:center;justify-content:center">
																						@if($product->returnable)
																							<div class="input-group w-md-50"
																							data-kt-dialer="true"
																							data-kt-dialer-min="0"
																							data-kt-dialer-max="{{$product->cart_quantity}}"
																							data-kt-dialer-step="1"
																							data-kt-dialer-prefix="">

																							<!--begin::Decrease control-->
																							<button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="decrease">
																								<i class="ki-duotone ki-minus fs-2"></i>
																							</button>
																							<!--end::Decrease control-->

																							<!--begin::Input control-->
																							<input type="text" class="form-control text-center quantities" placeholder="Quantity" name="quantity[{{$productKey}}]" value="0" data-kt-dialer-control="input"/>
																							<!--end::Input control-->

																							<!--begin::Increase control-->
																							<button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="increase">
																								<i class="ki-duotone ki-plus fs-2"></i>
																							</button>
																							<!--end::Increase control-->
																							</div>
																						@else
																							<span class="text-danger">{{__('messages.product_cannot_be_returned')}}</span>
																							<input type="hidden" name="quantity[{{$productKey}}]" value="0"/>
																						@endif
																					</td>
																				@else
																						<td class="text-end" style="display:flex;align-items:center;justify-content:center" data-bs-toggle="tooltip" wire:ignore data-bs-placement="top" data-bs-custom-class="tooltip-inverse" title="المنتج غير قابل للارجاع">
																							<!--begin::Dialer-->
																							<div class="input-group w-md-50 "
																							data-kt-dialer="true"
																							data-kt-dialer-min="0"
																							data-kt-dialer-max="0"
																							data-kt-dialer-step="1"
																							data-kt-dialer-prefix="">

																							<!--begin::Decrease control-->
																							<button class="btn btn-icon btn-outline btn-active-color-primary" disabled type="button" data-kt-dialer-control="decrease">
																								<i class="ki-duotone ki-minus fs-2"></i>
																							</button>
																							<!--end::Decrease control-->

																							<!--begin::Input control-->
																							<input type="text" class="form-control text-center quantities" disabled  placeholder="Quantity" name="quantity[{{$productKey}}]" value="0" data-kt-dialer-control="input"/>
																							<!--end::Input control-->

																							<!--begin::Increase control-->
																							<button class="btn btn-icon btn-outline btn-active-color-primary" disabled type="button" data-kt-dialer-control="increase">
																								<i class="ki-duotone ki-plus fs-2"></i>
																							</button>
																							<!--end::Increase control-->
																							</div>
																							<!--end::Dialer-->
																						</td>
																				@endif
                                                                                <td class="text-end">SR{{$product->price_tax_inclusive}}</td>
                                                                                <td class="text-end">SR{{$product->price_tax_inclusive * $product->cart_quantity}}</td>
                                                                            </tr>
																			@endif
                                                                            @endforeach
																			<tr>
                                                                                <td colspan="1" class="text-start">طريقة الدفع:</td>
                                                                            </tr>

																			<tr class="pt-0">
																				<td>
																					@foreach($paymentMethods as $paymentMethod)
																						@if(!$paymentMethod['is_postpay'])
																						<div class="form-check form-check-custom form-check-solid {{!$loop->first?'pt-5':''}}">
																							<input class="form-check-input" name='payment' type="radio" value="{{$paymentMethod["id"]}}" {{$loop->first?'checked':''}} id="flexRadioDefault"/>
																							<label class="form-check-label text-pink" for="flexRadioDefault">
																								{{$paymentMethod["name"]}}
																							</label>
																						</div>
																						@endif
																					@endforeach
																				</td>
                                                                            </tr>
																			<tr>
                                                                                <td colspan="1" class="text-start">فرع الارجاع</td>
                                                                            </tr>

																			<tr class="pt-0">
																				<td>
																					@if(count($branches))
																						<!--begin::Select2-->
																						<select class="form-select mb-2" data-control="select2" data-hide-search="true" name="branch" data-placeholder="{{__('messages.select_option')}}">
																						@foreach($branches as $branch)
																									<option></option>
																									<option  {{ $branch['id'] == $invoice['branch_id']? 'selected' : '' }} value="{{$branch['id']}}">{{$branch['branch_name']}}</option>
																						@endforeach
																						</select>
																						<!--end::Select2-->
																					@endif
																				</td>
                                                                            </tr>

																			@if($invoice->is_discounted)

																			<tr>
																				<td colspan="4" class="text-end">{{__('messages.subtotal')}}</td>
																				<td class="text-end">SR{{\BCMathExtended\BC::add($invoice->total_tax_exclusive,$invoice->total_discount_tax_exclusive_rounded,10)}}</td>
																			</tr>

																			<tr>
																				<td colspan="4" class="text-end">{{__('messages.discount_amount')}}</td>
																				<td class="text-end">SR{{$invoice->total_discount_tax_exclusive_rounded}}</td>
																			</tr>
																			<tr>
																				<td colspan="4" class="text-end">{{__('messages.vat')}}</td>
																				<td class="text-end">SR{{$invoice->total_tax}}</td>
																			</tr>
																			@else

																			<tr>
																				<td colspan="4" class="text-end">{{__('messages.subtotal')}}</td>
																				<td class="text-end">SR{{$invoice->total_tax_exclusive}}</td>
																			</tr>

																			<tr>
																				<td colspan="4" class="text-end">{{__('messages.vat')}}</td>
																				<td class="text-end">SR{{$invoice->total_tax}}</td>
																			</tr>

																			@endif
																			<tr>
																				<td colspan="4" class="fs-3 text-dark text-end">{{__('messages.grand_total')}}</td>
																				<td class="text-dark fs-3 fw-bolder text-end">SR{{$invoice->total_tax_inclusive}}</td>
																			</tr>


																			<tr id='return_amount' hidden>
                                                                                <td colspan="4" class="text-end text-danger">{{__('messages.return_amount_tax_inclusive')}}</td>
                                                                                <td class="text-end text-danger" id='text_return_amount'></td>
                                                                            </tr>

                                                                        </tbody>
                                                                    </table>
																	<input type="submit" {{$remainingAmount > 0?'disabled':''}} {{$totalQuantity == 0?'disabled':''}} class="btn btn-danger btn-lg" value="{{__('messages.return')}}"/>

                                                                    <!--end::Table-->
                                                                </div>
                                                            </div>
                                                            <!--end::Card body-->
                                                        </div>
                                                    </form>
													<!--end::Product List-->
												</div>
												<!--end::Orders-->
											</div>
											<!--end::Tab pane-->
										</div>
										<!--end::Tab content-->
									</div>
									<!--end::Order details page-->
								</div>
								<!--end::Content container-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Content wrapper-->
						<!--begin::Footer-->
						@include('theme::marble.footer')
						<!--end::Footer-->
					</div>
					<!--end:::Main-->

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add visual feedback for promotion groups
    const promotionRows = document.querySelectorAll('.promotion-group-product');
    promotionRows.forEach(row => {
        row.style.borderLeft = '3px solid #007bff';
        row.style.backgroundColor = '#f8f9ff';
    });

    // Add promotion group validation feedback
    const promotionInputs = document.querySelectorAll('.promotion-group-input input');
    promotionInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Basic validation feedback - can be enhanced
            const groupContainer = this.closest('.promotion-group-input');
            const groupId = groupContainer.getAttribute('data-group-id');
            console.log(`Promotion group ${groupId} quantity changed`);
        });
    });
});
</script>
@endpush

@endsection
