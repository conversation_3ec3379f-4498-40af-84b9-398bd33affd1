@extends('theme::marble.layout')

@section('content')

@include('theme::marble.branch-sidebar')


<div id="kt_app_content" class="app-content flex-column-fluid">

	<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
		<!--begin::Toolbar container-->
		<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
			<!--begin::Page title-->
			<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
				<!--begin::Title-->
				<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.branches_registeries')}}</h1>
				<!--end::Title-->
				<!--begin::Breadcrumb-->
				<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
					<!--begin::Item-->
					<li class="breadcrumb-item text-muted">
						<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
					</li>
					<!--end::Item-->
					<li class="breadcrumb-item">
						<span class="bullet bg-gray-400 w-5px h-2px"></span>
					</li>

					<!--begin::Item-->
					<li class="breadcrumb-item text-muted">
						<a href="{{url('settings/branches')}}" class="text-muted text-hover-primary">{{__('messages.branches_registeries')}}</a>
					</li>
					<!--end::Item-->
					<!--begin::Item-->
					<li class="breadcrumb-item">
						<span class="bullet bg-gray-400 w-5px h-2px"></span>
					</li>
					<!--end::Item-->
					<!--begin::Item-->
					<li class="breadcrumb-item text-muted">{{__('messages.show_registers')}}</li>
					<!--end::Item-->
				</ul>
				<!--end::Breadcrumb-->
			</div>
			<!--end::Page title-->
		</div>
		<!--end::Toolbar container-->
	</div>
    <!--begin::Content container-->
    <div id="kt_app_content_container" class="app-container container-xxl">
		@if ($errors->any())
			<div class="alert alert-danger">
				<ul>
					@foreach ($errors->all() as $error)
						<li>{{ $error }}</li>
					@endforeach
				</ul>
			</div>
		@endif
		@if (session('success'))
			<div class="alert alert-success">
				<ul>
						<li>{{ session('success') }}</li>
				</ul>
			</div>
		@endif

        <!--begin::Card-->
        <div class="card card-flush">
            <!--begin::Card body-->
            <div class="card-body">
                <!--begin:::Tabs-->
                <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-semibold mb-15">
                    <!--begin:::Tab item-->
                    <li class="nav-item">
                        <a class="nav-link text-active-primary d-flex align-items-center pb-5 active" data-bs-toggle="tab" href="#kt_ecommerce_settings_general">
                        <i class="ki-duotone ki-home fs-2 me-2"></i>{{__('messages.main_settings')}}</a>
                    </li>
                    <!--end:::Tab item-->
                </ul>
                <!--end:::Tabs-->
                <!--begin:::Tab content-->
                <div class="tab-content" id="myTabContent">
                    <!--begin:::Tab pane-->
                    <div class="tab-pane fade show active" id="kt_ecommerce_settings_general" role="tabpanel">
                        <!--begin::Form-->
                        <form id="kt_ecommerce_settings_general_form" class="form" method='post' action="{{request()->url()}}/store">
                            @csrf
                            <!--begin::Heading-->
                            <div class="row mb-7">
                                <div class="col-md-9 offset-md-3">
                                    <h2>{{__('messages.register_setting')}} {{$currentRegister->name}}</h2>
                                </div>
                            </div>
                            <!--end::Heading-->
                            <!--begin::Input group-->
                            <div class="row fv-row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <!--begin::Label-->
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span>{{__('messages.sale_screen')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.sale_screen_desc')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <div class="col-md-9">
                                    <div class="w-100">
                                        <!--begin::Select2-->
                                        <select class="form-select form-select-solid" name="layout" data-control="select2" data-hide-search="true" data-placeholder="Select a layout">
                                            <option></option>
                                            <option value="sale_default" {{$merchantRegister->screen == 'sale_default' ? "selected='selected'":''}}>{{__('messages.sale_default')}}</option>
                                            <option value="sale_images" {{$merchantRegister->screen == 'sale_images' ? "selected='selected'":''}}>{{__('messages.sale_images')}}</option>
                                        </select>
                                        <!--end::Select2-->
                                    </div>
                                </div>
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="row fv-row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <!--begin::Label-->
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span>{{__('messages.register_open_and_close_enable')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.sale_screen_desc')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <div class="col-md-9">
                                    <div class="d-flex mt-3">
                                        <!--begin::Checkbox-->
                                        <label class="form-check form-check-sm form-check-custom form-check-solid me-5 me-lg-20">
                                            <input class="form-check-input settings_select_all" type="checkbox" value="1" {{$merchantRegister?->open_close_register?'checked':''}} name="open_close_register" id="open_close_register" />
                                            <span class="form-check-label">{{__('messages.enable')}}</span>
                                        </label>
                                        <!--end::Checkbox-->
                                    </div>
                                </div>
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="row fv-row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <!--begin::Label-->
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span>{{__('messages.register_max_open_hours')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.register_max_open_hours_desc')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <div class="col-md-9">
                                    <div class="d-flex mt-3">
                                        <!--begin::Input-->
                                        <input type="text" class="form-control form-control-solid" name="max_open_hours" value="{{$merchantRegister?->max_open_hours ?? 8}}" min="1" max="72" />
                                        <!--end::Input-->
                                    </div>
                                </div>
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="row fv-row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <!--begin::Label-->
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span>{{__('messages.choose_permitted_payment_methods')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.choose_permitted_payment_methods_desc')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                    <!--end::Label-->
                                </div>
                                <div class="col-md-9">
                                    <div class="d-flex mt-3">
                                        <!--begin::Checkbox-->
                                        <input class="form-control form-control-lg" name="prmittedPaymentMethods"
                                            value="@foreach($permittedPaymentMethods as $payment){{$payment->payment->name}},@endforeach"
                                            id="kt_tagify_1"/>
                                        <label class="form-check form-check-sm form-check-custom form-check-solid me-5 me-lg-20">
                                        </label>
                                        <!--end::Checkbox-->
                                    </div>
                                </div>
                            </div>
                            <!--end::Input group-->
                            <!--begin::Action buttons-->
                            <div class="row py-5">
                                <div class="col-md-9 offset-md-3">
                                    <div class="d-flex">
                                        <!--begin::Button-->
                                        <!--end::Button-->
                                        <!--begin::Button-->
                                        <button type="submit" data-kt-ecommerce-settings-type="submit" class="btn btn-primary">
                                            <span class="indicator-label">{{__('messages.save')}}</span>
                                            <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                        </button>
                                        <!--end::Button-->
                                    </div>
                                </div>
                            </div>
                            <!--end::Action buttons-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end:::Tab pane-->
                </div>
                <!--end:::Tab content-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content container-->
</div>
<!--end::Content-->
<script>

	document.addEventListener("DOMContentLoaded", function() {
		        // Keep your existing script
				var input1 = document.querySelector("#kt_tagify_1");
        var paymentMethods = {!! json_encode($paymentMethods) !!};
        var permittedPaymentMethods = {!! json_encode($permittedPaymentMethods) !!};
        // Initialize Tagify components on the above inputs
        var whitelist = [];
        var whitelist = paymentMethods.map(function(paymentMethod) {
            if(!paymentMethod.is_postpay){
                return { value: paymentMethod.name, code: paymentMethod.id };
            }
        })
        .filter(function(item) {
            return item !== undefined;
        });
        var permittedValue = [];
        var permittedValue = permittedPaymentMethods.map(function(permittedPaymentMethod) {
            return { value: permittedPaymentMethod.id, code: permittedPaymentMethod.id };

        })

        new Tagify(input1, {
            value: permittedValue.map(method => ({ value: method.value, code: method.code })),
            whitelist: whitelist,
            maxTags: 10,
            dropdown: {
                maxItems: 20,           // <- mixumum allowed rendered suggestions
                classname: "",          // <- custom classname for this dropdown, so it could be targeted
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
            }
        });
	});
</script>
@endsection
