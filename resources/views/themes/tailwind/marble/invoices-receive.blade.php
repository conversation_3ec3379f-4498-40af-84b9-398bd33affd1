@extends('theme::marble.layout')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

					<!--begin::Main-->
					<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
						<!--begin::Content wrapper-->
						<div class="d-flex flex-column flex-column-fluid">
							<!--begin::Toolbar-->
							<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
								<!--begin::Toolbar container-->
								<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
									<!--begin::Page title-->
									<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
										<!--begin::Title-->
										<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.order_details')}}</h1>
										<!--end::Title-->
										<!--begin::Breadcrumb-->
										<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('invoices')}}" class="text-muted text-hover-primary">{{__('messages.invoices_list')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">{{__('messages.invoice_details')}}</li>
											<!--end::Item-->
										</ul>
										<!--end::Breadcrumb-->
									</div>
									<!--end::Page title-->
									<!--begin::Actions-->
									<div class="d-flex align-items-center gap-2 gap-lg-3">
										<!--begin::Secondary button-->
										<!--end::Secondary button-->
									</div>
									<!--end::Actions-->
								</div>
								<!--end::Toolbar container-->
							</div>
							<!--end::Toolbar-->
							<!--begin::Content-->
							<div id="kt_app_content" class="app-content flex-column-fluid">
								<!--begin::Content container-->
								<div id="kt_app_content_container" class="app-container container-xxl">
                                    @if (session('success'))
                                    <div class="alert alert-success mb-5">
                                        <ul>
                                                <li>{{ session('success') }}</li>
                                        </ul>
                                    </div>
                                    @endif
									<!--begin::Order details page-->
									<div class="d-flex flex-column gap-7 gap-lg-10">
										<div class="d-flex flex-wrap flex-stack gap-5 gap-lg-10">
											<!--begin:::Tabs-->
											<ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-lg-n2 me-auto">
												<!--begin:::Tab item-->
												<li class="nav-item">
													<a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab" href="#kt_ecommerce_sales_order_summary">{{__('messages.order_summary')}}</a>
												</li>
												<!--end:::Tab item-->
											</ul>
											<!--end:::Tabs-->
											<!--begin::Button-->
											@if($invoice['invoice_type'] =='sell')
											<a href="{{url("invoices/return/{$invoice->id}")}}{{!empty($_GET['backto'])?"?backto=pos&branchId={$_GET['branchId']}&registeryId={$_GET['registeryId']}":''}}" class="btn btn-danger btn-lg me-lg-n7">{{__('messages.return')}}</a>
											@endif
											<!--end::Button-->
                                            <!--begin::Button-->
											<button id="print" class="btn btn-success btn-lg ">{{__('messages.print')}}</button>
											<!--end::Button-->

										</div>
										<!--begin::Order summary-->
                                        <div class="row">
                                            <div class="d-flex flex-column col-xl-6" style="padding-left: 0">
                                                <!--begin::Order details-->
                                                <div class="card card-flush py-4 flex-row-fluid">
                                                    <!--begin::Card header-->
                                                    <div class="card-header">
                                                        <div class="card-title">
                                                            <h2>{{__('messages.invoice_details')}} ({{$invoice->pretty_id}})</h2>
                                                        </div>
                                                    </div>
                                                    <!--end::Card header-->
                                                    <!--begin::Card body-->
                                                    <div class="card-body pt-0">
                                                        <div class="table-responsive">
                                                            <!--begin::Table-->
                                                            <table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
                                                                <tbody class="fw-semibold text-gray-600">
                                                                    <tr>
                                                                        <td class="text-muted">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone ki-calendar fs-2 me-2">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                            </i>{{__('messages.date_created')}}</div>
                                                                        </td>
                                                                        <td class="fw-bold text-start">{{$invoice->created_at}}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="text-muted">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone ki-wallet fs-2 me-2">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                                <span class="path3"></span>
                                                                                <span class="path4"></span>
                                                                            </i>{{__('messages.payment_method')}}</div>
                                                                        </td>
                                                                        <td class="fw-bold text-start">
                                                                            @foreach($paymentMethods as $payment)
                                                                                {{$payment['name']}}
                                                                                @if (!$loop->last)
                                                                                ,
                                                                            @endif
                                                                            @endforeach
                                                                        <img src="{{asset('marble/src/')}}/media/svg/card-logos/visa.svg" class="w-50px ms-2" /></td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="text-muted">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone ki-home fs-2 me-2">
                                                                            </i>{{__('messages.thebranch')}}</div>
                                                                        </td>
                                                                                <td class="fw-bold text-start">{{$branchName}}</td>
                                                                    </tr>

                                                                    <tr>
                                                                        <td class="text-muted">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone ki-truck fs-2 me-2">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                                <span class="path3"></span>
                                                                                <span class="path4"></span>
                                                                                <span class="path5"></span>
                                                                            </i>{{__('messages.type')}}</div>
                                                                        </td>
                                                                        @if(__('messages.lang') == 'ar')
                                                                            @if($invoice['invoice_type'] == 'sell')
                                                                                <td class="fw-bold text-start">بيع</td>
                                                                            @else
                                                                                <td class="fw-bold text-start">ارجاع</td>
                                                                            @endif
                                                                        @else
                                                                            <td class="fw-bold text-start">{{$invoice['invoice_type']}}</td>
                                                                        @endif
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <!--end::Table-->
                                                        </div>
                                                    </div>
                                                    <!--end::Card body-->
                                                </div>
                                                <!--end::Order details-->
                                            </div>
                                            <!--end::Order summary-->
                                            <!--begin::Order summary-->
                                            <div class="d-flex flex-column col-xl-6" style="padding-right:0">
                                                <!--begin::Order details-->
                                                <div class="card card-flush py-4 flex-row-fluid">
                                                    <!--begin::Card header-->
                                                    <div class="card-header">
                                                        <div class="card-title">
                                                        </div>
                                                    </div>
                                                    <!--end::Card header-->
                                                    <!--begin::Card body-->
                                                    <div class="card-body pt-0">
                                                        <div class="table-responsive">
                                                            <!--begin::Table-->
                                                            <table class="table align-middle table-row-bordered mb-0 fs-6 gy-5 min-w-300px">
                                                                <tbody class="fw-semibold text-gray-600">
                                                                    <tr>
                                                                        <td class="text-danger">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone ki-minus text-danger fs-2 me-2">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                            </i>المبلغ المتبقي</div>
                                                                        </td>
                                                                        <td class="fw-bold text-start text-danger">{{$remainingPostPay}}</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="text-success">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone text-success ki-plus fs-2 me-2">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                                <span class="path3"></span>
                                                                                <span class="path4"></span>
                                                                            </i>المبلغ المدفوع</div>
                                                                        </td>
                                                                        <td class="fw-bold text-start text-success">{{BCMathExtended\BC::add($remainingPostPay,$invoice['total_tax_inclusive'],10)}}</td>

                                                                    </tr>
                                                                    <tr>
                                                                        <td class="text-bold bold">
                                                                            <div class="d-flex align-items-center">
                                                                            <i class="ki-duotone ki-tag fs-2 me-2">
                                                                                <span class="path1"></span>
                                                                                <span class="path2"></span>
                                                                                <span class="path3"></span>
                                                                            </i>الاجمالي</div>
                                                                        </td>
                                                                        <td class="fw-bold text-start">{{$invoice['total_tax_inclusive']}}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <!--end::Table-->
                                                        </div>
                                                    </div>
                                                    <!--end::Card body-->
                                                </div>
                                                <!--end::Order details-->
                                            </div>

                                        </div>
										<!--end::Order summary-->
										<!--begin::Tab content-->
										<div class="tab-content">
											<!--begin::Tab pane-->
											<div class="tab-pane fade show active" id="kt_ecommerce_sales_order_summary" role="tab-panel">
												<!--begin::Orders-->
												<div class="d-flex flex-column gap-7 gap-lg-10">
													<!--begin::Product List-->
													<div class="card card-flush py-4 flex-row-fluid overflow-hidden">
														<!--begin::Card header-->
														<div class="card-header">
															<div class="card-title">
																<h2>استلام مبلغ</h2>
															</div>
														</div>
                                                        <div class="card-body pt-0">
                                                            <form method="POST"  action="{{url("invoices/receive/{$invoice['id']}/store")}}">
                                                                @csrf
                                                                <div class="table-responsive">
                                                                <!--begin::Table-->
                                                                <table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
                                                                    <thead>
                                                                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                                            <th class="min-w-175px">المبلغ المستلم</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody class="fw-semibold text-gray-600">
                                                                        @if($invoice['invoice_type'] == 'sell')
                                                                        <tr>
                                                                            <td>
                                                                                <input type="text" name="amount" class="form-control" style="width:20%" placeholder="المبلغ المستلم من العميل" value="{{old("amount")}}" />
                                                                            </td>
                                                                        </tr>
                                                                        @endif
                                                                        <tr>
                                                                            <td colspan="1" class="text-start">طريقة الدفع:</td>
                                                                        </tr>

                                                                        <tr class="pt-0">
                                                                            <td>
                                                                                @foreach($payments as $paymentMethod)
                                                                                    @if(!$paymentMethod['is_postpay'])
                                                                                        <div class="form-check form-check-custom form-check-solid pt-5">
                                                                                            <input class="form-check-input" name='payment' type="radio" value="{{$paymentMethod["id"]}}" {{$loop->first?'checked':''}} id="flexRadioDefault"/>
                                                                                            <label class="form-check-label text-pink" for="flexRadioDefault">
                                                                                                {{$paymentMethod["name"]}}
                                                                                            </label>
                                                                                        </div>
                                                                                    @endif
                                                                                @endforeach
                                                                            </td>
                                                                        </tr>


                                                                    </tbody>
                                                                </table>
                                                                <input type="submit" class="btn btn-danger btn-lg" value="استلام"/>

                                                                <!--end::Table-->
                                                                </div>
                                                            </form>
                                                        </div>

														<!--end::Card header-->
													</div>
													<!--end::Product List-->
												</div>
												<!--end::Orders-->
											</div>
											<!--end::Tab pane-->
										</div>
										<!--end::Tab content-->
									</div>
									<!--end::Order details page-->
								</div>
								<!--end::Content container-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Content wrapper-->
						<!--begin::Footer-->
						@include('theme::marble.footer')
						<!--end::Footer-->
					</div>
					<!--end:::Main-->
					@if(session('payment_id'))
					<iframe style="display:none" id="invoice"  src="{{session('payment_id')?asset("invoices/receive/print/").'/'.session('payment_id'):''}}" name="invoice" frameborder="0"></iframe>
                    <script>
                        // Get the current URL
                        const url = new URL(window.location.href);

                        // Get the search parameters from the URL
                        const searchParams = new URLSearchParams(url.search);
                        // Get the value of the 'test' parameter
                        const testValue = searchParams.get('return');

                        if(testValue){
                            var iframe = document.getElementById("invoice");
                                // iframe.contentWindow.location.reload();
                                iframe.onload = function() {
                                    window.frames["invoice"].focus();
                                    window.frames["invoice"].print();

                                };
                        }
						const pathname = url.pathname;

						// Extract the ID from the pathname

						var submit = document.getElementById('print');
						submit.onclick = function() {
						var iframe = document.getElementById("invoice");
						iframe.setAttribute('src', '/invoices/receive/print/'+{{session('payment_id')}});

						// iframe.contentWindow.location.reload();
							window.frames["invoice"].focus();
							window.frames["invoice"].print();

					}

                    </script>
					@endif


@endsection
