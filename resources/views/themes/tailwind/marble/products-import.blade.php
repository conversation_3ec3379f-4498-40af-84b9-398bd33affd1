@extends('theme::marble.layout')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

@if (session()->has('failures'))
    <div class="alert alert-danger">
        <ul>
            @foreach (session('failures') as $failure)
                @foreach ($failure->errors() as $error)
                    <li> Row {{ $failure->row() }} failed: {{ $error }}</li>
                @endforeach
            @endforeach
        </ul>
    </div>
@endif

<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">
        <!--begin::Toolbar-->
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <!--begin::Toolbar container-->
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <!--begin::Title-->
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.import_file')}}</h1>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">
                            <a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">
                            <a href="{{url('products')}}" class="text-muted text-hover-primary">{{__('messages.products')}}</a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">{{__('messages.import_file')}}</li>
                        <!--end::Item-->
                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Page title-->
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                    <!--begin::Secondary button-->
                    <!--end::Secondary button-->
                    <!--begin::Primary button-->
                    <!--end::Primary button-->
                </div>

            </div>
            <!--end::Toolbar container-->
        </div>
        <!--end::Toolbar-->
        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->

            <div id="kt_app_content_container" class="app-container container-xxl">
                <form id="kt_ecommerce_add_product_form" class="form d-flex flex-column flex-lg-row" method="POST" enctype="multipart/form-data" action="{{url('products/import/store')}}">
                    <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                        <!--begin:::Tabs-->
                        <div class="tab-content">
                            <!--begin::Tab pane-->
                            <div class="tab-pane fade show active" id="kt_ecommerce_add_product_general" role="tab-panel">
                                <div class="d-flex flex-column gap-7 gap-lg-10">
                                    <!--begin::Product Type Selection-->
                                    <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h2>{{__('messages.product_type')}}</h2>
                                            </div>
                                        </div>
                                        <!--end::Card header-->
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0">
                                            <!--begin::Input group-->
                                            <div class="fv-row mb-2">
                                                <label class="required form-label">{{__('messages.select_product_type')}}</label>
                                                <select name="product_type" id="product_type" class="form-select form-select-solid" data-control="select2" data-placeholder="{{__('messages.select_product_type')}}">
                                                    <option value="simple" selected>{{__('enums.simple')}}</option>
                                                    <option value="weighted">{{__('enums.weighted')}}</option>
                                                    <option value="service">{{__('enums.service')}}</option>
                                                    <option value="variant">{{__('enums.variant')}}</option>
                                                </select>
                                            </div>
                                            <!--end::Input group-->

                                            <!--begin::Product type descriptions-->
                                            <div class="text-muted fs-7">
                                                <div id="simple-desc" class="product-type-desc">{{__('messages.simple_product_desc')}}</div>
                                                <div id="weighted-desc" class="product-type-desc d-none">{{__('messages.weighted_product_desc')}}</div>
                                                <div id="service-desc" class="product-type-desc d-none">{{__('messages.service_product_desc')}}</div>
                                                <div id="variant-desc" class="product-type-desc d-none">{{__('messages.variant_product_desc')}}</div>
                                            </div>
                                            <!--end::Product type descriptions-->
                                        </div>
                                    </div>
                                    <!--end::Product Type Selection-->

                                    <!--begin::Template Download-->
                                    <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title d-block">
                                                <h2 class=" mb-2">{{__('messages.import_file_download')}} </h2>
                                                <i> {{__('messages.import_product_file_download_desc')}}</i>
                                            </div>
                                        </div>
                                        <!--end::Card header-->
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0 mt-4">
                                            <!--begin::Form-->
                                                    <!--begin::Input group-->
                                                    <div class="fv-row" >
                                                        <div class="dropzone cursor-default">
                                                            <!--begin::Message-->
                                                            <div class="dz-message ">
                                                                <i class="ki-duotone ki-file-down fs-3x text-primary"><span class="path1"></span><span class="path2"></span></i>

                                                                <!--begin::Info-->
                                                                <div class="ms-4">
                                                                    <span> {{__('messages.import_product_file_download_desc2')}} <a href="{{url('products/template-export/simple')}}" id="template-download-link"> {{__('messages.here')}}</a></span>
                                                                </div>
                                                                <!--end::Info-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!--end::Input group-->
                                        </div>
                                        <!--end::Card header-->
                                    </div>
                                    <!--end::Template Download-->
                                </div>
                            </div>
                        </div>
                        <div class="tab-content">
                            <!--begin::Tab pane-->
                            <div class="tab-pane fade show active" id="kt_ecommerce_add_product_general" role="tab-panel">
                                <div class="d-flex flex-column gap-7 gap-lg-10">
                                    <!--begin::General options-->
                                    <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h2>{{__('messages.file')}}</h2>
                                            </div>
                                        </div>
                                        <!--end::Card header-->
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0">
                                            <!--begin::Form-->
                                                    <!--begin::Input group-->
                                                    <div class="fv-row">
                                                        <div class="dropzone" id="s">
                                                            <!--begin::Message-->
                                                            <div class="dz-message ">
                                                                <i class="ki-duotone ki-file-up fs-3x text-primary"><span class="path1"></span><span class="path2"></span></i>

                                                                <!--begin::Info-->
                                                                <div class="ms-4">
                                                                    <input type="file" name="file" id="" />
                                                                </div>
                                                                <!--end::Info-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!--end::Input group-->
                                        </div>
                                        <!--end::Card header-->
                                    </div>
                                    <!--end::General options-->
                                </div>
                            </div>
                        </div>

                        <!--end::Tab content-->
                        <div class="d-flex justify-content-end">
                            <!--begin::Button-->
                            <a href="{{url('products')}}" id="kt_ecommerce_add_product_cancel" class="btn btn-light me-5">{{__('messages.cancel')}}</a>
                            <!--end::Button-->
                            <!--begin::Button-->
                            <button type="submit" id="kt_ecommerce_add_product_submit" class="btn btn-primary">
                                <span class="indicator-label">{{__('messages.import')}}</span>
                                <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                            </button>
                            <!--end::Button-->
                        </div>
                    </div>
                    @csrf
                </form>
            </div>

        </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productTypeSelect = document.getElementById('product_type');
    const templateDownloadLink = document.getElementById('template-download-link');
    const productTypeDescs = document.querySelectorAll('.product-type-desc');

    // Update template download link and description based on product type
    function updateTemplateLink() {
        const selectedType = productTypeSelect.value;
        const baseUrl = '{{ url("products/template-export") }}';
        templateDownloadLink.href = `${baseUrl}/${selectedType}`;

        // Hide all descriptions
        productTypeDescs.forEach(desc => desc.classList.add('d-none'));

        // Show selected description
        const selectedDesc = document.getElementById(`${selectedType}-desc`);
        if (selectedDesc) {
            selectedDesc.classList.remove('d-none');
        }
    }

    // Update on change
    productTypeSelect.addEventListener('change', updateTemplateLink);

    // Initialize on page load
    updateTemplateLink();
});
</script>
@endpush
