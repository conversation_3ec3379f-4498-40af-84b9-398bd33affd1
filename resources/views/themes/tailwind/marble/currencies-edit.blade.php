@extends('theme::marble.layout')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
@if (session('success'))
    <div class="alert alert-success">
        <ul>
                <li>{{ session('success') }}</li>
        </ul>
    </div>
@endif

@endif

<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">
        <!--begin::Toolbar-->
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <!--begin::Toolbar container-->
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <!--begin::Title-->
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.currencies_edit')}}</h1>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">
                            <a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <li class="breadcrumb-item text-muted">
                            <a href="{{url('settings/main')}}" class="text-muted text-hover-primary">{{__('messages.settings')}}</a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">{{__('messages.currencies_edit')}}</li>
                        <!--end::Item-->
                    </ul>
                <!--end::Breadcrumb-->
                </div>
                <!--end::Page title-->
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                    <!--begin::Secondary button-->
                    <!--end::Secondary button-->
                    <!--begin::Primary button-->
                    <!--end::Primary button-->
                </div>

            </div>
            <!--end::Toolbar container-->
        </div>
        <!--end::Toolbar-->
        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div id="kt_app_content_container" class="app-container container-xxl">
                <!--begin::Form-->
                <form id="kt_ecommerce_add_product_form" class="form d-flex flex-column flex-lg-row" method="POST"  action="{{url("settings/currencies/edit/{$currency->id}/store")}}">
                    <!--begin::Aside column-->
                    <!--end::Aside column-->
                    <!--begin::Main column-->
                    <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                        <!--begin:::Tabs-->
                        <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-n2">
                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab" href="#kt_ecommerce_add_product_general">{{__('messages.currencies')}}</a>
                            </li>
                            <!--end:::Tab item-->
                        </ul>
                        <!--end:::Tabs-->
                        <!--begin::Tab content-->
                        <div class="tab-content">
                            <!--begin::Tab pane-->
                            <div class="tab-pane fade show active" id="kt_ecommerce_add_product_general" role="tab-panel">
                                <div class="d-flex flex-column gap-7 gap-lg-10">
                                    <!--begin::General options-->
                                    <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h2>{{__('messages.currency')}}</h2>
                                            </div>
                                        </div>
                                        <!--end::Card header-->
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0">
                                            <!--begin::Input group-->
                                            <div class="mb-10 fv-row">
                                                <!--begin::Label-->
                                                <label class="required form-label">{{__('messages.currency_name')}}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input type="text" name="name" class="form-control" placeholder="{{__('messages.currency_name')}}" value="{{$currency->name}}" />
                                                <!--end::Input-->
                                                <!--begin::Description-->
                                                <!--end::Description-->
                                            </div>
                                            <div class="mb-5 fv-row">
                                                <!--begin::Label-->
                                                <label class="required form-label">{{__('messages.currency_symbol')}}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input type="text" id="symbolInput" name="symbol" class="form-control" placeholder="{{__('messages.currency_symbol')}}" {{in_array($currency?->symbol,['SAR','AED','BHD','QAR'])?'disabled':''}} value="{{$currency?->symbol}}" />
                                                <!--end::Input-->
                                            </div>
                                            <div class="mb-5 fv-row">
                                                {{__('messages.OR')}}
                                            </div>
                                            <!--begin::Radio group-->
                                            <div class="btn-group w-100 w-lg-50 mb-5" data-kt-buttons="true" data-kt-buttons-target="[data-kt-button]">
                                                <!--begin::Radio-->
                                                <label class="btn btn-outline btn-color-muted btn-active-primary {{$currency?->symbol == "SAR"? "active": ''}}" data-kt-button="true">
                                                    <!--begin::Input-->
                                                    <input class="btn-check" type="radio" name="symbol" {{$currency?->symbol == "SAR"? "checked": ''}} value="SAR" />
                                                    <!--end::Input-->
                                                    <img class="w-20px" src="{{url('')}}/marble/src/media/svg/currencies/Saudi_Riyal_Symbol.svg">
                                                </label>
                                                <!--end::Radio-->

                                                <!--begin::Radio-->
                                                <label class="btn btn-outline btn-color-muted btn-active-primary  {{$currency?->symbol == "AED"? "active": ''}}" data-kt-button="true">
                                                    <!--begin::Input-->
                                                    <input class="btn-check" type="radio" name="symbol" {{$currency?->symbol == "AED"? "checked": ''}} value="AED" />
                                                    <!--end::Input-->
                                                    AED
                                                </label>
                                                <!--end::Radio-->

                                                <!--begin::Radio-->
                                                <label class="btn btn-outline btn-color-muted btn-active-primary  {{$currency?->symbol == "BHD"? "active": ''}}" data-kt-button="true">
                                                    <!--begin::Input-->
                                                    <input class="btn-check" type="radio" name="symbol" {{$currency?->symbol == "BHD"? "checked": ''}} value="BHD" />
                                                    <!--end::Input-->
                                                    BHD
                                                </label>
                                                <!--end::Radio-->

                                                <!--begin::Radio-->
                                                <label class="btn btn-outline btn-color-muted btn-active-primary  {{$currency?->symbol == "QAR"? "active": ''}}" data-kt-button="true">
                                                    <!--begin::Input-->
                                                    <input class="btn-check" type="radio" name="symbol" {{$currency?->symbol == "QAR"? "checked": ''}}  value="QAR" />
                                                    <!--end::Input-->
                                                    QAR
                                                </label>
                                                <!--end::Radio-->
                                            </div>
                                            <!--end::Radio group-->
                                            <div class="mb-5 fv-row">
                                                {{__('messages.main_currency')}}
                                            </div>

                                            <div class="form-check form-switch form-check-custom form-check-solid mt-5">

                                                <input class="form-check-input" name="default" type="checkbox" {{$currency->default == 1?'checked':''}} value="1" id="flexSwitchDefault"/>
                                                <label class="form-check-label" for="flexSwitchDefault">
                                                    {{__('messages.enable')}}
                                                </label>
                                            </div>

                                            <!--begin::Description-->
                                            <div class="text-muted fs-7">{{__('messages.default_currency_desc')}}</div>
                                            <!--end::Description-->

                                        </div>
                                        <!--end::Card header-->
                                    </div>
                                    <!--end::General options-->
                                </div>
                            </div>
                        </div>
                        <script>
                        document.addEventListener('DOMContentLoaded', function () {
                            const symbolInput = document.getElementById('symbolInput');
                            const radioButtons = document.querySelectorAll('input[type="radio"][name="symbol"]');

                            // Track the currently selected radio button
                            let selectedRadio = null;

                            radioButtons.forEach(radio => {
                                radio.addEventListener('click', function () {
                                    // Use a small delay to let the library update the DOM first
                                    setTimeout(() => {
                                        if (selectedRadio === this) {
                                            // If the same radio button is clicked again:
                                            // 1. Uncheck the radio button
                                            this.checked = false;
                                            selectedRadio.closest('label').classList.remove('active');

                                            // 2. Enable the input field
                                            symbolInput.disabled = false;
                                            // 3. Reset the selected radio
                                            selectedRadio = null;
                                        } else {
                                            // If a different radio button is clicked:
                                            // 1. Disable the input field
                                            symbolInput.disabled = true;
                                            // 2. Update the selected radio
                                            selectedRadio = this;
                                        }
                                    }, 10); // 10ms delay (adjust as needed)
                                });
                            });
                            // Enable the input field if the user starts typing
                            symbolInput.addEventListener('input', function () {
                                if (symbolInput.value.trim() !== '') {
                                    // If the user starts typing, uncheck all radio buttons
                                    radioButtons.forEach(radio => {
                                        radio.checked = false;
                                    });
                                    selectedRadio = null; // Reset the selected radio
                                }
                            });
                        });
                        </script>
                        <!--end::Tab content-->
                        <div class="d-flex justify-content-end">
                            <!--begin::Button-->
                            <a href="{{url('settings/main')}}" id="kt_ecommerce_add_product_cancel" class="btn btn-light me-5">{{__('messages.cancel')}}</a>
                            <!--end::Button-->
                            <!--begin::Button-->
                            <button type="submit" id="kt_ecommerce_add_product_submit" class="btn btn-primary">
                                <span class="indicator-label">{{__('messages.save')}}</span>
                                <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                            </button>
                            <!--end::Button-->
                        </div>
                    </div>
                    @csrf
                </form>

@endsection
