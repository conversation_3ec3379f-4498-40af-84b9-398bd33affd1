<!DOCTYPE html>
<html direction="{{__('messages.direction')}}" dir="{{__('messages.direction')}}" style="direction: {{__('messages.direction')}}" lang="{{__('messages.lang')}}">
	<!--begin::Head-->
	<head><base href=""/>
		<title>Point - {{isset($title)?$title:''}}</title>
		@livewireStyles
		<meta charset="utf-8" />
		<meta name="description" content="Point Cashier App" />
		<meta name="keywords" content="Point Cashier App" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta property="og:title" content="Point Cashier App" />
		<link rel="shortcut icon" href="{{asset('marble/src/media/logos/favicon.ico')}}" />
		<!--begin::Fonts(mandatory for all pages)-->
		<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic&display=swap" rel="stylesheet">

		<!--end::Fonts-->
		<!--begin::Vendor Stylesheets(used for this page only)-->
		<link href="{{asset('marble/src/')}}{{__('messages.plugins.datatables')}}" rel="stylesheet" type="text/css" />
		<!--end::Vendor Stylesheets-->
		<!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
		<link href="{{asset('marble/src/')}}{{__('messages.plugins.global')}}" rel="stylesheet" type="text/css" />

		<link href="{{asset('marble/src/')}}{{__('messages.style.bundle')}}" rel="stylesheet" type="text/css" />
		<script>
			!function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
			posthog.init('phc_d3dXnmUfsXXgWgBXUQNZje0h1lj2lDkFudLQDIrCKnX',{api_host:'https://app.posthog.com'})
			posthog.identify(
				'{{getCurrentUser()->id}}',  // Replace 'distinct_id' with your user's unique identifier
				{ merchantId: '{{getCurrentUser()->merchant_id}}' , email: '{{getCurrentUser()->email}}', name: '{{getCurrentUser()->name}}',phone: '{{getCurrentUser()->phone_number}}', mainAccount: '{{getCurrentUser()->main_account}}' } // optional: set additional user properties
			);


		</script>
        <script src="https://cdn.ably.com/lib/ably.min-1.js"></script>


        <!--end::Global Stylesheets Bundle-->
		<meta name="csrf-token" content="{{ csrf_token() }}">
	</head>
	<!--end::Head-->
	<!--begin::Body-->
	<body {{Request::is('pos')? "data-kt-app-sidebar-minimize=on " :""}}  id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" style="font-family: 'IBM Plex Sans Arabic', sans-serif;" class="app-default">
		<!--begin::Theme mode setup on page load-->
		<script>var defaultThemeMode = "light"; var themeMode; if ( document.documentElement ) { if ( document.documentElement.hasAttribute("data-bs-theme-mode")) { themeMode = document.documentElement.getAttribute("data-bs-theme-mode"); } else { if ( localStorage.getItem("data-bs-theme") !== null ) { themeMode = localStorage.getItem("data-bs-theme"); } else { themeMode = defaultThemeMode; } } if (themeMode === "system") { themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"; } document.documentElement.setAttribute("data-bs-theme", themeMode); }</script>
		<!--end::Theme mode setup on page load-->
		<!--begin::App-->
		<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
			<!--begin::Page-->
			<div class="app-page flex-column flex-column-fluid" id="kt_app_page">
				<!--begin::Header-->
				<div id="kt_app_header" class="app-header">
					<!--begin::Header container-->
					<div class="app-container container-fluid d-flex align-items-stretch justify-content-between" id="kt_app_header_container">
						<!--begin::Sidebar mobile toggle-->
						<div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
							<div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="kt_app_sidebar_mobile_toggle">
								<i class="ki-duotone ki-abstract-14 fs-2 fs-md-1">
									<span class="path1"></span>
									<span class="path2"></span>
								</i>
							</div>
						</div>
						<!--end::Sidebar mobile toggle-->
						<!--begin::Header wrapper-->
						<div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1" id="kt_app_header_wrapper">
							<!--begin::Menu wrapper-->
								<!--begin::Menu-->
								<!--end::Menu-->
							</div>
							<!--end::Menu wrapper-->
							@include('theme::marble.navbar')
						</div>
						<!--end::Header wrapper-->
					</div>
					<!--end::Header container-->
				</div>
				<!--end::Header-->
				<!--begin::Wrapper-->
				<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
					<!--begin::Sidebar-->
					<div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
						<!--begin::Logo-->
						<div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">
							<!--begin::Logo image-->
							<a href="{{asset('')}}">
								<img alt="Logo" src="{{asset('marble/src/')}}/media/logos/default-dark.svg" class="h-55px app-sidebar-logo-default" />
								<img alt="Logo" src="{{asset('marble/src/')}}/media/logos/default-dark.svg" class="h-30px app-sidebar-logo-minimize" />
							</a>
							<!--end::Logo image-->
							<!--begin::Sidebar toggle-->
							<!--begin::Minimized sidebar setup:
            if (isset($_COOKIE["sidebar_minimize_state"]) && $_COOKIE["sidebar_minimize_state"] === "on") {
                1. "src/js/layout/sidebar.js" adds "sidebar_minimize_state" cookie value to save the sidebar minimize state.
                2. Set data-kt-app-sidebar-minimize="on" attribute for body tag.
                3. Set data-kt-toggle-state="active" attribute to the toggle element with "kt_app_sidebar_toggle" id.
                4. Add "active" class to to sidebar toggle element with "kt_app_sidebar_toggle" id.
            }
        -->
							<div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
								<i class="ki-duotone ki-double-left fs-2 rotate-180">
									<span class="path1"></span>
									<span class="path2"></span>
								</i>
							</div>
							<!--end::Sidebar toggle-->
						</div>
						<!--end::Logo-->
						<!--begin::sidebar menu-->
						@include("theme::marble.sidebar")
						<!--end::sidebar menu-->
					</div>
					<!--end::Sidebar-->
					<!--begin::Main-->
                    @yield("content")
					<!--end:::Main-->
				</div>
				<!--end::Wrapper-->
			</div>
			<!--end::Page-->
		</div>
		<!--end::App-->

		<!--begin::Javascript-->
		@livewireScripts

		<script>var hostUrl = "{{asset('marble/src/')}}/";</script>

		<!--begin::Global Javascript Bundle(mandatory for all pages)-->
		<script src="{{asset('marble/src/')}}/plugins/global/plugins.bundle.js"></script>
		<script src="{{asset('marble/src/')}}/js/scripts.bundle.js"></script>
		@if (Request::is('/'))
			<script src="{{asset('marble/src/js')}}/widget3.js"></script>
		@endif

		@if (Request::is(['products/edit/*','products/add']))
			<script src="{{asset('marble/src/')}}/js/custom/apps/ecommerce/catalog/save-product.js"></script>
		@endif
		@if (Request::is('products/categories/*'))
			<script src="{{asset('marble/src/')}}/js/custom/apps/ecommerce/catalog/save-product.js"></script>
		@endif
		@if (Request::is('inventory/*'))
			<script src="{{asset('marble/src/')}}/js/custom/apps/ecommerce/sales/save-order.js"></script>
		@endif



		@if (Request::is('expenses'))
			<script src="{{asset('marble/src/')}}/js/custom/apps/ecommerce/catalog/expenses.js"></script>
		@endif

		@if (Request::is('subscription'))
			<script src="{{asset('marble/src/')}}/js/custom/pages/pricing/general.js"></script>
		@endif

		@if (Request::is('marketplace'))
			<script src="{{asset('marble/src/')}}/js/custom/apps/projects/list/list.js"></script>
			<script src="{{asset('marble/src/')}}/js/widgets.bundle.js"></script>
			<script src="{{asset('marble/src/')}}/js/custom/widgets.js"></script>
		@endif
		@if (Request::is('marketplace/*'))
			<script src="{{asset('marble/src/')}}/js/custom/apps/projects/project/project.js"></script>

			<script src="{{asset('marble/src/')}}/js/custom/utilities/modals/create-account.js"></script>
		@endif

		@if (Request::is('settings/branches'))
			<script src="{{asset('marble/src/')}}/js/custom/utilities/modals/create-app.js"></script>
			<script src="{{asset('marble/src/')}}/plugins/custom/formrepeater/formrepeater.bundle.js"></script>
		@endif
		@if (Request::is('settings/branches/*'))
			<script src="{{asset('marble/src/')}}/js/custom/utilities/modals/create-app.js"></script>
			<script src="{{asset('marble/src/')}}/plugins/custom/formrepeater/formrepeater.bundle.js"></script>
		@endif

{{--        <script type="module">--}}
{{--            // Import the functions you need from the SDKs you need--}}
{{--            import { initializeApp } from "https://www.gstatic.com/firebasejs/10.14.0/firebase-app.js";--}}
{{--            // TODO: Add SDKs for Firebase products that you want to use--}}
{{--            // https://firebase.google.com/docs/web/setup#available-libraries--}}

{{--            // Your web app's Firebase configuration--}}
{{--            const firebaseConfig = {--}}
{{--                apiKey: "AIzaSyBcqjtfvhc1B6qOyxlYB9UqKX-Yp0p3yK0",--}}
{{--                authDomain: "point-97395.firebaseapp.com",--}}
{{--                projectId: "point-97395",--}}
{{--                storageBucket: "point-97395.appspot.com",--}}
{{--                messagingSenderId: "534400861230",--}}
{{--                appId: "1:534400861230:web:f4c3b50fdaf2e37d47cbbf"--}}
{{--            };--}}

{{--            // Initialize Firebase--}}
{{--            const firebaseApp = initializeApp(firebaseConfig);--}}
{{--        </script>--}}

		<!-- Arabic Numeral Converter - Global Implementation -->
		<script src="{{ asset('js/arabic-converter.js') }}"></script>

		@if(config('app.debug'))
		<!-- Enable debug mode in development environment -->
		<script>
			document.addEventListener('DOMContentLoaded', function() {
				if (window.arabicConverter) {
					window.arabicConverter.setDebug(true);
					console.log('Arabic Converter: Debug mode enabled for development');
				}
			});
		</script>
		@endif
	</body>
	<!--end::Body-->
</html>
