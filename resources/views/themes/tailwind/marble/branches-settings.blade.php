@extends('theme::marble.layout')

@section('content')

@include('theme::marble.branch-sidebar')


<!--begin::Content-->
<div id="kt_app_content" class="app-content flex-column-fluid">

    <!--begin::Toolbar-->
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">

        <!--begin::Toolbar container-->
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">

            <!--begin::Page title-->
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <!--begin::Title-->
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.branch_settings')}}</h1>
                <!--end::Title-->
                <!--begin::Breadcrumb-->
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-muted">
                        <a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
                    </li>
                    <!--end::Item-->
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-muted">
                        <a href="{{url('settings/branches')}}" class="text-muted text-hover-primary">{{__('messages.branches_registeries')}}</a>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-muted">{{__('messages.branch_settings')}}</li>
                    <!--end::Item-->
                </ul>
                <!--end::Breadcrumb-->
            </div>
            <!--end::Page title-->
        </div>
        <!--end::Toolbar container-->
    </div>
    <!--end::Toolbar-->

    <!--begin::Content container-->
    <div id="kt_app_content_container" class="app-container container-xxl">
        @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif
        @if (session('success'))
            <div class="alert alert-success">
                <ul>
                    <li>{{ session('success') }}</li>
                </ul>
            </div>
        @endif

        <!--begin::Card-->
        <div class="card card-flush">
            <!--begin::Card body-->
            <div class="card-body">
                <!--begin:::Tabs-->
                <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-semibold mb-15">
                    <!--begin:::Tab item-->
                    <li class="nav-item">
                        <a class="nav-link text-active-primary d-flex align-items-center pb-5 active" data-bs-toggle="tab" href="#kt_branch_settings_general">
                            <i class="ki-duotone ki-home fs-2 me-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            {{__('messages.branch_settings')}}
                        </a>
                    </li>
                    <!--end:::Tab item-->
                </ul>
                <!--end:::Tabs-->

                <!--begin:::Tab content-->
                <div class="tab-content" id="myTabContent">
                    <!--begin:::Tab pane-->
                    <div class="tab-pane fade show active" id="kt_branch_settings_general" role="tabpanel">
                        <!--begin::Form-->
                        <form id="kt_branch_settings_general_form" class="form" action="{{ url("settings/branches/{$currentBranch->id}/store") }}" method="POST">
                            @csrf
                            <!--begin::Heading-->
                            <div class="row mb-7">
                                <div class="col-md-9 offset-md-3">
                                    <h2>{{__('messages.branch_settings')}} {{$currentBranch->branch_name}}</h2>
                                </div>
                            </div>
                            <!--end::Heading-->

                            <!--begin::Input group-->
                            <div class="row fv-row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span class="required">{{__('messages.branch_name')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.branch_name_tooltip')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                </div>
                                <div class="col-md-9">
                                    <input type="text" class="form-control form-control-solid"
                                           name="branch_name" value="{{ old('branch_name', $currentBranch->branch_name) }}" required/>
                                </div>
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="row fv-row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span class="required">{{__('messages.currency')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.currency_tooltip')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                </div>
                                <div class="col-md-9">
                                    <select class="form-select form-select-solid" name="currency_id"
                                            data-control="select2" data-placeholder="{{__('messages.select_currency')}}" required>
                                        <option></option>
                                        @foreach($currencies as $currency)
                                            <option value="{{$currency->id}}"
                                                {{old('currency_id', $currentBranch->currency_id) == $currency->id ? "selected" : ""}}>
                                                {{$currency->name}}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <!--end::Input group-->

                            <!--begin::Detailed Address-->
                            <div class="row mb-7">
                                <div class="col-md-3 text-md-end">
                                    <label class="fs-6 fw-semibold form-label mt-3">
                                        <span class="required">{{__('messages.branch_address')}}</span>
                                        <span class="ms-1" data-bs-toggle="tooltip" title="{{__('messages.branch_address_tooltip')}}">
                                            <i class="ki-duotone ki-information-5 text-gray-500 fs-6">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                                <span class="path3"></span>
                                            </i>
                                        </span>
                                    </label>
                                </div>
                                <div class="col-md-9">
                                    <div class="card card-flush">
                                        <div class="card-body" style="padding: 0;">
                                            <!-- Street -->
                                            <div class="mb-3">
                                                <label class="form-label" id="edit-branch-street-label">{{__('messages.street_name')}}</label>
                                                <input type="text" class="form-control" name="street"
                                                       value="{{ old('street', $currentBranch->address->street ?? '') }}"
                                                       placeholder="{{__('messages.street_name')}}" />
                                            </div>

                                            <!-- Building Number and Postal Code -->
                                            <div class="row mb-3">
                                                <div class="col">
                                                    <label class="form-label" id="edit-branch-building-number-label">{{__('messages.building_number')}}</label>
                                                    <input type="text" class="form-control" name="building_number"
                                                           value="{{ old('building_number', $currentBranch->address->building_number ?? '') }}"
                                                           placeholder="{{__('messages.building_number')}}" />
                                                </div>
                                                <div class="col">
                                                    <label class="form-label" id="edit-branch-postal-code-label">{{__('messages.postal_code')}}</label>
                                                    <input type="text" class="form-control" name="postal_code"
                                                           value="{{ old('postal_code', $currentBranch->address->postal_code ?? '') }}"
                                                           placeholder="{{__('messages.postal_code')}}" />
                                                </div>
                                            </div>

                                            @livewire('address-selector', ['model' => 'MerchantBranch', 'id' => $currentBranch->id])

                                            <!-- District -->
                                            <div class="mb-3">
                                                <label class="form-label" id="edit-branch-district-label">{{__('messages.district')}}</label>
                                                <input type="text" class="form-control" name="district"
                                                       value="{{ old('district', $currentBranch->address->district ?? '') }}"
                                                       placeholder="{{__('messages.district_name')}}" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end::Detailed Address-->

                            <!--begin::Action buttons-->
                            <div class="row py-5">
                                <div class="col-md-9 offset-md-3">
                                    <div class="d-flex">
                                        <button type="submit" class="btn btn-primary" data-kt-settings-type="submit">
                                            <span class="indicator-label">{{__('messages.save')}}</span>
                                            <span class="indicator-progress">
                                                {{__('messages.please_wait')}}
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!--end::Action buttons-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end:::Tab pane-->
                </div>
                <!--end:::Tab content-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content container-->
</div>
<!--end::Content-->

@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Add any branch-specific JavaScript here if needed
    });
</script>
@endpush
