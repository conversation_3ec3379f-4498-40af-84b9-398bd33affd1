@extends('theme::marble.layout')

@section('content')
@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif
@if (session('success'))
    <div class="alert alert-success">
        <ul>
                <li>{{ session('success') }}</li>
        </ul>
    </div>
@endif
					<!--begin::Main-->
					<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
						<!--begin::Content wrapper-->
						<div class="d-flex flex-column flex-column-fluid">
							<!--begin::Toolbar-->
							<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
								<!--begin::Toolbar container-->
								<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
									<!--begin::Page title-->
									<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
										<!--begin::Title-->
										<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.edit_main_setting')}}</h1>
										<!--end::Title-->
										<!--begin::Breadcrumb-->
										<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">
												<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<li class="breadcrumb-item text-muted">
												<a href="{{url('settings/main')}}" class="text-muted text-hover-primary">{{__('messages.settings')}}</a>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item">
												<span class="bullet bg-gray-400 w-5px h-2px"></span>
											</li>
											<!--end::Item-->
											<!--begin::Item-->
											<li class="breadcrumb-item text-muted">{{__('messages.main_settings')}}</li>
											<!--end::Item-->
										</ul>
										<!--end::Breadcrumb-->
									</div>
									<!--end::Page title-->
								</div>
								<!--end::Toolbar container-->
							</div>
							<!--end::Toolbar-->
							<!--begin::Content-->
							<div id="kt_app_content" class="app-content flex-column-fluid">
								<!--begin::Content container-->
								<div id="kt_app_content_container" class="app-container container-xxl">
									<!--begin::Row-->
									<div class="row g-5 g-xl-8">
										<div class="col-xl-12">
                                            <form class="form d-flex flex-column" method="POST" enctype="multipart/form-data" action="{{url('settings/main/edit/store')}}">
                                                @csrf
                                                <!--begin::List Widget 1-->
                                                <div class="card card-xl-stretch mb-xl-8">
                                                    <!--begin::Header-->
                                                    <div class="card-header border-0 pt-5">
                                                        <h3 class="card-title align-items-start flex-column">
                                                            <span class="card-label fw-bold text-dark">{{__('messages.main_settings')}}</span>
                                                            <span class="text-muted mt-1 fw-semibold fs-7">{{__('messages.main_settings_msg')}}</span>
                                                        </h3>
                                                    </div>
                                                    <!--end::Header-->
                                                    <!--begin::Body-->
                                                    <div class="card-body pt-5">
														<label class="form-label">{{__('messages.company_logo')}}</label>

                                                        <div class=" pt-0">

                                                            <!--begin::Input group-->
                                                            <div class="mb-10 fv-row">
                                                                <!--begin::Image input placeholder-->
                                                                @if(!empty($settings->logo))
                                                                <style>
                                                                    .image-input-placeholder {
                                                                        background-image: url('{{asset("storage/$settings->logo")}}');
                                                                    }

                                                                    [data-bs-theme="dark"] .image-input-placeholder {
                                                                        background-image: url('{{asset("storage/$settings->logo")}}');
                                                                    }
                                                                </style>
                                                                @else
                                                                <style>
                                                                    .image-input-placeholder {
                                                                        background-image: url('{{asset('marble/src/')}}/media/svg/files/blank-image.svg');
                                                                    }

                                                                    [data-bs-theme="dark"] .image-input-placeholder {
                                                                        background-image: url('{{asset('marble/src/')}}/media/svg/files/blank-image-dark.svg');
                                                                    }
                                                                </style>
                                                                @endif
                                                            <!--end::Image input placeholder-->

                                                            <!--begin::Image input-->
                                                            <div class="image-input image-input-empty image-input-outline image-input-placeholder" data-kt-image-input="true">
                                                                <!--begin::Image preview wrapper-->
                                                                <div class="image-input-wrapper w-125px h-125px"></div>
                                                                <!--end::Image preview wrapper-->

                                                                <!--begin::Edit button-->
                                                                <label class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="change"
                                                                data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click"
                                                                title="Change avatar">
                                                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                    <!--begin::Inputs-->
                                                                    <input type="file" name="logo" accept=".png, .jpg, .jpeg" />
                                                                    <input type="hidden" name="avatar_remove" />
                                                                    <!--end::Inputs-->
                                                                </label>
                                                                <!--end::Edit button-->

                                                                <!--begin::Cancel button-->
                                                                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="cancel"
                                                                data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click"
                                                                title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                <!--end::Cancel button-->

                                                                <!--begin::Remove button-->
                                                                <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="remove"
                                                                data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click"
                                                                title="Remove avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                <!--end::Remove button-->
                                                            </div>
                                                            <!--end::Image input-->
                                                            </div>
                                                            <div class="mb-10 fv-row">
                                                                <div class="row">
                                                                    <div class="col">
                                                                        <!--begin::Label-->
                                                                        <label class="required form-label">{{__('messages.company_name')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="name" class="form-control" placeholder="{{__('messages.company_name')}}" value="{{old('name')?old('name'):$settings->name}}" />
                                                                        <!--end::Input-->
                                                                        <!--begin::Description-->
                                                                        <div class="text-muted fs-7">{{__('messages.company_name_msg')}}</div>
                                                                        <!--end::Description-->
                                                                    </div>
                                                                    <div class="col">
                                                                        <!--begin::Label-->
                                                                        <label class="required form-label">{{__('validation.attributes.business_category')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="business_category" class="form-control" placeholder="{{__('validation.attributes.business_category')}}" value="{{old('business_category')?old('business_category'):$settings->business_category}}" />
                                                                        <!--end::Input-->
                                                                        <!--begin::Description-->
                                                                        <div class="text-muted fs-7">{{__('messages.business_category')}}</div>
                                                                        <!--end::Description-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="mb-10 row">
                                                                <div class="col">
                                                                <!--begin::Label-->
                                                                <label class="required form-label">{{__('messages.CR')}}</label>
                                                                <!--end::Label-->
                                                                <!--begin::Input-->
                                                                <input type="text" name="CR" class="form-control" placeholder="{{__('messages.CR')}}" value="{{old('CR')?old('CR'):$settings->CR}}" />
                                                                <!--end::Input-->
                                                                <!--begin::Description-->
                                                                <div class="text-muted fs-7">{{__('messages.CR_msg')}}</div>
                                                                <!--end::Description-->
                                                                </div>
                                                                <div class="col">
                                                                <!--begin::Label-->
                                                                <label class="form-label">{{__('messages.vat_number')}}</label>
                                                                <!--end::Label-->
                                                                <!--begin::Input-->
                                                                <input type="text" name="vat" class="form-control" placeholder="{{__('messages.vat_number')}}" value="{{old('vat')?old('vat'):$settings->VAT}}" />
                                                                <!--end::Input-->
                                                                <!--begin::Description-->
                                                                <div class="text-muted fs-7">{{__('messages.vat_msg')}}</div>
                                                                <!--end::Description-->
                                                                </div>
                                                            </div>
                                                            <div class="mb-10 fv-row">
                                                                <!--begin::Label-->
                                                                <label class="required form-label">{{__('messages.phone_number')}}</label>
                                                                <!--end::Label-->
                                                                <!--begin::Input-->
                                                                <input type="text" name="phone" class="form-control" placeholder="{{__('messages.phone_number')}}" value="{{old('phone')?old('phone'):$settings->phone}}" />
                                                                <!--end::Input-->
                                                            </div>

                                                            <div class="mb-10 fv-row">
                                                                <div class="mb-3">
                                                                    <!--begin::Label-->
                                                                    <label class="required form-label">{{__('messages.street_name')}}</label>
                                                                    <!--end::Label-->
                                                                    <!--begin::Input-->
                                                                    <input type="text" name="street" class="form-control" placeholder="{{__('messages.street_name')}}" value="{{old('street')?old('street'):$address?->street}}" />
                                                                    <!--end::Input-->
                                                                </div>

                                                                <div class="row mb-3">
                                                                    <div class="col">
                                                                        <!-- Building Number -->
                                                                        <!--begin::Label-->
                                                                        <label class="required form-label">{{__('messages.building_number')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="building_number" class="form-control" placeholder="{{__('messages.building_number')}}" value="{{old('building_number')?old('building_number'):$address?->building_number}}" />
                                                                        <!--end::Input-->
                                                                    </div>

                                                                    <div class="col">
                                                                        <!-- Postal Code -->
                                                                        <!--begin::Label-->
                                                                        <label class="required form-label">{{__('messages.postal_code')}}</label>
                                                                        <!--end::Label-->
                                                                        <!--begin::Input-->
                                                                        <input type="text" name="postal_code" class="form-control" placeholder="{{__('messages.postal_code')}}" value="{{old('postal_code')?old('postal_code'):$address?->postal_code}}" />
                                                                        <!--end::Input-->
                                                                    </div>
                                                                </div>
                                                                @livewire('address-selector',['model' => $model,'id' => $user->merchant_id])

                                                                <div class="mb-3">
                                                                    <!--begin::Label-->
                                                                    <label class="required form-label">{{__('messages.district')}}</label>
                                                                    <!--end::Label-->
                                                                    <!--begin::Input-->
                                                                    <input type="text" name="district" class="form-control" placeholder="{{__('messages.district_name')}}" value="{{old('district')?old('district'):$address?->district}}" />
                                                                    <!--end::Input-->
                                                                </div>



                                                            </div>
                                                            <!--end::Input group-->
                                                        </div>
                                                        <input class="btn btn-primary" value="{{__('messages.save')}}" type="submit" />
                                                    </div>
                                                    <!--end::Body-->
                                                </div>
                                                <!--end::List Widget 1-->
                                        </form>
										</div>
									</div>
									<!--end::Row-->
								</div>
								<!--end::Content container-->
							</div>
							<!--end::Content-->
						</div>
						<!--end::Content wrapper-->
						<!--begin::Footer-->
                        @include('theme::marble.footer')
						<!--end::Footer-->
					</div>

@endsection
