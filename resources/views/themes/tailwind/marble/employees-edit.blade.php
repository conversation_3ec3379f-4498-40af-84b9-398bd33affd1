@extends('theme::marble.layout')

@section('content')

@if ($errors->any())
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
@endif

@if (session('branches_required'))
    <div class="alert alert-danger">
        <ul>
                <li>{{ __('messages.permited_branches_required') }}</li>
        </ul>
    </div>
@endif

@if (session('success'))
    <div class="alert alert-success">
        <ul>
            <li>{{ session('success') }}</li>
        </ul>
    </div>
@endif


<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
    <!--begin::Content wrapper-->
    <div class="d-flex flex-column flex-column-fluid">
        <!--begin::Toolbar-->
        <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
            <!--begin::Toolbar container-->
            <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <!--begin::Title-->
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('messages.employee_edit')}}</h1>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">
                            <a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <li class="breadcrumb-item text-muted">
                            <a href="{{url('employees')}}" class="text-muted text-hover-primary">{{__('messages.employees')}}</a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item">
                            <span class="bullet bg-gray-400 w-5px h-2px"></span>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->
                        <li class="breadcrumb-item text-muted">{{__('messages.employee_edit')}}</li>
                        <!--end::Item-->
                    </ul>
                <!--end::Breadcrumb-->
                </div>
                <!--end::Page title-->
                <div class="d-flex align-items-center gap-2 gap-lg-3">
                    <!--begin::Secondary button-->
                    <!--end::Secondary button-->
                    <!--begin::Primary button-->
                    <!--end::Primary button-->
                </div>

            </div>
            <!--end::Toolbar container-->
        </div>
        <!--end::Toolbar-->
        <!--begin::Content-->
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <!--begin::Content container-->
            <div id="kt_app_content_container" class="app-container container-xxl">
                <!--begin::Form-->
                <form id="kt_ecommerce_add_product_form" class="form d-flex flex-column flex-lg-row" method="POST"  action="{{url("employees/edit/{$user->id}/store")}}">
                    <!--begin::Aside column-->
                    <!--end::Aside column-->
                    <!--begin::Main column-->
                    <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                        <!--begin:::Tabs-->
                        <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-semibold mb-n2">
                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab" href="#kt_ecommerce_add_product_general">{{__('messages.users')}}</a>
                            </li>
                            <!--end:::Tab item-->
                        </ul>
                        <!--end:::Tabs-->
                        <!--begin::Tab content-->
                        @if($user->main_account)
                        <div class="alert alert-danger">
                            <ul>
                                <li>{{__('messages.warning_main_account')}}</li>
                            </ul>
                        </div>

                        @endif
                        <div class="tab-content">
                            <!--begin::Tab pane-->
                            <div class="tab-pane fade show active" id="kt_ecommerce_add_product_general" role="tab-panel">
                                <div class="d-flex flex-column gap-7 gap-lg-10">
                                    <!--begin::General options-->
                                    <div class="card card-flush py-4">
                                        <!--begin::Card header-->
                                        <div class="card-header">
                                            <div class="card-title">
                                                <h2>{{__('messages.general')}}</h2>
                                            </div>
                                        </div>
                                        <!--end::Card header-->
                                        <!--begin::Card body-->
                                        <div class="card-body pt-0">
                                            <!--begin::Input group-->
                                            <div class="mb-10 fv-row">
                                                <!--begin::Label-->
                                                <label class="required form-label">{{__('messages.users_name')}}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input type="text" name="name" class="form-control" placeholder="{{__('messages.users_name')}}" value="{{$user->name}}" />
                                                <!--end::Input-->
                                                <!--begin::Description-->
                                                <!--end::Description-->
                                            </div>
                                            <div class="mb-10 fv-row">
                                                <!--begin::Label-->
                                                <label class="required form-label">{{__('messages.users_email')}}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input type="text" name="email" class="form-control" {{$user->main_account?'disabled':''}} placeholder="{{__('messages.users_email')}}" value="{{$user->email}}" />
                                                <!--end::Input-->
                                                <!--begin::Description-->
                                                <!--end::Description-->
                                            </div>
                                            <div class="mb-10 fv-row">
                                                <!--begin::Label-->
                                                <label class="required form-label">{{__('messages.users_password')}}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <input type="password" name="password" class="form-control"  {{$user->main_account?'disabled':''}} placeholder="{{__('messages.users_password')}}" value="" />
                                                <!--end::Input-->
                                                <!--begin::Description-->
                                                <!--end::Description-->
                                            </div>
                                            @if(!$user->main_account)
                                            <div class="mb-10 fv-row">
                                                <label class="required form-label">{{__('messages.employee_permission_add')}}</label>

                                                <!--begin::Select2-->
                                                <select class="form-select mb-2" data-control="select2" data-hide-search="true" name="group_id" data-placeholder="{{__('messages.select_option')}}" id="r">
                                                    @foreach ($groups as $group)
                                                        <option {{$user['group_id'] == $group['id']?'selected': ''}} value="{{$group['id']}}">{{$group['name']}}</option>
                                                    @endforeach
                                                </select>
                                                <!--end::Select2-->
                                                <!--begin::Description-->
                                                <div class="text-muted fs-7">{{__('messages.employees_perm_set')}}</div>
                                            </div>
                                            @endif
                                            <!--begin::Description-->
                                            <div class="mb-10 fv-row">

                                                <!--begin::Label-->
                                                <label class="required form-label">{{__('messages.perm_branches')}}</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <div class="mb-10">
                                                    <input class="form-control form-control-lg" name="branches" placeholder="{{__('messages.choose_branches')}}" id="kt_tagify_1"/>
                                                </div>
                                                <!--end::Input-->
                                                <!--begin::Description-->
                                                <!--end::Description-->
                                            </div>

                                            <!--end::Description-->
                                        </div>
                                        <!--end::Card header-->
                                    </div>
                                    <!--end::General options-->

                        <!--end::Tab content-->
                        <div class="d-flex justify-content-end">
                            <!--begin::Button-->
                            <a href="{{url('settings/main')}}" id="kt_ecommerce_add_product_cancel" class="btn btn-light me-5">{{__('messages.cancel')}}</a>
                            <!--end::Button-->
                            <!--begin::Button-->
                            <button type="submit" id="kt_ecommerce_add_product_submit" class="btn btn-primary">
                                <span class="indicator-label">{{__('messages.save')}}</span>
                                <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                            </button>
                            <!--end::Button-->
                        </div>
                    </div>
                    @csrf
                </form>
                <script>
                document.addEventListener("DOMContentLoaded", function() {

                var input1 = document.querySelector("#kt_tagify_1");
                var branches = {!! json_encode($branches) !!};
                var accessedBranches = {!! json_encode($accessedBranches) !!};

                // Initialize Tagify components on the above inputs
                var whitelist = [];
                var whitelist = branches.map(function(branch) {
                    return { value: branch.branch_name, code: branch.id };
                });

                let t = new Tagify(input1, {
                    whitelist: whitelist,
                    maxTags: 10,
                    dropdown: {
                        maxItems: 20,           // <- mixumum allowed rendered suggestions
                        classname: "", // <- custom classname for this dropdown, so it could be targeted
                        enabled: 0,             // <- show suggestions on focus
                        closeOnSelect: false    // <- do not hide the suggestions dropdown once an item has been selected
                    }
                });

                accessedBranches.forEach(function(branch) {
                    t.addTags([{ value: branch.name, code: branch.branch_id }]);
                });

                });

                </script>

@endsection