<div class="card card-flush">
    <div class="card-header align-items-center py-5 gap-2 gap-md-5">
        <!--begin::Card title-->
        <div class="card-title">
            <!--begin::Search-->
            <div class="d-flex align-items-center position-relative my-1">
                <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
                <input wire:input.debounce.500ms="searchUpdated" wire:model='search' id='invoice-search' type="text" data-kt-ecommerce-order-filter="search" class="form-control form-control-solid w-250px ps-12" placeholder="{{__('messages.search_invoice')}}" />
            </div>
            <!--end::Search-->
        </div>
        <!--end::Card title-->
        <!--begin::Card toolbar-->
        <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
            <!--begin::Flatpickr-->
            <div wire:ignore class="input-group w-250px">
                <input class="form-control form-control-solid rounded rounded-end-0" placeholder="{{__('messages.pick_date_range')}}" name="date" id="kt_pos_invoices_flatpickr" />
                <button type="button" class="btn btn-icon btn-light" id="kt_pos_invoices_flatpickr_clear">
                    <i class="ki-duotone ki-cross fs-2">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                </button>
            </div>
            <!--end::Flatpickr-->
            <div wire:ignore class="w-100 mw-150px">
                <!--begin::Select2-->
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="{{__('messages.status')}}" data-kt-ecommerce-order-filter="status">
                    <option></option>
                    <option value="all">{{__('messages.all')}}</option>
                    <option value="sell">{{__('messages.sale')}}</option>
                    <option value="return">{{__('messages.return')}}</option>
                </select>
                <!--end::Select2-->
            </div>
        </div>
        <!--end::Card toolbar-->
    </div>
    <div class="card-body pt-0">
        <!--begin::Table-->
        @if(count($invoices) > 0)
        <div class="table-responsive">
            <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_pos_invoices_table">
                <thead>
                    <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                        <th class="text-start min-w-100px">{{__('messages.invoice_id')}}</th>
                        <th class="text-start min-w-100px">{{__('messages.type')}}</th>
                        <th class="text-start min-w-100px">{{__('messages.payment_method')}}</th>
                        <th class="text-end min-w-100px">{{__('messages.price_tax_exclusive')}}</th>
                        <th class="text-end min-w-100px">{{__('messages.total_price')}}</th>
                        <th class="text-end min-w-100px">{{__('messages.date_created')}}</th>
                    </tr>
                </thead>
                <tbody class="fw-semibold text-gray-600">
                    @foreach($invoices as $invoice)
                    <tr>
                        <td data-kt-ecommerce-order-filter="order_id">
                            <a href="#" wire:click.prevent="showInvoiceModal({{$invoice['id']}})" class="text-gray-800 text-hover-primary fw-bold">{{$invoice['pretty_id']}}</a>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="ms-2">
                                    @if(__('messages.lang') == 'ar')
                                        <div class="{{$invoice['invoice_type'] == 'sell'?'text-primary':'text-danger'}} fs-6 fw-bold">{{$invoice['invoice_type'] == 'sell'?'بيع':'ارجاع'}}</div>
                                    @else
                                        <div class="{{$invoice['invoice_type'] == 'sell'?'text-primary':'text-danger'}} fs-6 fw-bold">{{$invoice['invoice_type'] == 'sell'?'Sale':'Return'}}</div>
                                    @endif
                                </div>
                            </div>
                        </td>
                        <td class="text-start">
                            <div class="badge {{($invoice['has_postpay'])?'badge-light-warning':'badge-light-primary'}}">
                                @foreach($invoice['payments'] as $payment)
                                {{$payment->paymentMethod->name}}
                                    @if(!$loop->last)
                                    ,
                                    @endif
                                @endforeach
                            </div>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold">{{{$invoice['total_tax_exclusive']}}}</span>
                        </td>
                        <td class="text-end">
                            <span class="fw-bold">{{{$invoice['total_tax_inclusive']}}}</span>
                        </td>
                        <td class="text-end" data-order="{{\Carbon\Carbon::parse($invoice['created_at'])->toDateString()}}">
                            <span class="fw-bold">{{\Carbon\Carbon::parse($invoice['created_at'])->toDateString()}}</span>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="card-body d-flex flex-center flex-column py-10">
            <!--begin::Empty state illustration-->
            <img src="{{asset('marble/src/')}}/svg/invoice.svg" class="w-200px mb-5" alt="No invoices" />
            <!--end::Empty state illustration-->
            <!--begin::Message-->
            <div class="text-center">
                <h3 class="fs-2 fw-bold text-gray-900 mb-3">{{__('messages.product_invoice_first')}}</h3>
                <div class="fs-5 text-gray-600 mb-5">{{__('messages.product_invoice_first_note')}}</div>
            </div>
            <!--end::Message-->
        </div>
        @endif
        <!--end::Table-->
    </div>

    <!-- Pagination -->
    <div class="card-footer py-3">
        {{$invoices->links('livewire.pagination')}}
    </div>

    <!-- JavaScript for handling filters and date picker -->
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            var statusVal = '{{$status}}';
            var date_start = '{{$date_start}}';
            var date_end = '{{$date_end}}';
            var search = '{{$search}}';

            // Prevent form submission on Enter key in search field
            document.getElementById("invoice-search").addEventListener("keydown", function(event) {
                if (event.key === "Enter") {
                    event.preventDefault();
                }
            });

            // Initialize date picker
            const element = document.querySelector('#kt_pos_invoices_flatpickr');
            if (element) {
                flatpickr = $(element).flatpickr({
                    altInput: true,
                    altFormat: "Y-m-d",
                    dateFormat: "Y-m-d",
                    mode: "range",
                    defaultDate: [date_start, date_end],
                    onChange: function (selectedDates, dateStr, instance) {
                        if(selectedDates.length == 2){
                            const dateParts = dateStr.split(" to ");
                            Livewire.dispatch('dateChanged', {start: dateParts[0], end: dateParts[1]});
                        }
                    },
                });

                // Handle clear button for date picker
                const clearButton = document.querySelector('#kt_pos_invoices_flatpickr_clear');
                if (clearButton) {
                    clearButton.addEventListener('click', e => {
                        flatpickr.clear();
                        Livewire.dispatch('dateChanged', {start: 0, end: 0});
                    });
                }
            }

            // Handle status filter
            const filterStatus = document.querySelector('[data-kt-ecommerce-order-filter="status"]');
            if (filterStatus) {
                if(statusVal.length > 0){
                    filterStatus.value = statusVal;
                }

                $(filterStatus).on('change', e => {
                    let value = e.target.value;
                    if(value === 'all'){
                        value = '';
                        Livewire.dispatch('updatedStatus', {value: ''});
                    }
                    else if(value === 'sell'){
                        value = 'sell';
                        Livewire.dispatch('updatedStatus', {value: 'sell'});
                    }
                    else if(value === 'return'){
                        value = 'return';
                        Livewire.dispatch('updatedStatus', {value: 'return'});
                    }
                });
            }
        });
    </script>
</div> <!-- End of card -->
