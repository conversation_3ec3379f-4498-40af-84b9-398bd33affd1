<div>
	<!DOCTYPE html>
	<html direction="{{__('messages.direction')}}" dir="{{__('messages.direction')}}" style="direction: {{__('messages.direction')}}" lang="{{__('messages.lang')}}">
		<!--begin::Head-->
		<head><base href=""/>
			<title>Point - الكاشير</title>
			@livewireStyles
			<meta charset="utf-8" />
			<meta name="description" content="Point Cashier App" />
			<meta name="keywords" content="Point Cashier App" />
			<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
			<meta property="og:title" content="Point Cashier App" />
			<link rel="shortcut icon" href="{{asset('marble/src/media/logos/favicon.ico')}}" />
			<!--begin::Fonts(mandatory for all pages)-->
			<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
			<link rel="preconnect" href="https://fonts.googleapis.com">
			<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
			<link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic&display=swap" rel="stylesheet">
			<!--end::Fonts-->
			<!--begin::Vendor Stylesheets(used for this page only)-->
			<link href="{{asset('marble/src/')}}{{__('messages.plugins.datatables')}}" rel="stylesheet" type="text/css" />
			<!--end::Vendor Stylesheets-->
			<!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
			<link href="{{asset('marble/src/')}}{{__('messages.plugins.global')}}" rel="stylesheet" type="text/css" />

			<link href="{{asset('marble/src/')}}{{__('messages.style.bundle')}}" rel="stylesheet" type="text/css" />
			<script>
				!function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
				posthog.init('phc_d3dXnmUfsXXgWgBXUQNZje0h1lj2lDkFudLQDIrCKnX',{api_host:'https://app.posthog.com'})
			</script>


			<!--end::Global Stylesheets Bundle-->
		</head>
		<!--end::Head-->
		<!--begin::Body-->
		<body {{Request::is('pos/*')? "data-kt-app-sidebar-minimize=on " :""}}  id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" style="font-family: 'IBM Plex Sans Arabic', sans-serif;" class="app-default">
			<!--begin::Theme mode setup on page load-->
			<script>var defaultThemeMode = "light"; var themeMode; if ( document.documentElement ) { if ( document.documentElement.hasAttribute("data-bs-theme-mode")) { themeMode = document.documentElement.getAttribute("data-bs-theme-mode"); } else { if ( localStorage.getItem("data-bs-theme") !== null ) { themeMode = localStorage.getItem("data-bs-theme"); } else { themeMode = defaultThemeMode; } } if (themeMode === "system") { themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"; } document.documentElement.setAttribute("data-bs-theme", themeMode); }</script>
			<!--end::Theme mode setup on page load-->
			<!--begin::App-->
			<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
				<!--begin::Page-->
				<div class="app-page flex-column flex-column-fluid" id="kt_app_page">
					<!--begin::Wrapper-->
					<div class="flex-column flex-row-fluid" id="kt_app_wrapper">
						<!--begin::Main-->
						<div class="app-main flex-column flex-row-fluid" id="kt_app_main">
							<!--begin::Content wrapper-->
							<div class="d-flex flex-column flex-column-fluid">
								<!--begin::Toolbar-->
								<div id="kt_app_toolbar" class="app-toolbar py-3">
									<!--begin::Toolbar container-->
									<div id="kt_app_toolbar_container" class="app-container container-fluid container-xl d-flex flex-column flex-md-row flex-stack">
										<!--begin::Page title-->
										<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3 mb-3 mb-md-0">
											<!--begin::Title-->
											<div class="d-flex flex-column flex-md-row gap-3 align-items-center">
												<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0 mb-2 mb-md-0">{{__('messages.pos_system')}}</h1>
												<select class="form-select w-100 w-md-auto min-w-xl-200px form-select-sm" data-hide-search="true" data-control="select2" id='branches' data-placeholder="Select an option">
													<optgroup label="{{__('messages.branch_name')}}:"></optgroup>
													@foreach($branchesAndRegisteries as $b)
														<optgroup label="{{$b['branch_name']}}">
															@foreach($b['registers'] as $r)
																<option {{($branchId == $b['id'] && $registeryId == $r['id'])?'selected':''}} value="{{$r['id']}}" data-url="{{url("pos/{$b['id']}/{$r['id']}") }}">{{$r['name']}}</option>
															@endforeach
														</optgroup>
													@endforeach
												</select>
											</div>
											<!--end::Title-->
											<!--begin::Breadcrumb-->
											<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
												<!--begin::Item-->
												<li class="breadcrumb-item text-muted">
													<a href="{{url('')}}" class="text-muted text-hover-primary">{{__('messages.home')}}</a>
												</li>
												<!--end::Item-->
												<!--begin::Item-->
												<li class="breadcrumb-item">
													<span class="bullet bg-gray-400 w-5px h-2px"></span>
												</li>
												<!--end::Item-->
												<!--begin::Item-->
												<li class="breadcrumb-item text-muted">{{__('messages.point_of_sales')}}</li>
												<!--end::Item-->
											</ul>
											<!--end::Breadcrumb-->
										</div>
										<!--end::Page title-->
										<!--begin::Actions-->

										<div class="d-flex flex-wrap align-items-center gap-2 gap-lg-3">
											<!--begin::Primary button-->
											<a href="{{url('')}}" class="btn btn-sm fw-bold btn-primary mb-2 mb-md-0">{{__('messages.home')}}</a>
											<!--begin::Secondary button-->
											<a href="{{url("invoices?backto=pos&branchId={$branchId}&registeryId={$registeryId}")}}" class="btn btn-sm fw-bold bg-body btn-color-gray-700 btn-active-color-primary mb-2 mb-md-0">{{__('messages.recent_invoices')}}</a>
											<!--end::Secondary button-->
											<!--begin::Primary button-->
											<a href="{{url('products/add')}}" class="btn btn-sm fw-bold bg-body btn-color-gray-700 btn-active-color-primary mb-2 mb-md-0">{{__('messages.new_product')}}</a>

											@if($registerSettings?->open_close_register)
												@livewire('register-transaction', ['branchId' => $branchId, 'registerId' => $registeryId])
												<a href='{{url("pos/{$branchId}/{$registeryId}/close")}}' class="btn btn-sm fw-bold btn-danger">{{__('messages.close_registery')}}</a>
											@endif

											<!--end::Primary button-->
										</div>

										<!--end::Actions-->
									</div>

									<!--end::Toolbar container-->
								</div>
								<!--end::Toolbar-->
								<!--begin::Content-->


								<div id="kt_app_content" class="app-content flex-column-fluid overflow-auto">
									<!--begin::Content container-->
									<div id="kt_app_content_container" class="app-container container-fluid container-xl">
										@if($registerOpenTooLong)
										<div class="alert alert-warning d-flex flex-column flex-md-row align-items-center p-3 p-md-5 mb-5">
											<!--begin::Icon-->
											<i class="ki-duotone ki-information-5 fs-2hx text-warning me-4">
												<span class="path1"></span>
												<span class="path2"></span>
												<span class="path3"></span>
											</i>
											<!--end::Icon-->

											<!--begin::Wrapper-->
											<div class="d-flex flex-column mt-3 mt-md-0">
												<!--begin::Title-->
												<h4 class="mb-1" id="register-warning-title">{{__('messages.register_open_too_long_title')}}</h4>
												<!--end::Title-->
												<!--begin::Content-->
												<span id="register-warning-message">{{__('messages.register_open_too_long_message', ['hours' => floor($registerOpenHours), 'max_hours' => $maxOpenHours])}}</span>

												<script>
												    // Set text color based on theme mode
												    document.addEventListener('DOMContentLoaded', function() {
												        const setTextColor = () => {
												            const isDarkMode = document.documentElement.getAttribute('data-bs-theme') === 'dark';
												            const titleElement = document.getElementById('register-warning-title');
												            const messageElement = document.getElementById('register-warning-message');

												            // Remove existing color classes
												            titleElement.classList.remove('text-warning', 'text-black');
												            messageElement.classList.remove('text-warning', 'text-black');

												            if (isDarkMode) {
												                // In dark mode, use warning color
												                titleElement.classList.add('text-warning');
												                messageElement.classList.add('text-warning');
												            } else {
												                // In light mode, use black text
												                titleElement.classList.add('text-black');
												                messageElement.classList.add('text-black');
												            }
												        };

												        // Set initial color
												        setTextColor();

												        // Listen for theme changes
												        document.addEventListener('kt.thememode.change', setTextColor);
												    });
												</script>
												<!--end::Content-->
											</div>
											<!--end::Wrapper-->

											<!--begin::Close-->
											<div class="ms-auto mt-3 mt-md-0">
												<a href='{{url("pos/{$branchId}/{$registeryId}/close")}}' class="btn btn-sm btn-danger">{{__('messages.close_registery')}}</a>
											</div>
											<!--end::Close-->
										</div>
										@endif

										<!--begin::Tabs-->
										<ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
											<li class="nav-item">
												<a class="nav-link {{ $activeTab == 'cashier' ? 'active' : '' }}" data-tab="cashier" href="javascript:void(0)">
													<i class="ki-duotone ki-handcart fs-2 me-1">
														<span class="path1"></span>
														<span class="path2"></span>
														<span class="path3"></span>
													</i>
													{{__('messages.pos_system')}}
												</a>
											</li>
											<li class="nav-item">
												<a class="nav-link {{ $activeTab == 'invoices' ? 'active' : '' }}" data-tab="invoices" href="javascript:void(0)">
													<i class="ki-duotone ki-document fs-2 me-1">
														<span class="path1"></span>
														<span class="path2"></span>
														<span class="path3"></span>
													</i>
													{{__('messages.invoices')}}
												</a>
											</li>
											<li class="nav-item">
												<a class="nav-link {{ $activeTab == 'fulfillments' ? 'active' : '' }}" data-tab="fulfillments" href="javascript:void(0)">
													<i class="ki-duotone ki-delivery fs-2 me-1">
														<span class="path1"></span>
														<span class="path2"></span>
														<span class="path3"></span>
														<span class="path4"></span>
														<span class="path5"></span>
													</i>
													{{__('messages.fulfillment_orders')}}
												</a>
											</li>
										</ul>
										<!--end::Tabs-->

										<!--begin::Tab Content-->
										<div class="tab-content" id="myTabContent">
											<!--begin::Cashier Tab-->
											<div class="tab-pane fade {{ $activeTab == 'cashier' ? 'show active' : '' }}" id="kt_tab_cashier" role="tabpanel">
												<!--begin::Layout-->
												<div class="d-flex gap-4 flex-column flex-lg-row">
													<!--begin::Content-->
													<div class="col-12 col-lg-5 col-xl-4 mb-4 mb-lg-0">
														@livewire("pos-cart",['showSideBarProducts' => false,'branchId' => $branchId,'registeryId' => $registeryId])
													</div>
													<div class="col-12 col-lg-7 col-xl-8">
														@livewire("pos-products-list",['branchId' => $branchId,'registeryId' => $registeryId])
													</div>
													<!--end::Content-->
												</div>
												<!--end::Layout-->
											</div>
											<!--end::Cashier Tab-->

											<!--begin::Invoices Tab-->
											<div class="tab-pane fade {{ $activeTab == 'invoices' ? 'show active' : '' }}" id="kt_tab_invoices" role="tabpanel">
												@livewire('pos-invoices', ['branchId' => $branchId, 'registeryId' => $registeryId])
											</div>
											<!--end::Invoices Tab-->

											<!--begin::Fulfillments Tab-->
											<div class="tab-pane fade {{ $activeTab == 'fulfillments' ? 'show active' : '' }}" id="kt_tab_fulfillments" role="tabpanel">
												@livewire('pos-fulfillments', ['branchId' => $branchId, 'registeryId' => $registeryId])
											</div>
											<!--end::Fulfillments Tab-->
										</div>
										<!--end::Tab Content-->
									</div>
									<!--end::Content container-->
								</div>
								<!--end::Content-->
							</div>
							<!--end::Content wrapper-->
							<!--begin::Footer-->
							<!--end::Footer-->
						</div>
						<!--end:::Main-->
						<!--end:::Main-->
					</div>
					<!--end::Wrapper-->
				</div>
				<!--end::Page-->
			</div>
			<!--end::App-->
			<!--begin::Scrolltop-->
			<div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
				<i class="ki-duotone ki-arrow-up">
					<span class="path1"></span>
					<span class="path2"></span>
				</i>
			</div>
			<!--end::Scrolltop-->
			<!--begin::Javascript-->
			<script>var hostUrl = "{{asset('marble/src/')}}/";</script>

			<!--begin::Global Javascript Bundle(mandatory for all pages)-->
			<script src="{{asset('marble/src/')}}/plugins/global/plugins.bundle.js"></script>
			<script src="{{asset('marble/src/')}}/js/scripts.bundle.js"></script>
			@livewireScripts
			<script type="text/javascript">

			Livewire.on('searchInputFocus', () => {
				var searchInput = document.getElementById("search");
				searchInput.focus();
			});

			</script>
			<script>
				$(document).ready(function () {
					// Handle the selection change event
					$('#branches').on('change', function () {
						var selectedOption = $(this).find('option:selected');
						var selectedUrl = selectedOption.data('url');

						if (selectedUrl) {
							window.location.href = selectedUrl;
						}
					});

					// Browser history management for POS tabs
					const PosTabHistory = {
						validTabs: ['cashier', 'invoices', 'fulfillments'],
						currentTab: 'cashier',

						init() {
							// Get initial tab from URL or default to cashier
							this.currentTab = this.getTabFromUrl() || 'cashier';

							// Set initial tab without adding to history
							this.setActiveTabSilent(this.currentTab);

							// Listen for browser back/forward button
							window.addEventListener('popstate', (event) => {
								const tab = event.state?.tab || this.getTabFromUrl() || 'cashier';
								this.setActiveTabSilent(tab);
							});

							// Handle tab clicks
							this.bindTabClickHandlers();
						},

						getTabFromUrl() {
							// Check URL parameters first
							const urlParams = new URLSearchParams(window.location.search);
							const tabParam = urlParams.get('tab');
							if (this.validTabs.includes(tabParam)) {
								return tabParam;
							}

							// Fallback to hash for backward compatibility
							const hash = window.location.hash.substring(1);
							if (this.validTabs.includes(hash)) {
								// Migrate hash to URL parameter
								const url = new URL(window.location);
								url.searchParams.set('tab', hash);
								url.hash = '';
								history.replaceState({ tab: hash }, '', url.toString());
								return hash;
							}

							return null;
						},

						setActiveTab(tab) {
							if (!this.validTabs.includes(tab) || tab === this.currentTab) {
								return;
							}

							this.currentTab = tab;

							// Update URL with history entry
							const url = new URL(window.location);
							url.searchParams.set('tab', tab);
							history.pushState({ tab: tab }, '', url.toString());

							// Update Livewire component
							Livewire.dispatch('setActiveTabFromHistory', { tabName: tab });
						},

						setActiveTabSilent(tab) {
							if (!this.validTabs.includes(tab)) {
								return;
							}

							this.currentTab = tab;

							// Update URL without adding history entry
							const url = new URL(window.location);
							url.searchParams.set('tab', tab);
							history.replaceState({ tab: tab }, '', url.toString());

							// Update Livewire component
							Livewire.dispatch('setActiveTabFromHistory', { tabName: tab });
						},

						bindTabClickHandlers() {
							// Override tab link clicks to use history management
							document.querySelectorAll('.nav-link[data-tab]').forEach(link => {
								// Remove existing listeners to prevent duplicates
								link.removeEventListener('click', this.handleTabClick);
								// Add new listener
								link.addEventListener('click', this.handleTabClick.bind(this));
							});
						},

						handleTabClick(e) {
							e.preventDefault();
							const tab = e.currentTarget.getAttribute('data-tab');
							this.setActiveTab(tab);
						}
					};

					// Initialize tab history management
					PosTabHistory.init();

					// Rebind handlers after Livewire updates
					document.addEventListener('livewire:navigated', () => {
						PosTabHistory.bindTabClickHandlers();
					});

					// Expose for external use
					window.PosTabHistory = PosTabHistory;
				});

			</script>

			<!-- Invoice Details Modal -->
			<div wire:ignore.self class="modal fade" id="invoice_details_modal" tabindex="-1" aria-hidden="true">
				<div class="modal-dialog modal-dialog-centered modal-xl">
					<div class="modal-content">
						<div class="modal-header">
							<h3 class="modal-title">{{__('messages.invoice_details')}}</h3>
							<!--begin::Close-->
							<div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
								<i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
							</div>
							<!--end::Close-->
						</div>
						<div>
							@if($activeTab == 'invoices' && isset($selectedInvoiceId))
								@livewire('pos-invoice-details', ['invoiceId' => $selectedInvoiceId], key('invoice-details-' . $selectedInvoiceId))
							@else
								@livewire('pos-invoice-details', [], key('invoice-details-empty'))
							@endif
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.close')}}</button>
						</div>
					</div>
				</div>
			</div>
			<!-- End Invoice Details Modal -->

			<script>
				document.addEventListener('livewire:initialized', () => {
					// Handle opening the invoice modal
					Livewire.on('open-invoice-modal', (data) => {
						console.log('Received open-invoice-modal event with data:', data);
						const invoiceId = data.invoiceId;
						console.log('Invoice ID:', invoiceId);

						// Open the modal - the component will be loaded with the correct invoice ID
						console.log('Opening modal');
						const modal = new bootstrap.Modal(document.getElementById('invoice_details_modal'));
						modal.show();
					});

					// Handle closing the invoice modal
					Livewire.on('close-invoice-modal', () => {
						const modal = bootstrap.Modal.getInstance(document.getElementById('invoice_details_modal'));
						if (modal) {
							modal.hide();
						}
					});

					// Handle switching to cashier tab from return functionality
					Livewire.on('switch-to-cashier-tab', () => {
						console.log('Switching to cashier tab from return');
						if (window.PosTabHistory) {
							window.PosTabHistory.setActiveTab('cashier');
						}
					});

					// Rebind tab handlers after Livewire component updates
					Livewire.hook('morph.updated', () => {
						if (window.PosTabHistory) {
							window.PosTabHistory.bindTabClickHandlers();
						}
					});
				});
			</script>
		</body>
		<!--end::Body-->
	</html>
</div>
