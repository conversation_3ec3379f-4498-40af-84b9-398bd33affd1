<div>
    @if (session('success'))
        <div class="alert alert-success">
            <ul>
                <li>{{ session('success') }}</li>
            </ul>
        </div>
    @endif

    <div class="card card-flush">
        <!--begin::Card header-->
        <div class="card-header align-items-center py-5 gap-2 gap-md-5">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1">
                    <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                        <i class="path1"></i>
                        <i class="path2"></i>
                    </i>
                    <input type="text" wire:model.live.debounce.300ms="search" class="form-control form-control-solid w-250px ps-12" placeholder="{{__('messages.search_promotions')}}">
                </div>
                <!--end::Search-->
            </div>
            <!--begin::Card title-->
            <!--begin::Card toolbar-->
            <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
                <!--begin::Add promotion-->
                <a href="{{ route('promotions.add') }}" class="btn btn-primary">
                    <i class="ki-duotone ki-plus fs-2"></i>{{__('messages.add_promotion')}}
                </a>
                <!--end::Add promotion-->
            </div>
            <!--end::Card toolbar-->
        </div>
        <!--end::Card header-->
        <!--begin::Card body-->
        <div class="card-body pt-0">
            <!--begin::Table-->
            <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_promotions_table">
                <thead>
                    <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                        <th class="min-w-125px">{{__('messages.promotion_name')}}</th>
                        <th class="min-w-125px">{{__('messages.promotion_type')}}</th>
                        <th class="min-w-150px">{{__('messages.categories_tags')}}</th>
                        <th class="min-w-125px">{{__('messages.date_range')}}</th>
                        <th class="min-w-125px">{{__('messages.branches')}}</th>
                        <th class="min-w-125px">{{__('messages.status')}}</th>
                        <th class="text-end min-w-100px">{{__('messages.actions')}}</th>
                    </tr>
                </thead>
                <tbody class="text-gray-600 fw-semibold">
                    @foreach($promotions as $promotion)
                    <tr>
                        <td>
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center mb-1">
                                    <a href="{{ route('promotions.detail', $promotion->id) }}" class="text-gray-800 text-hover-primary">{{ $promotion->name }}</a>
                                    <a href="{{ route('promotions.detail', $promotion->id) }}" class="ms-1">
                                        <i class="ki-duotone ki-eye fs-7 text-muted"></i>
                                    </a>
                                </div>
                                @if($promotion->discount_percentage)
                                <span class="text-muted fs-7">{{__('messages.discount')}}: {{ $promotion->discount_percentage }}%</span>
                                @endif
                            </div>
                        </td>
                        <td>
                            @if($promotion->type === \App\Enums\Application\PromotionTypeEnum::DISCOUNT->value)
                                <div class="badge badge-light-primary">{{__('messages.discount')}}</div>
                            @elseif($promotion->type === \App\Enums\Application\PromotionTypeEnum::BUY_X_GET_Y->value)
                                <div class="d-flex flex-column">
                                    <div class="badge badge-light-info">{{__('messages.buy_x_get_y')}}</div>
                                    @if($promotion->get_y_type === \App\Enums\Application\PromotionDiscountTypeEnum::FREE->value)
                                        <span class="text-muted fs-7 mt-1">{{__('messages.free_product')}}</span>
                                    @else
                                        <span class="text-muted fs-7 mt-1">{{__('messages.discount')}}: {{ $promotion->get_y_discount_percentage }}%</span>
                                    @endif
                                </div>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                @if($promotion->type === \App\Enums\Application\PromotionTypeEnum::BUY_X_GET_Y->value)
                                    <!-- Buy X Categories -->
                                    @if($promotion->buyXCategories->count() > 0)
                                    <div class="mb-2">
                                        <span class="fw-bold text-primary">{{__('messages.buy_x')}} {{__('messages.categories')}}:</span>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($promotion->buyXCategories->take(3) as $category)
                                            <span class="badge badge-light-primary">{{ $category->name }}</span>
                                            @endforeach
                                            @if($promotion->buyXCategories->count() > 3)
                                            <span class="badge badge-light">+{{ $promotion->buyXCategories->count() - 3 }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Buy X Tags -->
                                    @if($promotion->buyXTags->count() > 0)
                                    <div class="mb-2">
                                        <span class="fw-bold text-primary">{{__('messages.buy_x')}} {{__('messages.tags')}}:</span>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($promotion->buyXTags->take(3) as $tag)
                                            <span class="badge badge-light-primary">{{ $tag->name }}</span>
                                            @endforeach
                                            @if($promotion->buyXTags->count() > 3)
                                            <span class="badge badge-light">+{{ $promotion->buyXTags->count() - 3 }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Get Y Categories -->
                                    @if($promotion->getYCategories->count() > 0)
                                    <div class="mb-2">
                                        <span class="fw-bold text-success">{{__('messages.get_y')}} {{__('messages.categories')}}:</span>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($promotion->getYCategories->take(3) as $category)
                                            <span class="badge badge-light-success">{{ $category->name }}</span>
                                            @endforeach
                                            @if($promotion->getYCategories->count() > 3)
                                            <span class="badge badge-light">+{{ $promotion->getYCategories->count() - 3 }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Get Y Tags -->
                                    @if($promotion->getYTags->count() > 0)
                                    <div class="mb-2">
                                        <span class="fw-bold text-success">{{__('messages.get_y')}} {{__('messages.tags')}}:</span>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($promotion->getYTags->take(3) as $tag)
                                            <span class="badge badge-light-success">{{ $tag->name }}</span>
                                            @endforeach
                                            @if($promotion->getYTags->count() > 3)
                                            <span class="badge badge-light">+{{ $promotion->getYTags->count() - 3 }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif
                                @else
                                    <!-- Regular Categories -->
                                    @if($promotion->categories->count() > 0)
                                    <div class="mb-2">
                                        <span class="fw-bold">{{__('messages.categories')}}:</span>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($promotion->categories->take(3) as $category)
                                            <span class="badge badge-light-primary">{{ $category->name }}</span>
                                            @endforeach
                                            @if($promotion->categories->count() > 3)
                                            <span class="badge badge-light">+{{ $promotion->categories->count() - 3 }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Regular Tags -->
                                    @if($promotion->tags->count() > 0)
                                    <div>
                                        <span class="fw-bold">{{__('messages.tags')}}:</span>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($promotion->tags->take(3) as $tag)
                                            <span class="badge badge-light-primary">{{ $tag->name }}</span>
                                            @endforeach
                                            @if($promotion->tags->count() > 3)
                                            <span class="badge badge-light">+{{ $promotion->tags->count() - 3 }}</span>
                                            @endif
                                        </div>
                                    </div>
                                    @endif
                                @endif
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <span>{{ $promotion->start_date->format('Y-m-d') }}</span>
                                <span class="text-muted fs-7">{{__('messages.to')}}</span>
                                <span>{{ $promotion->end_date->format('Y-m-d') }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                @foreach($promotion->branches->take(2) as $branch)
                                <span>{{ $branch->branch_name }}</span>
                                @endforeach
                                @if($promotion->branches->count() > 2)
                                <span class="text-muted fs-7">+{{ $promotion->branches->count() - 2 }} {{__('messages.more')}}</span>
                                @endif
                            </div>
                        </td>
                        <td>
                            @if($promotion->status === \App\Enums\Application\PromotionStatusEnum::ACTIVE->value)
                                <div class="badge badge-light-success">{{__('messages.active')}}</div>
                                @if($promotion->isActive())
                                    <div class="badge badge-light-primary">{{__('messages.running')}}</div>
                                @else
                                    @if($promotion->start_date > now())
                                        <div class="badge badge-light-warning">
                                            {{__('messages.upcoming')}}
                                        </div>
                                    @else
                                        <div class="badge badge-light-danger">
                                            {{__('messages.expired')}}
                                        </div>
                                    @endif
                                @endif
                            @else
                                <div class="badge badge-light-danger">{{__('messages.inactive')}}</div>
                            @endif
                        </td>
                        <td class="text-end">
                            <div class="dropdown">
                                <button class="btn btn-light btn-active-light-primary btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    {{__('messages.actions')}}
                                    <i class="ki-duotone ki-down fs-5 ms-1"></i>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                    <li><a class="dropdown-item" href="{{ route('promotions.detail', $promotion->id) }}">{{__('messages.view')}}</a></li>
                                    <li><a class="dropdown-item" href="{{ route('promotions.edit', $promotion->id) }}">{{__('messages.edit')}}</a></li>
                                    <li>
                                        <button wire:click="delete({{ $promotion->id }})" class="dropdown-item text-danger" onclick="return confirm('{{ __('messages.confirm_delete') }}');">
                                            {{__('messages.delete')}}
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    @endforeach

                    @if($promotions->isEmpty())
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <div class="d-flex flex-column align-items-center">
                                <i class="ki-duotone ki-discount fs-5x text-muted mb-5">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <div class="fw-bold fs-3 text-muted mb-5">{{__('messages.no_promotions_found')}}</div>
                                <a href="{{ route('promotions.add') }}" class="btn btn-primary">
                                    <i class="ki-duotone ki-plus fs-2"></i>{{__('messages.add_promotion')}}
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endif
                </tbody>
            </table>
            <!--end::Table-->

            <!--begin::Pagination-->
            <div class="d-flex justify-content-center">
                {{$promotions->links('livewire.pagination')}}
            </div>
            <!--end::Pagination-->
        </div>
        <!--end::Card body-->
    </div>
</div>
