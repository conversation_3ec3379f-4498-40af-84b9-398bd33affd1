<div>
    <!-- Hidden input to preserve variants data on form submission -->
    @if(is_array($variants) && !empty($variants))
        @foreach($variants as $index => $variant)
            @if(isset($variant['variant_id']) && !empty($variant['variant_id']))
                <input type="hidden" name="variants[{{$index}}][variant_id]" value="{{$variant['variant_id']}}">
                @if(isset($variant['options']) && is_array($variant['options']))
                    @foreach($variant['options'] as $optionIndex => $optionId)
                        <input type="hidden" name="variants[{{$index}}][options][{{$optionIndex}}]" value="{{$optionId}}">
                    @endforeach
                @endif
            @endif
        @endforeach
    @endif
    <div class="card card-flush py-4">
        <div class="card-header">
            <div class="card-title">
                <h2>{{__('messages.variants')}}</h2>
            </div>
            <div class="card-toolbar">
                <button type="button" wire:click="addVariant" class="btn btn-sm btn-primary">
                    <i class="ki-duotone ki-plus fs-2"></i>
                    {{__('messages.add_variant')}}
                </button>
            </div>
        </div>
        <div class="card-body pt-0">
            <!-- Variant instructions -->
            <div class="alert alert-light-primary mb-5">
                <div class="d-flex align-items-center">
                    <span class="svg-icon svg-icon-2hx svg-icon-primary me-3">
                        <i class="ki-duotone ki-information-5 fs-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                    </span>
                    <div class="d-flex flex-column">
                        <h4 class="mb-1 text-dark">{{__('messages.variant_instructions')}}</h4>
                        <span>{{__('messages.variant_instructions_desc')}}</span>
                    </div>
                </div>
            </div>

            <style>
                .variant-help-message {
                    font-size: 12px;
                    color: #f1416c;
                    margin-top: 4px;
                    font-style: italic;
                    padding-left: 5px;
                }

                /* Select2 dropdown styling */
                .select2-dropdown-large {
                    min-width: 250px !important;
                    z-index: 9999 !important;
                }

                .select2-container--open .select2-dropdown {
                    z-index: 9999 !important;
                }

                .select2-container--bootstrap5 .select2-dropdown {
                    border: 1px solid #e4e6ef;
                    box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
                }

                .option-remove-icon {
                    cursor: pointer;
                    margin-left: 5px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                }

                .option-remove-icon i {
                    font-size: 0.85rem !important;
                    width: 16px;
                    height: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .option-remove-icon:hover {
                    color: #f1416c;
                    background-color: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                }

                .badge .ki-duotone.ki-cross {
                    cursor: pointer;
                    font-size: 0.85rem !important;
                    margin-left: 3px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 16px;
                    height: 16px;
                }

                .badge .ki-duotone.ki-cross:hover {
                    color: #f1416c;
                    background-color: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                }

                /* Add button styling next to select */
                .input-group-text.bg-light {
                    background-color: #f5f8fa !important;
                    border-color: #e4e6ef;
                }

                .input-group-text.bg-light-primary {
                    background-color: #f1faff !important;
                    border-color: #d9f1ff;
                    color: #009ef7;
                }

                .input-group-text .btn-icon {
                    width: 22px;
                    height: 22px;
                }

                .badge.badge-light-primary {
                    display: inline-flex;
                    align-items: center;
                    padding: 0.25rem 0.5rem;
                    margin-right: 0.25rem;
                    margin-bottom: 0.25rem;
                }

                /* Better spacing for option input groups */
                .input-group {
                    margin-bottom: 8px;
                }

                /* Remove margin from last input group */
                .input-group:last-child {
                    margin-bottom: 0;
                }

                /* Full width for option containers */
                .d-flex.align-items-start.flex-column.gap-2 {
                    width: 100%;
                }

                /* Table column widths */
                table th.min-w-125px, table td.min-w-125px {
                    min-width: 125px !important;
                    width: 25%;
                }

                table th.min-w-250px, table td.min-w-250px {
                    min-width: 250px !important;
                    width: 65%;
                }

                table th.min-w-70px, table td.min-w-70px {
                    min-width: 70px !important;
                    width: 10%;
                }

                /* Hide any debug text */
                .d-none {
                    display: none !important;
                }
            </style>

            @if(!empty($variants))
                <div class="table-responsive">
                    <table class="table align-middle table-row-dashed fs-6 gy-5">
                        <thead>
                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                <th class="min-w-125px">{{__('messages.variant_name')}}</th>
                                <th class="min-w-250px">{{__('messages.options')}}</th>
                                <th class="text-end min-w-70px">{{__('messages.actions')}}</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-600 fw-semibold">
                            @foreach(is_array($variants) ? $variants : [] as $index => $variant)
                                <tr>
                                    <td class="min-w-125px">
                                        <div class="d-flex flex-column gap-2">
                                            <div>
                                                <select class="form-select form-select-solid variant-select"
                                                        id="variant-select-{{$index}}"
                                                        data-index="{{$index}}"
                                                        data-control="select2"
                                                        data-placeholder="{{__('messages.select_variant')}}">
                                                    <option value="">{{__('messages.select_variant')}}</option>
                                                    @foreach($availableVariants as $availableVariant)
                                                        @if(!$this->isVariantSelected($availableVariant->id, $index))
                                                            <option value="{{ $availableVariant->id }}" {{ isset($variant['variant_id']) && $variant['variant_id'] == $availableVariant->id ? 'selected' : '' }}>{{ $availableVariant->name }}</option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                            <span class="text-danger variant-help-message"
                                                  style="{{ isset($variant['variant_id']) && !empty($variant['variant_id']) ? 'display:none;' : '' }}">
                                                {{__('messages.select_variant')}}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="min-w-250px">
                                        <div class="d-flex flex-column gap-2">
                                            @if(isset($variant['variant_id']) && isset($availableOptions[$variant['variant_id']]))
                                                <div class="d-flex align-items-start flex-column gap-2">
                                                    <!-- Option Selection Row -->
                                                    <div class="d-flex align-items-center gap-2 w-100">
                                                        <div class="flex-grow-1">
                                                            <div class="d-flex gap-2">
                                                                <div class="input-group" style="width: 60%;">
                                                                    <div>
                                                                        <select class="form-select form-select-solid option-select"
                                                                                id="option-select-{{$index}}"
                                                                                data-index="{{$index}}"
                                                                                data-variant-id="{{$variant['variant_id']}}"
                                                                                data-control="select2"
                                                                                data-placeholder="{{__('messages.select_option')}}">
                                                                            <option value="">{{__('messages.select_option')}}</option>
                                                                            @foreach($availableOptions[$variant['variant_id']] as $option)
                                                                                @if(!$this->isOptionSelected($index, $option->id))
                                                                                    <option value="{{ $option->id }}">{{ $option->name }}</option>
                                                                                @endif
                                                                            @endforeach
                                                                        </select>
                                                                    </div>
                                                                </div>


                                                            </div>

                                                            <span class="text-danger variant-help-message mt-1"
                                                                style="{{ isset($variant['options']) && !empty($variant['options']) ? 'display:none;' : '' }}">
                                                                {{__('messages.select_option')}}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <!-- Selected Options Display -->
                                                    @if(isset($variant['options']) && !empty($variant['options']))
                                                        <div class="d-flex flex-wrap gap-2 align-items-center mt-2 w-100">
                                                            <div class="d-flex flex-wrap gap-1 flex-grow-1">
                                                                @foreach($variant['options'] as $optionId)
                                                                    @php
                                                                        $option = $availableOptions[$variant['variant_id']]->firstWhere('id', $optionId);
                                                                    @endphp
                                                                    @if($option)
                                                                        <span class="badge badge-light-primary">
                                                                            {{ $option->name }}
                                                                            <span class="option-remove-icon" wire:click="removeOption({{$index}}, {{$optionId}})">
                                                                                <i class="ki-duotone ki-cross"><span class="path1"></span><span class="path2"></span></i>
                                                                            </span>
                                                                        </span>
                                                                    @endif
                                                                @endforeach
                                                            </div>

                                                            <!-- Only show "unselect all" if there are multiple options -->
                                                            @if(count($variant['options']) > 1)
                                                                <button type="button"
                                                                        wire:click="unselectAllOptions({{$index}})"
                                                                        class="btn btn-sm btn-light-danger">
                                                                    {{__('messages.unselect_all')}}
                                                                </button>
                                                            @endif
                                                        </div>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="text-end">
                                        <button type="button"
                                            wire:click="removeVariant({{$index}})"
                                            class="btn btn-icon btn-sm btn-light-danger">
                                            <i class="ki-duotone ki-trash fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                            <input type="hidden" name="variants" value="{{ json_encode($variants) }}">"
                        </tbody>
                    </table>
                </div>

                @if($showGeneratedProducts && !empty($generatedProducts))
                    <div class="mt-10">
                        <div class="d-flex justify-content-between align-items-center mb-5">
                            <h3 class="fs-4 fw-bold">{{__('messages.generated_products')}}</h3>
                            <div class="d-flex align-items-center gap-3">
                                @if(!empty($branches) && !$showAllBranches)
                                <div class="w-200px">
                                    <select class="form-select" id="branch-select" wire:model.live="selectedBranch">
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch['id'] }}">{{ $branch['branch_name'] }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                @endif
                                <button type="button"
                                    class="btn btn-icon btn-light-primary"
                                    wire:click="toggleView"
                                    title="{{ $showAllBranches ? __('messages.show_single_branch') : __('messages.show_all_branches') }}">
                                    <i class="ki-duotone {{ $showAllBranches ? 'ki-up-down' : 'ki-up-down' }} fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                </button>
                            </div>
                        </div>

                        @if($showAllBranches)
                            <!-- All Branches View -->
                            @foreach($branches as $branch)
                                <div class="mb-10">
                                    <h3 class="fs-4 fw-bold mb-5">{{ $branch['branch_name'] }}</h3>
                                    <div class="table-responsive">
                                        <table class="table align-middle table-row-dashed fs-6 gy-5">
                                            <thead>
                                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                                    <th class="min-w-125px">{{__('messages.product_name')}}</th>
                                                    <th class="min-w-125px">{{__('messages.barcode')}}</th>
                                                    <th class="min-w-125px">{{__('messages.quantity')}}</th>
                                                    <th class="min-w-125px">{{__('messages.cost')}}</th>
                                                    <th class="min-w-125px">{{__('messages.sell_price')}}</th>
                                                    <th class="min-w-125px">{{__('messages.wholesale_price')}}</th>
                                                    <th class="text-end min-w-100px">{{__('messages.actions')}}</th>
                                                </tr>
                                            </thead>
                                            <tbody class="text-gray-600 fw-semibold">
                                                @foreach($generatedProducts as $index => $product)
                                                    <tr>
                                                        <td>{{ $product['name'] }}
                                                            <input type="hidden"
                                                                   wire:model.live="generatedProducts[{{$index}}][product_name]"
                                                                   name="generatedProducts[{{$index}}][product_name]"
                                                                   value="{{ $product['name'] }}">
                                                            <!-- Add hidden inputs for variant and option IDs as JSON strings -->
                                                            <input type="hidden"
                                                                   wire:model.live="generatedProducts[{{$index}}][variant_id]"
                                                                   name="generatedProducts[{{$index}}][variant_id]"
                                                                   value="{{ json_encode($product['variant_id']) }}">
                                                            <input type="hidden"
                                                                   wire:model.live="generatedProducts[{{$index}}][variant_option_id]"
                                                                   name="generatedProducts[{{$index}}][variant_option_id]"
                                                                   value="{{ json_encode($product['variant_option_id']) }}">
                                                        </td>
                                                        <td>
                                                            <input type="text"
                                                                class="form-control form-control-solid"
                                                                wire:model="generatedProducts.{{$index}}.barcode"
                                                                wire:change="updateBarcode($event.target.value, '{{$index}}.barcode')"
                                                                placeholder="{{__('messages.barcode')}}"
                                                                name="generatedProducts[{{$index}}][barcode]"
                                                            />
                                                        </td>
                                                        <td>
                                                            <input type="text"
                                                                class="form-control form-control-solid"
                                                                wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.quantity"
                                                                placeholder="{{__('messages.quantity')}}"
                                                                name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][quantity]"

                                                            />
                                                        </td>
                                                        <td>
                                                            <input type="text"
                                                                class="form-control form-control-solid"
                                                                wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.cost"
                                                                wire:input="updateCost($event.target.value, '{{$index}}.branch_values.{{$branch['id']}}.cost')"
                                                                placeholder="{{__('messages.cost')}}"
                                                                name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][cost]"
                                                            />
                                                        </td>
                                                        <td>
                                                            <input type="text"
                                                                class="form-control form-control-solid"
                                                                wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.sell_price"
                                                                placeholder="{{__('messages.sell_price')}}"
                                                                name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][sell_price]"

                                                            />
                                                        </td>
                                                        <td>
                                                            <input type="text"
                                                                class="form-control form-control-solid"
                                                                wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.wholesale_price"
                                                                placeholder="{{__('messages.wholesale_price')}}"
                                                                name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][wholesale_price]"

                                                            />
                                                        </td>
                                                        <td class="text-end">
                                                            @if($index > 0)
                                                                <button type="button"
                                                                    wire:click="copyFromFirst({{$index}})"
                                                                    class="btn btn-icon btn-sm btn-light-primary"
                                                                    title="{{__('messages.copy_from_first')}}">
                                                                    <i class="ki-duotone ki-copy fs-2"></i>
                                                                </button>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <!-- Single Branch View -->
                            <div class="table-responsive">
                                <table class="table align-middle table-row-dashed fs-6 gy-5">
                                    <thead>
                                        <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                            <th class="min-w-125px">{{__('messages.product_name')}}</th>
                                            <th class="min-w-125px">{{__('messages.barcode')}}</th>
                                            @foreach($branches as $branch)
                                                <th class="min-w-125px branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                    {{__('messages.quantity')}}
                                                </th>
                                                <th class="min-w-125px branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                    {{__('messages.cost')}}
                                                </th>
                                                <th class="min-w-125px branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                    {{__('messages.sell_price')}}
                                                </th>
                                                <th class="min-w-125px branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                    {{__('messages.wholesale_price')}}
                                                </th>
                                            @endforeach
                                            <th class="text-end min-w-100px">{{__('messages.actions')}}</th>
                                        </tr>
                                    </thead>
                                    <tbody class="text-gray-600 fw-semibold">
                                        @foreach($generatedProducts as $index => $product)
                                            <tr>
                                                <td>{{ $product['name'] }}
                                                    <input type="hidden"
                                                           wire:model.live="generatedProducts[{{$index}}][product_name]"
                                                           name="generatedProducts[{{$index}}][product_name]"
                                                           value="{{ $product['name'] }}">
                                                    <!-- Add hidden inputs for variant and option IDs as JSON strings -->
                                                    <input type="hidden"
                                                           wire:model.live="generatedProducts[{{$index}}][variant_id]"
                                                           name="generatedProducts[{{$index}}][variant_id]"
                                                           value="{{ json_encode($product['variant_id']) }}">
                                                    <input type="hidden"
                                                           wire:model.live="generatedProducts[{{$index}}][variant_option_id]"
                                                           name="generatedProducts[{{$index}}][variant_option_id]"
                                                           value="{{ json_encode($product['variant_option_id']) }}">
                                                </td>

                                                <td>
                                                    <input type="text"
                                                        class="form-control form-control-solid"
                                                        wire:model="generatedProducts.{{$index}}.barcode"
                                                        wire:change="updateBarcode($event.target.value, '{{$index}}.barcode')"
                                                        placeholder="{{__('messages.barcode')}}"
                                                        name="generatedProducts[{{$index}}][barcode]"
                                                    />
                                                </td>
                                                @foreach($branches as $branch)
                                                    <td class="branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                        <input type="text"
                                                            class="form-control form-control-solid"
                                                            wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.quantity"
                                                            placeholder="{{__('messages.quantity')}}"
                                                            name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][quantity]"
                                                            />
                                                    </td>
                                                    <td class="branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                        <input type="text"
                                                            class="form-control form-control-solid"
                                                            wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.cost"
                                                            wire:input="updateCost($event.target.value, '{{$index}}.branch_values.{{$branch['id']}}.cost')"
                                                            placeholder="{{__('messages.cost')}}"
                                                            name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][cost]"
                                                        />
                                                    </td>
                                                    <td class="branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                        <input type="text"
                                                            class="form-control form-control-solid"
                                                            wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.sell_price"
                                                            placeholder="{{__('messages.sell_price')}}"
                                                            name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][sell_price]"
                                                        />
                                                    </td>
                                                    <td class="branch-column {{ $selectedBranch == $branch['id'] ? '' : 'd-none' }}" data-branch="{{ $branch['id'] }}">
                                                        <input type="text"
                                                            class="form-control form-control-solid"
                                                            wire:model="generatedProducts.{{$index}}.branch_values.{{$branch['id']}}.wholesale_price"
                                                            placeholder="{{__('messages.wholesale_price')}}"
                                                            name="generatedProducts[{{$index}}][branch_values][{{$branch['id']}}][wholesale_price]"
                                                        />
                                                    </td>
                                                @endforeach
                                                <td class="text-end">
                                                    @if($index > 0)
                                                        <button type="button"
                                                            wire:click="copyFromFirst({{$index}})"
                                                            class="btn btn-icon btn-sm btn-light-primary"
                                                            title="{{__('messages.copy_from_first')}}">
                                                            <i class="ki-duotone ki-copy fs-2"></i>
                                                        </button>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                @endif
            @else
                <div class="card-body d-flex flex-column flex-center">
                    <div class="mb-2">
                        <div class="py-10 text-center">
                            <i class="ki-duotone ki-tag fs-7x text-primary"></i>
                        </div>
                        <h1 class="fw-semibold text-gray-800 text-center lh-lg">
                            {{__('messages.add_variants_msg')}}
                            <br />
                            <span class="fw-bolder">{{__('messages.add_variants_msg2')}}</span>
                        </h1>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
    document.addEventListener('livewire:initialized', function () {
        // Initialize Select2 for all variant and option selects only when page is fully loaded
        if (document.readyState === 'complete') {
            console.log('Page already loaded during Livewire initialization, initializing Select2');
            initializeSelect2();
        } else {
            console.log('Page not fully loaded during Livewire initialization, delaying Select2 initialization');
            window.addEventListener('load', function() {
                console.log('Window loaded after Livewire initialization, now initializing Select2');
                initializeSelect2();
            });
        }

        // Listen for variant added event
        Livewire.on('variantAdded', (params) => {
            console.log('Variant added event received:', params);

            // Only process if page is fully loaded
            if (isPageFullyLoaded()) {
                // Close all dropdowns
                const dropdowns = document.querySelectorAll('.dropdown-toggle');
                dropdowns.forEach(dropdown => {
                    bootstrap.Dropdown.getInstance(dropdown)?.hide();
                });

                // Clear the input
                @this.set('newVariantName', '');

                // We don't need to do anything else here since we're handling the update directly in the addNewVariant response
                // This event is kept for backward compatibility
                console.log('Variant added event handled');
            } else {
                console.log('Page not fully loaded during variant added event, delaying processing');
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        // Process the event when the page is loaded
                        const dropdowns = document.querySelectorAll('.dropdown-toggle');
                        dropdowns.forEach(dropdown => {
                            bootstrap.Dropdown.getInstance(dropdown)?.hide();
                        });
                        @this.set('newVariantName', '');
                    }
                }, 300);
            }
        });

        // Listen for option added event
        Livewire.on('optionAdded', (params) => {
            console.log('Option added event received:', params);

            // If an optionId and variantIndex were provided
            if (params.optionId && params.variantIndex !== undefined) {
                // Force a complete refresh of the component
                @this.call('$refresh').then(() => {
                    // Check if page is fully loaded before proceeding
                    if (isPageFullyLoaded()) {
                        processOptionAdded(params);
                    } else {
                        console.log('Page not fully loaded during option added event, delaying processing');
                        setTimeout(() => {
                            if (isPageFullyLoaded()) {
                                processOptionAdded(params);
                            } else {
                                // If still not loaded, try one more time with a longer delay
                                setTimeout(() => {
                                    processOptionAdded(params);
                                }, 500);
                            }
                        }, 300);
                    }
                });
            }
        });

        // Helper function to process option added event
        function processOptionAdded(params) {
            // Force a refresh of the option select for this variant
            const selectId = `option-select-${params.variantIndex}`;
            console.log('Looking for option select with ID:', selectId);

            const select = document.getElementById(selectId);
            if (select) {
                console.log('Found option select element, refreshing with optionId:', params.optionId);

                // Check if the option exists in the select
                let optionExists = false;
                select.querySelectorAll('option').forEach(option => {
                    if (option.value == params.optionId) {
                        optionExists = true;
                    }
                });

                if (!optionExists) {
                    // Add the option if it doesn't exist
                    console.log('Adding new option to select for option ID:', params.optionId);

                    // Find the option name
                    let optionName = 'New Option';
                    document.querySelectorAll('.option-select option').forEach(option => {
                        if (option.value == params.optionId) {
                            optionName = option.textContent;
                        }
                    });

                    const newOption = document.createElement('option');
                    newOption.value = params.optionId;
                    newOption.text = optionName;
                    select.appendChild(newOption);
                }

                refreshOptionSelect(params.variantIndex, params.optionId);

                // Scroll to the new option
                select.scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else {
                console.warn('Option select element not found:', selectId);
            }
        }

        // Listen for select new variant event
        Livewire.on('selectNewVariant', (params) => {
            if (params.variantId) {
                // Check if page is fully loaded
                if (isPageFullyLoaded()) {
                    processSelectNewVariant(params);
                } else {
                    console.log('Page not fully loaded during select new variant event, delaying processing');
                    setTimeout(() => {
                        if (isPageFullyLoaded()) {
                            processSelectNewVariant(params);
                        } else {
                            // If still not loaded, try one more time with a longer delay
                            setTimeout(() => {
                                processSelectNewVariant(params);
                            }, 400);
                        }
                    }, 300);
                }
            }
        });

        // Helper function to process select new variant event
        function processSelectNewVariant(params) {
            const variantSelects = document.querySelectorAll('.variant-select');
            for (let i = 0; i < variantSelects.length; i++) {
                const select = variantSelects[i];
                if (!select.value) {
                    // Select the new variant
                    select.value = params.variantId;
                    $(select).trigger('change');
                    break;
                }
            }
        }

        // Listen for select new option event
        Livewire.on('selectNewOption', (params) => {
            // Check if page is fully loaded
            if (isPageFullyLoaded()) {
                processSelectNewOption(params);
            } else {
                console.log('Page not fully loaded during select new option event, delaying processing');
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        processSelectNewOption(params);
                    } else {
                        // If still not loaded, try one more time with a longer delay
                        setTimeout(() => {
                            processSelectNewOption(params);
                        }, 400);
                    }
                }, 300);
            }
        });

        // Helper function to process select new option event
        function processSelectNewOption(params) {
            if (params.optionId && params.variantIndex !== undefined) {
                // Use the variant index directly
                refreshOptionSelect(params.variantIndex, params.optionId);
            } else if (params.optionId && params.variantId) {
                // For backward compatibility, find the variant index from the variant ID
                // Find the variant index from the variant ID
                @this.call('getVariantIndexById', params.variantId).then(variantIndex => {
                    if (variantIndex !== null) {
                        refreshOptionSelect(variantIndex, params.optionId);
                    }
                });
            }
        }
    });

    const errorAudio = new Audio("storage/error.mp3");
    const successAudio = new Audio("storage/success.mp3");
    window.addEventListener('show-toast', function(event) {
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": false,
            "progressBar": false,
            "positionClass": "toastr-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        var message = event.detail[0].message;
        var type = event.detail[0].type || 'error';

        if (type === 'error') {
            errorAudio.play();
            toastr.error(message, '{{__('messages.error')}}');
        } else if (type === 'success') {
            successAudio.play();
            toastr.success(message, '{{__('messages.success')}}');
        } else if (type === 'warning') {
            errorAudio.play();
            toastr.warning(message, '{{__('messages.warning')}}');
        } else if (type === 'info') {
            toastr.info(message, '{{__('messages.info')}}');
        }
    });

    // Function to initialize Select2 for all variant and option selects
    function initializeSelect2() {
        console.log('Initializing Select2 components');
        // Initialize variant selects
        $('.variant-select').each(function() {
            if ($(this).data('select2')) {
                $(this).select2('destroy');
            }

            const index = $(this).data('index');

            $(this).select2({
                dropdownParent: $('body'),
                width: '100%',
                tags: true,
                dropdownCssClass: 'select2-dropdown-large',
                createTag: function(params) {
                    return {
                        id: 'new:' + params.term,
                        text: params.term + ' ' + '{{__('messages.create_new')}}',
                        newTag: true
                    };
                }
            });

            // Listen for change event
            $(this).off('change').on('change', function(e) {
                const value = $(this).val();
                if (value) {
                    if (value.toString().startsWith('new:')) {
                        // This is a new variant, create it
                        const newVariantName = value.toString().substring(4);

                        // Clear the select value immediately to prevent multiple triggers
                        const selectElement = $(this);
                        selectElement.val('').trigger('change.select2');

                        // Only proceed if we have a valid variant name
                        if (newVariantName && newVariantName.trim() !== '') {
                            console.log('Creating new variant with name:', newVariantName);

                            // Set temporary value to prevent issues
                            @this.set('newVariantName', newVariantName);

                            // Show a loading indicator
                            const loadingToast = toastr.info('{{__('messages.creating_variant')}}...', '{{__('messages.please_wait')}}');

                            // Get the current index from the select element
                            const currentIndex = $(this).data('index');
                            console.log('Creating new variant with name:', newVariantName, 'for index:', currentIndex);

                            // Call Livewire method to create new variant with the current index
                            @this.call('addNewVariant', currentIndex)
                                .then(response => {
                                    console.log('New variant created with ID:', response);
                                    toastr.clear(loadingToast);

                                    if (response) {
                                        console.log('New variant created with ID:', response, 'for index:', currentIndex);

                                        // Directly update the select dropdown with the new variant
                                        const select = document.getElementById('variant-select-' + currentIndex);
                                        if (select) {
                                            // Check if the option already exists
                                            let optionExists = false;
                                            select.querySelectorAll('option').forEach(option => {
                                                if (option.value == response) {
                                                    optionExists = true;
                                                }
                                            });

                                            if (!optionExists) {
                                                // Add the new option to the select
                                                const newOption = document.createElement('option');
                                                newOption.value = response;
                                                newOption.text = newVariantName;
                                                select.appendChild(newOption);
                                            }

                                            // Select the new variant
                                            select.value = response;
                                            $(select).trigger('change');

                                            // Update Livewire property directly
                                            @this.set('variants.' + currentIndex + '.variant_id', response);
                                            // Call Livewire method
                                            @this.call('variantSelected', currentIndex, response);
                                        }
                                    } else {
                                        console.error('No response from addNewVariant');
                                        toastr.error('{{__('messages.error_creating_variant')}}', '{{__('messages.error')}}');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error creating variant:', error);
                                    toastr.clear(loadingToast);
                                    toastr.error('{{__('messages.error_creating_variant')}}', '{{__('messages.error')}}');
                                });
                        } else {
                            console.warn('Empty variant name, skipping creation');
                        }
                    } else {
                        // Update Livewire property
                        @this.set('variants.' + index + '.variant_id', value);
                        // Call Livewire method
                        @this.call('variantSelected', index, value);
                    }
                }
            });
        });

        // Initialize option selects
        $('.option-select').each(function() {
            if ($(this).data('select2')) {
                $(this).select2('destroy');
            }

            const index = $(this).data('index');

            $(this).select2({
                dropdownParent: $('body'),
                width: '100%',
                tags: true,
                dropdownCssClass: 'select2-dropdown-large',
                createTag: function(params) {
                    return {
                        id: 'new:' + params.term,
                        text: params.term + ' ' + '{{__('messages.create_new')}}',
                        newTag: true
                    };
                }
            });

            // Listen for change event
            $(this).off('change').on('change', function(e) {
                const value = $(this).val();
                if (value) {
                    if (value.toString().startsWith('new:')) {
                        // This is a new option, create it
                        const newOptionName = value.toString().substring(4);

                        // Clear the select value immediately to prevent multiple triggers
                        const selectElement = $(this);
                        selectElement.val('').trigger('change.select2');

                        // Only proceed if we have a valid option name
                        if (newOptionName && newOptionName.trim() !== '') {
                            console.log('Creating new option with name:', newOptionName, 'for variant index:', index);

                            // Set temporary value to prevent issues
                            @this.set('newOptionName', newOptionName);

                            // Show a loading indicator
                            const loadingToast = toastr.info('{{__('messages.creating_option')}}...', '{{__('messages.please_wait')}}');

                            // Call Livewire method to create new option using the variant index
                            @this.call('addNewOption', index)
                                .then(response => {
                                    console.log('New option created with ID:', response);
                                    toastr.clear(loadingToast);

                                    if (response) {
                                        // The optionAdded event will handle updating the dropdowns
                                        // Force a refresh of the options
                                        setTimeout(() => {
                                            // We use the index directly since we're now getting the variant ID from the backend
                                            refreshOptionSelect(index, response);
                                        }, 200);
                                    } else {
                                        console.error('No response from addNewOption');
                                        toastr.error('{{__('messages.error_creating_option')}}', '{{__('messages.error')}}');
                                    }
                                })
                                .catch(error => {
                                    console.error('Error creating option:', error);
                                    toastr.clear(loadingToast);
                                    toastr.error('{{__('messages.error_creating_option')}}', '{{__('messages.error')}}');
                                });
                        } else {
                            console.warn('Empty option name or missing variant ID, skipping creation');
                        }
                    } else {
                        // Update Livewire property
                        @this.set('variants.' + index + '.selected_option', value);
                        // Call Livewire method
                        @this.call('optionSelected', index, value);
                    }
                }
            });
        });
    }

    // Function to refresh the options in a select
    function refreshOptionSelect(index, optionIdToSelect) {
        // Only proceed if the page is fully loaded
        if (!isPageFullyLoaded()) {
            console.log('Page not fully loaded, delaying option select refresh');
            setTimeout(() => refreshOptionSelect(index, optionIdToSelect), 200);
            return;
        }

        console.log('Refreshing option select for index:', index, 'optionIdToSelect:', optionIdToSelect);

        // Get the variant ID from the backend using the index
        const variantId = @this.get('variants.' + index + '.variant_id');
        console.log('Got variant ID from backend:', variantId);

        if (!variantId) {
            console.error('No variant ID found for index:', index);
            return;
        }

        // First, force a reloading of available options from the backend
        @this.call('loadOptionsForVariant', variantId).then(availableOptions => {
            console.log('Available options for variant:', variantId, availableOptions);

            // Get the currently selected options
            @this.call('getSelectedOptions', index).then(selectedOptions => {
                console.log('Currently selected options for variant:', variantId, selectedOptions);

                setTimeout(() => {
                    const select = document.querySelector('#option-select-' + index);
                    if (select) {
                        console.log('Found option select element, rebuilding options');

                        // Store current value before destroying
                        const currentValue = select.value;

                        // Destroy Select2
                        if ($(select).data('select2')) {
                            $(select).select2('destroy');
                        }

                        // Clear all existing options
                        select.innerHTML = '';

                        // Add placeholder option
                        const placeholderOption = document.createElement('option');
                        placeholderOption.value = '';
                        placeholderOption.text = '{{__('messages.select_option')}}';
                        select.appendChild(placeholderOption);

                        // Convert selectedOptions to strings for comparison
                        const selectedOptionIds = selectedOptions ? selectedOptions.map(id => String(id)) : [];
                        console.log('Selected option IDs (as strings):', selectedOptionIds);

                        // Add available options that aren't already selected
                        availableOptions.forEach(option => {
                            const optionId = String(option.id);
                            const isSelected = selectedOptionIds.includes(optionId);
                            console.log(`Option ${option.name} (ID: ${optionId}) - isSelected: ${isSelected}`);

                            // Only add if not already selected
                            if (!isSelected) {
                                const optionEl = document.createElement('option');
                                optionEl.value = optionId;
                                optionEl.text = option.name;
                                select.appendChild(optionEl);
                            }
                        });

                        // Reinitialize Select2
                        $(select).select2({
                            dropdownParent: $('body'),
                            width: '100%',
                            tags: true,
                            dropdownCssClass: 'select2-dropdown-large',
                            createTag: function(params) {
                                return {
                                    id: 'new:' + params.term,
                                    text: params.term + ' ' + '{{__('messages.create_new')}}',
                                    newTag: true
                                };
                            }
                        });

                        // Add change handler
                        $(select).off('change').on('change', function(e) {
                            const value = $(this).val();
                            if (value) {
                                if (value.toString().startsWith('new:')) {
                                    // Create new option
                                    const newOptionName = value.toString().substring(4);
                                    $(select).val('').trigger('change.select2');

                                    if (newOptionName && newOptionName.trim() !== '') {
                                        @this.set('newOptionName', newOptionName);

                                        const loadingToast = toastr.info('{{__('messages.creating_option')}}...', '{{__('messages.please_wait')}}');

                                        @this.call('addNewOption', index)
                                            .then(response => {
                                                toastr.clear(loadingToast);
                                                if (!response) {
                                                    toastr.error('{{__('messages.error_creating_option')}}', '{{__('messages.error')}}');
                                                }
                                            })
                                            .catch(error => {
                                                toastr.clear(loadingToast);
                                                toastr.error('{{__('messages.error_creating_option')}}', '{{__('messages.error')}}');
                                            });
                                    }
                                } else {
                                    // Select existing option
                                    console.log('Selecting option ID:', value);
                                    @this.set('variants.' + index + '.selected_option', value);
                                    @this.call('optionSelected', index, value);
                                }
                            }
                        });

                        // Select specific option if provided
                        if (optionIdToSelect && select.querySelector(`option[value="${optionIdToSelect}"]`)) {
                            select.value = optionIdToSelect;
                            $(select).trigger('change');
                        } else if (currentValue && select.querySelector(`option[value="${currentValue}"]`)) {
                            // Restore previous value if it exists in the refreshed options
                            select.value = currentValue;
                            $(select).trigger('change');
                        }
                    } else {
                        console.error('Select element not found:', '#option-select-' + index);
                    }
                }, 200);
            });
        });
    }

    // Function to update help messages visibility based on variant index
    function updateHelpMessageVisibility(variantIndex) {
        // Find the variant row
        const variantRows = document.querySelectorAll('table tbody tr');
        if (variantIndex < variantRows.length) {
            const variantRow = variantRows[variantIndex];

            // Find the option badge container
            const badgeContainer = variantRow.querySelector('.d-flex.flex-wrap.gap-2');
            if (!badgeContainer) return;

            // Get the help message
            const helpMessage = variantRow.querySelector('span.variant-help-message.mt-1');
            if (!helpMessage) return;

            // Check if there are any badges (selected options)
            const hasBadges = badgeContainer.querySelectorAll('.badge').length > 0;

            // Show/hide help message based on badges
            helpMessage.style.display = hasBadges ? 'none' : 'inline-block';
        }
    }

    // Fix for the "Add" button UI message
    // Function to check if page is fully loaded
    function isPageFullyLoaded() {
        return document.readyState === 'complete';
    }

    // Function to safely initialize Select2 only when page is fully loaded
    function safeInitSelect2() {
        if (isPageFullyLoaded()) {
            console.log('Page fully loaded, initializing Select2');
            initializeSelect2();
        } else {
            console.log('Page not fully loaded yet, delaying Select2 initialization');
            setTimeout(safeInitSelect2, 100);
        }
    }

    // Use window.onload instead of DOMContentLoaded
    window.addEventListener('load', function() {
        console.log('Window load event fired');
        // Hide all help messages on page load for fields that already have values
        const variantSelects = document.querySelectorAll('.variant-select');
        variantSelects.forEach(select => {
            const helpMessage = select.closest('.d-flex').querySelector('.variant-help-message');
            if (helpMessage && select.value) {
                helpMessage.style.display = 'none';
            }
        });

        // Safely initialize Select2 after page is fully loaded
        safeInitSelect2();

        // Debounce function to limit how often a function can be called
        function debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    func.apply(context, args);
                }, wait);
            };
        }

        // Create a debounced version of initializeSelect2 that checks page load state
        const debouncedInitSelect2 = debounce(() => {
            if (isPageFullyLoaded()) {
                console.log('Page fully loaded, running debounced Select2 initialization');
                initializeSelect2();
            } else {
                console.log('Page not fully loaded during update, delaying Select2 initialization');
                setTimeout(() => debouncedInitSelect2(), 100);
            }
        }, 500);

        // Listen for Livewire updates to hide/show messages and reinitialize Select2
        document.addEventListener('livewire:update', function() {
            // For variant dropdowns
            document.querySelectorAll('.variant-select').forEach(select => {
                const helpMessage = select.closest('.d-flex').querySelector('.variant-help-message');
                if (helpMessage) {
                    helpMessage.style.display = select.value ? 'none' : 'inline-block';
                }
            });

            // For option dropdowns
            document.querySelectorAll('.d-flex.flex-wrap.gap-2').forEach(optionsContainer => {
                const variant = optionsContainer.closest('tr');
                if (variant) {
                    const optionsHelpMessage = variant.querySelector('span.variant-help-message.mt-1');
                    if (optionsHelpMessage && optionsContainer.querySelectorAll('.badge').length > 0) {
                        optionsHelpMessage.style.display = 'none';
                    }
                }
            });

            // Use debounced function instead of direct call
            debouncedInitSelect2();
        });

        // Listen for Livewire component updates
        Livewire.hook('morph.updated', ({ el, component }) => {
            // Use debounced function instead of direct call
            debouncedInitSelect2();

            // Check if we need to update any variant selects, but only if page is fully loaded
            if (isPageFullyLoaded()) {
                setTimeout(() => {
                    document.querySelectorAll('.variant-select').forEach(select => {
                        // Ensure the select has the latest options
                        if ($(select).data('select2')) {
                            const currentValue = select.value;
                            $(select).select2('destroy');
                            $(select).select2({
                                dropdownParent: $('body'),
                                width: '100%',
                                tags: true,
                                dropdownCssClass: 'select2-dropdown-large',
                                createTag: function(params) {
                                    return {
                                        id: 'new:' + params.term,
                                        text: params.term + ' ' + '{{__('messages.create_new')}}',
                                        newTag: true
                                    };
                                }
                            });

                            // Restore the value if it was set
                            if (currentValue) {
                                select.value = currentValue;
                                $(select).trigger('change.select2');
                            }
                        }
                    });
                }, 200); // Increased timeout for better reliability
            } else {
                console.log('Page not fully loaded during morph update, delaying Select2 updates');
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        debouncedInitSelect2();
                    }
                }, 300);
            }
        });

        // Listen for option events
        window.addEventListener('option-added', function(event) {
            // Only process if page is fully loaded
            if (isPageFullyLoaded()) {
                updateHelpMessageVisibility(event.detail.variantIndex);
            } else {
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        updateHelpMessageVisibility(event.detail.variantIndex);
                    }
                }, 200);
            }
        });

        window.addEventListener('option-removed', function(event) {
            // Only process if page is fully loaded
            if (isPageFullyLoaded()) {
                updateHelpMessageVisibility(event.detail.variantIndex);

                // Force refreshing the options dropdown when an option is removed
                if (event.detail.variantIndex !== undefined && event.detail.variantId) {
                    console.log('Option removed, refreshing options dropdown for variant', event.detail.variantId);
                    setTimeout(() => {
                        refreshOptionSelect(event.detail.variantIndex, event.detail.variantId);
                    }, 200);
                }
            } else {
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        updateHelpMessageVisibility(event.detail.variantIndex);
                        if (event.detail.variantIndex !== undefined && event.detail.variantId) {
                            refreshOptionSelect(event.detail.variantIndex, event.detail.variantId);
                        }
                    }
                }, 300);
            }
        });

        window.addEventListener('option-cleared', function(event) {
            // Only process if page is fully loaded
            if (isPageFullyLoaded()) {
                updateHelpMessageVisibility(event.detail.variantIndex);
            } else {
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        updateHelpMessageVisibility(event.detail.variantIndex);
                    }
                }, 200);
            }
        });

        window.addEventListener('options-unselected', function(event) {
            // Only process if page is fully loaded
            if (isPageFullyLoaded()) {
                updateHelpMessageVisibility(event.detail.variantIndex);

                // Force refreshing the options dropdown when all options are unselected
                if (event.detail.variantIndex !== undefined && event.detail.variantId) {
                    console.log('All options unselected, refreshing options dropdown for variant', event.detail.variantId);
                    setTimeout(() => {
                        refreshOptionSelect(event.detail.variantIndex, event.detail.variantId);
                    }, 200);
                }
            } else {
                setTimeout(() => {
                    if (isPageFullyLoaded()) {
                        updateHelpMessageVisibility(event.detail.variantIndex);
                        if (event.detail.variantIndex !== undefined && event.detail.variantId) {
                            refreshOptionSelect(event.detail.variantIndex, event.detail.variantId);
                        }
                    }
                }, 300);
            }
        });

        // Show help messages when user clicks add variant
        const addVariantBtn = document.querySelector('[wire\\:click="addVariant"]');
        if (addVariantBtn) {
            addVariantBtn.addEventListener('click', function() {
                // Only process if page is fully loaded
                if (isPageFullyLoaded()) {
                    processAddVariantClick();
                } else {
                    console.log('Page not fully loaded during add variant click, delaying processing');
                    setTimeout(() => {
                        if (isPageFullyLoaded()) {
                            processAddVariantClick();
                        } else {
                            // If still not loaded, try one more time with a longer delay
                            setTimeout(() => {
                                processAddVariantClick();
                            }, 400);
                        }
                    }, 300);
                }
            });
        }

        // Helper function to process add variant button click
        function processAddVariantClick() {
            setTimeout(() => {
                const emptySelects = document.querySelectorAll('select[wire\\:model*="variants"][wire\\:model*="variant_id"]:not([value])');
                emptySelects.forEach(select => {
                    const helpMessage = select.parentElement.querySelector('.variant-help-message');
                    if (helpMessage) {
                        helpMessage.style.display = 'inline-block';
                    }
                });
            }, 300);
        }
    });
</script>
