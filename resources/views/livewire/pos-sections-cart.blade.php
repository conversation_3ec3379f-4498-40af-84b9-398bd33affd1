<div class="col-12">
    <!--begin::Table container-->
    <div class="table-responsive y-container" style="max-height: 145px; overflow-y: auto">
        <!--begin::Table-->
        <table class="table align-middle gs-0 gy-1 my-0">
            <!--begin::Table body-->
            @if(count($cartProductSorted) > 0)
            <tbody>
                @foreach($cartProductSorted as $product)
                    <tr data-kt-pos-element="item">
                        <td class="pe-0 col-6">
                            <div class="d-flex align-items-center">
                                @if(isset($product['is_return']) && $product['is_return'])
                                    <span class="badge badge-light-danger me-2">{{ __('messages.returned') }}</span>
                                @endif
                            </div>
                            <div class="d-flex flex-column">
                                <span class="fw-bold text-gray-800 cursor-pointer text-hover-primary fs-6 me-1">{{$product['name']}}</span>
                                @if(isset($product['has_promotion']) && $product['has_promotion'])
                                    @if($product['promotion_type'] === \App\Enums\Application\PromotionTypeEnum::DISCOUNT->value)
                                        <span class="badge badge-light-primary">{{ $product['promotion_discount_percentage'] }}% {{__('messages.discount')}}</span>
                                    @elseif($product['promotion_type'] === \App\Enums\Application\PromotionTypeEnum::BUY_X_GET_Y->value)
                                        @if($product['promotion_get_y_type'] === \App\Enums\Application\PromotionDiscountTypeEnum::FREE->value)
                                            <span class="badge badge-light-success">{{__('messages.free')}} ({{ $product['promotion_free_quantity'] }})</span>
                                        @else
                                            <span class="badge badge-light-primary">{{ $product['promotion_discount_percentage'] }}% {{__('messages.discount')}} ({{ $product['promotion_discounted_quantity'] }})</span>
                                        @endif
                                    @endif
                                @endif
                            </div>
                        </td>
                        <td class="pe-0 col-4">
                            <!--begin::Dialer-->
                            <div class="position-relative d-flex align-items-center" data-kt-dialer="true" data-kt-dialer-min="1" data-kt-dialer-step="1" data-kt-dialer-decimals="0">
                                <!--begin::Increase control-->
                                <button type="button" class="btn btn-icon btn-sm btn-light btn-icon-gray-400" wire:click="increaseQuantity('{{$product['cart_id'] ?? $product['id']}}')" data-kt-dialer-control="increase">
                                    <i class="ki-duotone ki-plus fs-1"></i>
                                </button>
                                <!--end::Increase control-->

                                <!--begin::Input control-->
                                <input type="text" class="form-control border-0 text-center px-0 fs-4 fw-bold text-gray-800 w-30px" data-kt-dialer-control="input" placeholder="Quantity" name="manageBudget" readonly="readonly" value="{{isset($product['is_return']) && $product['is_return'] ? abs($product['cartQuantity']) : $product['cartQuantity']}}" />
                                <!--end::Input control-->
                                <!--begin::Decrease control-->
                                <button type="button" class="btn btn-icon btn-sm btn-light btn-icon-gray-400" wire:click="decreaseQuantity('{{$product['cart_id'] ?? $product['id']}}')" data-kt-dialer-control="decrease">
                                    <i class="ki-duotone ki-minus fs-1"></i>
                                </button>
                                <!--end::Decrease control-->
                            </div>
                            <!--end::Dialer-->
                        </td>
                        <td class="text-end col-2">
                            @if(isset($product['original_sell_price']) && isset($product['has_promotion']) && $product['has_promotion'])
                                <div class="d-flex flex-column align-items-end">
                                    <span class="fw-bold fs-4" data-kt-pos-element="item-total">SR{{$product['sell_price']}}</span>
                                    <div class="d-flex align-items-center">
                                        <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                        <span class="text-muted text-decoration-line-through fs-8">SR{{$product['original_sell_price']}}</span>
                                    </div>
                                </div>
                            @else
                            <span class="fw-bold fs-4 {{isset($product['is_return']) && $product['is_return'] ? 'text-danger' : ''}}" data-kt-pos-element="item-total">
                                SR{{isset($product['is_return']) && $product['is_return'] ? '-' : ''}}{{$product['sell_price']}}
                            </span>
                            @endif
                        </td>
                    </tr>
                @endforeach
            </tbody>
            @endif
            <!--end::Table body-->
        </table>
        <!--end::Table-->
    </div>
    <!--end::Table container-->
    <script>
    document.addEventListener("DOMContentLoaded", function() {
        // Get the scrollable container
        var container = document.querySelector('.y-container');

        // Scroll the container to the bottom
        container.scrollTop = container.scrollHeight;

        // Create a MutationObserver
        var observer = new MutationObserver(function(mutations) {
            // Scroll the container to the bottom whenever changes occur
            container.scrollTop = container.scrollHeight;
        });

        // Configure the MutationObserver to watch for changes in the child nodes
        var config = { childList: true, subtree: true };

        // Start observing the container
        observer.observe(container, config);
    });
    </script>

</div>
