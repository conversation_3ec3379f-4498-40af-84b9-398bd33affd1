<div>
    <form id='myForm' method="GET" action="{{url('products/export')}}">
        <!--begin::Card header-->
        <div class="card-header align-items-center py-5 gap-2 gap-md-5">
            <!--begin::Card title-->
            <div class="card-title">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative">
                    <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                        <i class="path1"></i>
                        <i class="path2"></i>
                    </i>
                    <input type="text" id="search" wire:model.live="search" class="form-control form-control-solid w-250px ps-12" placeholder="{{__('messages.search_product')}}">
                </div>
                <!--end::Search-->
                <div class="d-flex align-items-center ms-4">
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle" type="button" id="productTypeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            {{__('messages.create_product')}}
                        </button>
                        <ul class="dropdown-menu w-100" aria-labelledby="productTypeDropdown">
                            <li><a class="dropdown-item" href="{{asset('products/add')}}">{{__('messages.simple_product')}}</a></li>
                            <li><a class="dropdown-item" href="{{asset('products/composite/add')}}">{{__('messages.composite_product')}}</a></li>
                            <li><a class="dropdown-item" href="{{asset('products/variant/add')}}">{{__('messages.variant')}}</a></li>
                            <li><a class="dropdown-item" href="{{asset('products/weighted/add')}}">{{__('messages.weighted_product')}}</a></li>
                            <li><a class="dropdown-item" href="{{asset('products/service/add')}}">{{__('messages.service_product')}}</a></li>
                        </ul>
                    </div>
                </div>
                <!--begin::Group actions-->
            </div>
            <!--end::Card title-->
            <!--begin::Card toolbar-->

            <div class="card-toolbar flex-row-fluid justify-content-end gap-5">

                <div wire:ignore class="w-100 mw-150px">
                    <!--begin::Select2-->
                    <select class="form-select form-select-solid"  data-control="select2"  data-hide-search="true" data-placeholder="{{__('messages.status')}}" data-kt-ecommerce-product-filter='status' >
                        <option default value="all">{{__('messages.all')}}</option>
                        <option value="{{\App\Enums\Application\ProductStatus::ACTIVE->value}}">{{__('messages.active')}}</option>
                        <option value="{{\App\Enums\Application\ProductStatus::DISABLED->value}}">{{__('messages.disabled')}}</option>
                    </select>
                    <!--end::Select2-->
                </div>

                <div wire:ignore class="w-100 mw-150px">
                    <!--begin::Select2-->
                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="false" data-placeholder="{{__('messages.filter_by_tag')}}" data-kt-ecommerce-product-filter='tag'>
                        <option default value="all">{{__('messages.all_tags')}}</option>
                        @foreach($tags as $tag)
                            <option value="{{$tag->id}}">{{$tag->name}}</option>
                        @endforeach
                    </select>
                    <!--end::Select2-->
                </div>

                @if(!$categoryId)
                <div wire:ignore class="w-100 mw-150px">
                    <!--begin::Select2-->
                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="false" data-placeholder="{{__('messages.filter_by_category')}}" data-kt-ecommerce-product-filter='category'>
                        <option default value="all">{{__('messages.all_categories')}}</option>
                        @foreach($categories as $category)
                            <option value="{{$category->id}}">{{$category->name}}</option>
                        @endforeach
                    </select>
                    <!--end::Select2-->
                </div>
                @endif

                <div wire:ignore class="w-100 mw-150px">
                    <!--begin::Select2-->
                    <select class="form-select form-select-solid" data-control="select2" data-hide-search="false" data-placeholder="{{__('messages.filter_by_type')}}" data-kt-ecommerce-product-filter='type'>
                        <option default value="all">{{__('messages.all_types')}}</option>
                        @foreach($productTypes as $typeValue => $typeName)
                            <option value="{{$typeValue}}">{{$typeName}}</option>
                        @endforeach
                    </select>
                    <!--end::Select2-->
                </div>
                @if(!$categoryId)
                <div class="mw-150px">
                    <!--begin::Select2-->
                    <button type="submit" class="btn btn-success">{{__('messages.export')}}</button>
                    <!--end::Select2-->
                </div>
                <div class="mw-150px">
                    <!--begin::Select2-->
                    <a href="{{asset('products/import')}}" class="btn btn-primary">{{__('messages.import')}}</a>
                    <!--end::Select2-->
                </div>
                @endif
            </div>
            <!--end::Card toolbar-->

        </div>
    </form>

    <div class="card-body pt-0 table-responsive">
        <!--begin::Table-->
        @if(count($products) > 0)
        <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_products_table">
            <thead>
                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                    <th class="w-10px pe-2">
                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                            <input class="form-check-input" type="checkbox" data-kt-check="true" data-kt-check-target="#kt_ecommerce_products_table .form-check-input" value="1" />
                        </div>
                    </th>
                    @if(__('messages.lang') == 'ar')
                        <th class="text-start min-w-100px" style="padding-right: 26px">{{__('messages.product')}}</th>
                    @else
                        <th class="min-w-200px">{{__('messages.product')}}</th>
                    @endif
                    <th class="text-end min-w-100px">{{__('messages.barcode')}}</th>
                    <th class="text-end min-w-100px">{{__('messages.type')}}</th>
                    <th class="text-end min-w-70px">{{__('messages.qty')}}</th>
                    <th class="text-end min-w-100px" id="priceColumnHeader">{{__('messages.price')}}</th>
                    <th class="text-end min-w-100px">{{__('messages.status')}}</th>
                    <th class="text-end min-w-70px">{{__('messages.actions')}}</th>
                </tr>
            </thead>
            <tbody class="fw-semibold text-gray-600">
                @foreach($products as $product)
                <tr>
                    <td>
                        <div class="form-check form-check-sm form-check-custom form-check-solid">
                            <input class="form-check-input" type="checkbox" value="1" />
                        </div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <!--begin::Thumbnail-->
                            @if($product['type'] === \App\Enums\Application\ProductTypeEnum::COMPOSITE->value)
                                <a href="{{asset("products/edit/composite/{$product['id']}")}}" class="symbol symbol-50px">
                                    <span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$product['thumbnail']}});"></span>
                                </a>
                            @elseif($product['type'] === \App\Enums\Application\ProductTypeEnum::VARIANT->value)
                                <a href="{{asset("products/edit/variant/{$product['id']}")}}" class="symbol symbol-50px">
                                    <span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$product['thumbnail']}});"></span>
                                </a>
                            @else
                                <a href="{{asset("products/edit/{$product['id']}")}}" class="symbol symbol-50px">
                                    <span class="symbol-label" style="background-image:url({{asset('storage')}}/{{$product['thumbnail']}});"></span>
                                </a>
                            @endif
                            <!--end::Thumbnail-->
                            <div class="ms-5">
                                <!--begin::Title-->
                                @if($product['type'] === \App\Enums\Application\ProductTypeEnum::COMPOSITE->value)
                                <a href="{{asset("products/edit/composite/{$product['id']}")}}"
                                    class="text-gray-800 text-hover-primary fs-5 fw-bold"
                                    data-kt-ecommerce-product-filter="product_name">{{$product['name']}}</a>
                                @elseif($product['type'] === \App\Enums\Application\ProductTypeEnum::VARIANT->value)
                                <a href="{{asset("products/edit/variant/{$product['id']}")}}"
                                    class="text-gray-800 text-hover-primary fs-5 fw-bold"
                                    data-kt-ecommerce-product-filter="product_name">{{$product['name']}}</a>
                                @else
                                <a href="{{asset("products/edit/{$product['id']}")}}"
                                    class="text-gray-800 text-hover-primary fs-5 fw-bold"
                                    data-kt-ecommerce-product-filter="product_name">{{$product['name']}}</a>
                                @endif
                                <!--end::Title-->
                            </div>
                        </div>
                    </td>
                    <td class="text-end pe-0">
                        <span class="fw-bold">{{$product['barcode']}}</span>
                    </td>
                    <td class="text-end pe-0">
                        <span class="fw-bold">{{
                            match($product['type']) {
                                'simple' => __('messages.simple'),
                                'weighted' => __('messages.weighted'),
                                'service' => __('messages.service'),
                                'composite' => __('messages.composite'),
                                'variant' => __('messages.variant'),
                            }
                            }}</span>
                    </td>
                    <td class="text-end pe-0" data-order="{{$product['totalQuantities']}}">
                        @if($product['type'] === \App\Enums\Application\ProductTypeEnum::COMPOSITE->value)
                            <span class="fw-bold ms-3" data-bs-toggle="tooltip" data-bs-placement="top" title="{{__('messages.composite_quantity_note')}}">
                                {{$product['totalQuantities']}}
                                <i class="ki-duotone ki-information-5 fs-7">
                                    <i class="path1"></i>
                                    <i class="path2"></i>
                                    <i class="path3"></i>
                                </i>
                            </span>
                        @else
                            @if($product['type'] === \App\Enums\Application\ProductTypeEnum::SERVICE->value)
                            <span class="fw-bold ms-3" data-bs-toggle="tooltip" data-bs-placement="top" title="{{__('messages.service_quantity_note')}}">
                                ∞
                                <i class="ki-duotone ki-information-5 fs-7">
                                    <i class="path1"></i>
                                    <i class="path2"></i>
                                    <i class="path3"></i>
                                </i>
                            </span>
                            @else
                                <span class="fw-bold ms-3">{{$product['totalQuantities']}}</span>
                            @endif
                        @endif
                    </td>
                    @if($product['max_price'] ==  $product['min_price'])
                        <td class="text-end pe-0">{{$product['max_price']}}</td>
                    @else
                        <td class="text-end pe-0 priceminmax">{{$product['min_price']}} - {{$product['max_price']}} </td>

                    @endif
                    @if($product['status'] == \App\Enums\Application\ProductStatus::ACTIVE->value)
                    <td class="text-end pe-0" data-order="Active">
                        <!--begin::Badges-->
                        <div class="badge badge-light-success">{{__('messages.active')}}</div>
                        <!--end::Badges-->
                    </td>
                    @else
                    <td class="text-end pe-0" data-order="Inactive">
                        <!--begin::Badges-->
                        <div class="badge badge-light-danger">{{__('messages.disabled')}}</div>
                        <!--end::Badges-->
                    </td>

                    @endif
                    <td class="text-end">
                        <a href="#" class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">{{__('messages.actions')}}
                        <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                        <!--begin::Menu-->
                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="{{$product['type'] === \App\Enums\Application\ProductTypeEnum::COMPOSITE->value
                                    ? asset("products/edit/composite/{$product['id']}")
                                    : asset("products/edit/{$product['id']}")}}"
                                    class="menu-link px-3">{{__('messages.edit')}}</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="#" data-kt-ecommerce-product-filter="delete_row" data-product-id="{{$product['id']}}" class="menu-link px-3" >{{__('messages.delete')}}</a>
                            </div>
                            <!--end::Menu item-->
                        </div>
                        <!--end::Menu-->
                    </td>
                </tr>
                @endforeach



            </tbody>
        </table>
        @else
        <div class="card-body d-flex flex-center flex-column pt-12 p-9">
            <!--begin::Avatar-->
            <img src="{{asset('marble/src/')}}/svg/product_page.svg" class=" w-250px" alt="image" />
            <!--end::Avatar-->
            <!--begin::Name-->
            <span class="fs-3 text-gray-800 text-hover-primary fw-bold mb-0 mt-12">{{__('messages.product_page_first')}}</span>
            <!--end::Name-->
            <!--begin::Position-->
            <div class="fw-semibold text-gray-600 mb-6">{{__('messages.product_page_first_note')}} <a href="{{url('products/add')}}">{{__('messages.here')}}</a> </div>
            <!--end::Position-->
        </div>
        @endif
        <!--end::Table-->
    </div>

{{$products->links('livewire.pagination')}}

<script>
    document.addEventListener("DOMContentLoaded", function() {
        var statusVal = '{{$status}}';
        var tagVal = '{{$selectedTag}}';
        var categoryVal = '{{$selectedCategory}}';
        var typeVal = '{{$selectedType}}';
        var search = '{{$search}}';

        document.getElementById("search").addEventListener("keydown", function(event) {
            if (event.key === "Enter") {
                event.preventDefault();
            }
        });

        document.getElementById("myForm").addEventListener("submit", function(event) {
                event.preventDefault();
                // Fix the URL to avoid double slashes
                var baseUrl = window.location.origin + '/products/export';
                window.location.href = baseUrl + window.location.search;

        });

        const filterStatus = document.querySelector('[data-kt-ecommerce-product-filter="status"]');
        if(statusVal.length > 0){
            filterStatus.value = '{{$status}}'
        }

        $(filterStatus).on('change', e => {

            let value = e.target.value;
            if(value === 'all'){
                value = '';
                Livewire.dispatch('updatedStatus', {value: ''});

            }
            else if(value === 'active'){
                value = 'مفعّل';
                Livewire.dispatch('updatedStatus', {value: 'active'});
            }
            else if(value === 'disabled'){
                value = 'معطل';
                Livewire.dispatch('updatedStatus', {value: 'disabled'});

            }
        });

        // Handle tag filter
        const filterTag = document.querySelector('[data-kt-ecommerce-product-filter="tag"]');
        if(filterTag) {
            // Set initial value
            if(tagVal && tagVal.length > 0){
                filterTag.value = '{{$selectedTag}}';
            } else {
                filterTag.value = 'all';
            }

            $(filterTag).on('change', e => {
                let value = e.target.value;
                console.log('Tag filter changed to:', value);
                if(value === 'all'){
                    value = '';
                    Livewire.dispatch('updatedTag', {value: ''});
                } else {
                    Livewire.dispatch('updatedTag', {value: value});
                }
            });
        }

        // Handle category filter
        const filterCategory = document.querySelector('[data-kt-ecommerce-product-filter="category"]');
        if(filterCategory) {
            // Set initial value
            if(categoryVal && categoryVal.length > 0){
                filterCategory.value = '{{$selectedCategory}}';
            } else {
                filterCategory.value = 'all';
            }

            $(filterCategory).on('change', e => {
                let value = e.target.value;
                console.log('Category filter changed to:', value);
                if(value === 'all'){
                    value = '';
                    Livewire.dispatch('updatedCategory', {value: ''});
                } else {
                    Livewire.dispatch('updatedCategory', {value: value});
                }
            });
        }

        // Handle product type filter
        const filterType = document.querySelector('[data-kt-ecommerce-product-filter="type"]');
        if(filterType) {
            // Set initial value
            if(typeVal && typeVal.length > 0){
                filterType.value = '{{$selectedType}}';
            } else {
                filterType.value = 'all';
            }

            $(filterType).on('change', e => {
                let value = e.target.value;
                console.log('Type filter changed to:', value);
                if(value === 'all'){
                    value = '';
                    Livewire.dispatch('updatedType', {value: ''});
                } else {
                    Livewire.dispatch('updatedType', {value: value});
                }
            });
        }

        table = document.querySelector('#kt_ecommerce_products_table');
        const deleteButtons = table.querySelectorAll('[data-kt-ecommerce-product-filter="delete_row"]');

        deleteButtons.forEach(d => {
            // Delete button on click
            d.addEventListener('click', function (e) {
                e.preventDefault();

                // Select parent row
                const parent = e.target.closest('tr');

                // Get category name
                const productName = parent.querySelector('[data-kt-ecommerce-product-filter="product_name"]').innerText;
                var anchorElement = document.querySelector('a[data-product-id]');

                var productId = d.getAttribute('data-product-id');

                // SweetAlert2 pop up --- official docs reference: https://sweetalert2.github.io/
                Swal.fire({
                    text: "{{__('messages.delete_confrimation_message')}} " + productName + "؟",
                    icon: "warning",
                    showCancelButton: true,
                    buttonsStyling: false,
                    confirmButtonText: "{{__('messages.delete')}}!",
                    cancelButtonText: "{{__('messages.cancel')}}",
                    customClass: {
                        confirmButton: "btn fw-bold btn-danger",
                        cancelButton: "btn fw-bold btn-active-light-primary"
                    }
                }).then(function (result) {
                    if (result.value) {

                        var fullLink = "{{asset("products/delete/")}}/" + productId
                        window.location.href = fullLink;
                    }
                });
            })
        });


    });
</script>

</div>
