<div class="card card-flush col-lg-12 col-md-12 col-sm-12">
@php use Illuminate\Support\Collection ;@endphp

    <!--begin::Body-->
    <div class="card-body">
        <!--begin::Main column-->
        <div class="d-flex flex-column flex-lg-row-fluid gap-7 gap-lg-10">
            <!--begin::Order details-->
            <div class="card card-flush py-4">
                <div class="card-body pt-0">
                    <div class="d-flex flex-column gap-5">
                        <!--begin::Search products-->
                        <div class="d-flex align-items-center position-relative mb-n7">
                            @if($permissions->cashier_wholesale || $user->main_account)
                                @if(__('messages.direction') == 'rtl')
                                    <div style="position: relative; left:15px" class="form-check form-switch form-check-custom form-check-solid">
                                        <input wire:model="wholesale" {{!$this->plan->is_pos_wholesale_price_supported?"data-bs-toggle=modal data-bs-target=#needsSubscription":''}} wire:click="wholesaleAction" class="form-check-input" type="checkbox" value="false" id="flexSwitchDefault" />
                                        @if(!$this->plan->is_pos_wholesale_price_supported)
                                            <span class="position-absolute translate-middle badge badge-circle badge-sm badge-light-danger z-index-1 w-20px h-20px" style="left:-20px">*</span>
                                        @endif
                                    </div>
                                @else
                                    <div style="position: relative; right:15px" class="form-check form-switch form-check-custom form-check-solid">
                                        <input wire:model="wholesale" {{!$this->plan->is_pos_wholesale_price_supported?"data-bs-toggle=modal data-bs-target=#needsSubscription":''}} wire:click="wholesaleAction" class="form-check-input" type="checkbox" value="false" id="flexSwitchDefault" />
                                        @if(!$this->plan->is_pos_wholesale_price_supported)
                                            <span class="position-absolute translate-middle badge badge-circle badge-sm badge-light-danger z-index-1 w-20px h-20px" style="right:-20px">*</span>
                                        @endif
                                    </div>
                                @endif
                            @endif

                            <div class="position-relative mx-2" style="flex: 1;">
                                <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4" style="top: 50%; transform: translateY(-50%); z-index: 2;">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>

                                <input type="text" data-kt-ecommerce-edit-order-filter="search"
                                    class="form-control form-control-solid w-100 ps-12"
                                    wire:keydown.enter="handleEnterKeyPress()" wire:input.debounce.200ms="FindProducts"
                                    wire:model="inputValue" id='search' placeholder="{{__('messages.search_product')}}" />

                                <!-- Search results dropdown -->
                                @if(strlen($inputValue) > 0)
                                    <!-- Loading state (always visible when searching) -->
                                    <div wire:loading.delay wire:target="FindProducts" class="position-absolute bg-gray-100 bg-opacity-70 px-3 py-3 border" style="width: 100%; z-index: 100; top: 100%; left: 0; margin-top: 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                        <div class="d-flex justify-content-center py-5">
                                            <div class="d-flex flex-column align-items-center">
                                                <span class="spinner-border spinner-border-sm text-primary mb-2" role="status"></span>
                                                <span class="text-gray-600">{{__('messages.loading')}}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Results list (only visible when not loading) -->
                                    <div wire:loading.remove wire:target="FindProducts" class="position-absolute bg-gray-100 bg-opacity-70 px-3 py-3 border" style="width: 100%; z-index: 100; top: 100%; left: 0; margin-top: 0; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                                        @if($showList && count($searchResults) > 0)
                                            <ul class="ps-2 m-0" style="list-style: none">
                                                @foreach($searchResults as $searchResult)
                                                <li wire:click="ProdClicked2('add', @if(isset($searchResult['is_variant']) && $searchResult['is_variant']) {{$searchResult['variant_id']}} @else {{$searchResult['id']}} @endif, @if(isset($searchResult['is_variant']) && $searchResult['is_variant']) true @else false @endif, false)"
                                                    class="mb-1 bg-hover-light-dark p-2 rounded cursor-pointer">
                                                    <!--begin::Title-->
                                                    <a class="text-gray-800 text-hover-primary fs-5 fw-bold d-block">
                                                        @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                            {{$searchResult['variant_name'] ?? $searchResult['display_name'] ?? $searchResult['name']}}
                                                            <span class="badge badge-light-primary">{{__('messages.variant')}}</span>
                                                        @else
                                                            {{$searchResult['name']}}
                                                        @endif
                                                    </a>
                                                    <!--end::Title-->
                                                    <!--begin::Price-->
                                                    <div class="fw-semibold fs-7">{{__('messages.price')}}:
                                                        @if(isset($searchResult['has_promotion']) && $searchResult['has_promotion'])
                                                            @if(!$this->wholesale)
                                                                @if(isset($searchResult['original_sell_price']))
                                                                    <span data-kt-ecommerce-edit-order-filter="price">
                                                                        @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                                            {{$searchResult['sell_price']}}
                                                                        @else
                                                                            {{$searchResult->branchQuantities[0]['sell_price'] ?? '0'}}
                                                                        @endif
                                                                    </span>
                                                                    <span class="text-muted text-decoration-line-through ms-1">{{$searchResult['original_sell_price']}}</span>
                                                                @else
                                                                    <span data-kt-ecommerce-edit-order-filter="price">
                                                                        @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                                            {{$searchResult['sell_price']}}
                                                                        @else
                                                                            {{$searchResult->branchQuantities[0]['sell_price'] ?? '0'}}
                                                                        @endif
                                                                    </span>
                                                                @endif
                                                            @else
                                                                @if(isset($searchResult['original_wholesale_price']))
                                                                    <span data-kt-ecommerce-edit-order-filter="price">
                                                                        @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                                            {{$searchResult['wholesale_price']}}
                                                                        @else
                                                                            {{$searchResult->branchQuantities[0]['wholesale_price'] ?? '0'}}
                                                                        @endif
                                                                    </span>
                                                                    <span class="text-muted text-decoration-line-through ms-1">{{$searchResult['original_wholesale_price']}}</span>
                                                                @else
                                                                    <span data-kt-ecommerce-edit-order-filter="price">
                                                                        @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                                            {{$searchResult['wholesale_price']}}
                                                                        @else
                                                                            {{$searchResult->branchQuantities[0]['wholesale_price'] ?? '0'}}
                                                                        @endif
                                                                    </span>
                                                                @endif
                                                            @endif
                                                        @else
                                                            @if(!$this->wholesale)
                                                                <span data-kt-ecommerce-edit-order-filter="price">
                                                                    @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                                        {{$searchResult['sell_price']}}
                                                                    @else
                                                                        {{$searchResult->branchQuantities[0]['sell_price'] ?? '0'}}
                                                                    @endif
                                                                </span>
                                                            @else
                                                                <span data-kt-ecommerce-edit-order-filter="price">
                                                                    @if(isset($searchResult['is_variant']) && $searchResult['is_variant'])
                                                                        {{$searchResult['wholesale_price']}}
                                                                    @else
                                                                        {{$searchResult->branchQuantities[0]['wholesale_price'] ?? '0'}}
                                                                    @endif
                                                                </span>
                                                            @endif
                                                        @endif
                                                    </div>
                                                    <!--end::Price-->
                                                    <!--begin::SKU-->
                                                    <div class="text-muted fs-7">{{__('messages.barcode')}}:
                                                        {{$searchResult['barcode']}}</div>
                                                    <!--end::SKU-->
                                                </li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <!-- No results message -->
                                            <div class="d-flex flex-column align-items-center py-6">
                                                <i class="ki-duotone ki-information-5 fs-3x text-primary mb-2">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                    <span class="path3"></span>
                                                </i>
                                                <span class="text-gray-600 text-center">{{__('messages.no_match_error')}}</span>
                                            </div>
                                        @endif
                                    </div>
                                @endif
                            </div>

                            @if($permissions->cashier_add_customer || $user->main_account)
                                @if($chosenCustomer['id'] <= 0)
                                <div data-bs-toggle="tooltip" wire:click="getCustomers"
                                    data-bs-placement="top" data-bs-custom-class="tooltip-inverse"
                                    title="{{__('messages.pos_add_customer')}}">
                                    <div class="cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_1"
                                        style="margin-left: 5px">
                                        @if(!$this->plan->is_pos_adding_customer_supported)
                                            <span class="position-absolute badge badge-circle badge-sm badge-light-danger z-index-1 w-20px h-20px" style="transform:translate(-70%, -50%) !important">*</span>
                                        @endif
                                        <i class="ki-duotone ki-user-square fs-2x text-primary">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </div>
                                </div>
                                @else
                                <div data-bs-toggle="tooltip" wire:click="getCustomers('')" data-bs-placement="top"
                                    data-bs-custom-class="tooltip-inverse" title="{{__('messages.pos_add_customer')}}">
                                    <div class="cursor-pointer" data-bs-toggle="modal" data-bs-target="#kt_modal_1"
                                        style="margin-left: 5px">
                                        <i class="ki-duotone ki-user-square fs-2x text-success">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                    </div>
                                </div>
                                @endif
                            @endif


                    </div>
                    @if($permissions->cashier_wholesale || $user->main_account)
                        @if(__('messages.direction') == 'rtl')

                        <label class="form-check-label d-block position-relative col-2" style="left:23px;" for="flexSwitchDefault">
                                البيع بالجملة
                        </label>
                        @else
                        <label class="form-check-label d-block position-relative col-2" style="right:23px;" for="flexSwitchDefault">
                            wholesale
                        </label>

                        @endif
                    @endif


                    <!--end::Search products-->
                    <!--begin::Table-->
                    <div style="max-height: 400px; overflow: auto;">
                        <div class="table-responsive">
                        <table class="table table-auto align-middle table-row-dashed fs-6 gy-5"
                            id="kt_ecommerce_edit_order_product_table">
                            <thead>
                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-25px pe-2"></th>
                                    <th class="min-w-200px">{{__('messages.product')}}</th>
                                    <th class="min-w-100px pe-5">{{__('messages.qty')}}</th>
                                </tr>
                            </thead>
                            <tbody class="fw-semibold text-gray-600">
                                @foreach($products as $key => $product)
                                <tr>
                                    @php
                                        // Determine if product needs fulfillment UI
                                        $needsFulfillmentUI = isset($product['fulfillment_status']) &&
                                                            $product['fulfillment_status'] != 'Available' &&
                                                            !isset($product['is_return']);
                                    @endphp

                                    @if(!$needsFulfillmentUI)
                                    <!-- Standard display for available products and returns -->
                                    <td>
                                        <div class="form">
                                            <button type="button" wire:click="RemoveProduct('{{$product["cart_id"] ?? $product["id"]}}')" style="width:20px;height:20px"
                                                class="btn btn-icon btn-xs btn-light btn-icon-gray-400"
                                                data-kt-dialer-control="decrease">
                                                <i class="ki-duotone ki-cross-square fs-1">
                                                    <i class="path1"></i>
                                                    <i class="path2"></i>
                                                </i>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center"
                                            data-kt-ecommerce-edit-order-filter="product"
                                            data-kt-ecommerce-edit-order-id="product_1">
                                            <!--begin::Thumbnail-->
                                            <a class="symbol symbol-50px">
                                                <span class="symbol-label" style="background-image:url({{asset("storage").'/'.$product['thumbnail']}});"></span>
                                            </a>
                                            <!--end::Thumbnail-->
                                            <div class="ms-5">
                                                <!--begin::Title-->
                                                <a data-bs-toggle="modal" data-bs-target="#prodcut_details" wire:click="chooseProductDetail([{{$key}}, {{$product['cart_id'] ?? $product['id']}}])" class="text-gray-800 text-hover-primary cursor-pointer fs-5 fw-bold">
                                                    @if(isset($product['is_variant']) && $product['is_variant'])
                                                        {{$product['variant_name'] ?? $product['display_name'] ?? $product['name']}}
                                                        <span class="badge badge-light-primary">{{__('messages.variant')}}</span>
                                                    @else
                                                        {{$product['name']}}
                                                    @endif

                                                    @if(isset($product['has_promotion']) && $product['has_promotion'])
                                                        <span class="badge badge-light-danger">{{__('messages.product_in_promotion')}}</span>
                                                    @endif

                                                    @if(isset($product['is_return']) && $product['is_return'])
                                                        <span class="badge badge-light-danger ms-2">{{__('messages.returned')}}</span>
                                                    @elseif(isset($product['fulfillment_status']))
                                                        <span class="badge badge-light-success ms-2">{{__('messages.available')}}</span>
                                                    @endif
                                                </a>
                                                <!--end::Title-->
                                                <!--begin::Price-->
                                                <div class="fw-semibold fs-7">{{__('messages.price')}}:
                                                    @if(isset($product['has_promotion']) && $product['has_promotion'])
                                                        @if(!$wholesale)
                                                            @if(isset($product['original_sell_price']))
                                                                <span data-kt-ecommerce-edit-order-filter="price">{{$product['sell_price']}}</span>
                                                                <div class="d-flex align-items-center mt-1">
                                                                    <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                                                    <span class="text-muted text-decoration-line-through fs-8">{{$product['original_sell_price']}}</span>
                                                                </div>
                                                            @else
                                                                <span data-kt-ecommerce-edit-order-filter="price">{{$product['sell_price']}}</span>
                                                            @endif
                                                        @else
                                                            @if(isset($product['original_wholesale_price']))
                                                                <span data-kt-ecommerce-edit-order-filter="price">{{$product['wholesale_price']}}</span>
                                                                <div class="d-flex align-items-center mt-1">
                                                                    <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                                                    <span class="text-muted text-decoration-line-through fs-8">{{$product['original_wholesale_price']}}</span>
                                                                </div>
                                                            @else
                                                                <span data-kt-ecommerce-edit-order-filter="price">{{$product['wholesale_price']}}</span>
                                                            @endif
                                                        @endif
                                                    @else
                                                        @if(!$wholesale)
                                                            <span data-kt-ecommerce-edit-order-filter="price">{{$product['sell_price']}}</span>
                                                        @else
                                                            <span data-kt-ecommerce-edit-order-filter="price">{{$product['wholesale_price']}}</span>
                                                        @endif
                                                    @endif
                                                </div>
                                                <!--end::Price-->
                                                <!--begin::SKU-->
                                                <div class="text-muted fs-7">{{__('messages.barcode')}}:
                                                    {{$product['barcode']}}</div>
                                                <!--end::SKU-->
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-end flex justify-start pe-5"
                                        data-order="{{$product['cartQuantity']}}">
                                        <input type="text" wire:key="qty_stable_{{$product["cart_id"] ?? $product["id"]}}"
                                            wire:model='changedQuantity.{{$product["cart_id"] ?? $product["id"]}}'
                                            x-data="{
                                                debounceTimer: null,
                                                sanitizeInput(value) {
                                                    // Remove any non-numeric and non-decimal characters
                                                    let numericValue = value.replace(/[^0-9.٠-٩]/g, '');

                                                    // Convert Arabic numerals to English numerals
                                                    const arabicToEnglish = {
                                                        '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                                        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
                                                    };

                                                    for (let arabic in arabicToEnglish) {
                                                        numericValue = numericValue.replace(new RegExp(arabic, 'g'), arabicToEnglish[arabic]);
                                                    }

                                                    return numericValue;
                                                },
                                                handleInput() {
                                                    // Store cursor position and current value
                                                    const cursorPos = $el.selectionStart;
                                                    const currentValue = $el.value;

                                                    // Sanitize input immediately but don't trigger server call yet
                                                    const sanitized = this.sanitizeInput(currentValue);
                                                    if (currentValue !== sanitized) {
                                                        $el.value = sanitized;
                                                        $wire.set('changedQuantity.{{$product["cart_id"] ?? $product["id"]}}', sanitized, false);

                                                        // Restore cursor position after a small delay
                                                        setTimeout(() => {
                                                            if (document.activeElement === $el) {
                                                                $el.setSelectionRange(cursorPos, cursorPos);
                                                            }
                                                        }, 0);
                                                    }

                                                    clearTimeout(this.debounceTimer);
                                                    const isEmpty = $el.value.trim() === '';
                                                    const delay = isEmpty ? 1000 : 400;
                                                    this.debounceTimer = setTimeout(() => {
                                                        // Store the current input state before server call
                                                        const beforeValue = $el.value;
                                                        const beforeCursor = $el.selectionStart;
                                                        const wasFocused = document.activeElement === $el;

                                                        $wire.EditQuantityInCart('{{$product["cart_id"] ?? $product["id"]}}').then(() => {
                                                            // Restore input state after server response
                                                            if (wasFocused && $el.value === beforeValue) {
                                                                $el.focus();
                                                                $el.setSelectionRange(beforeCursor, beforeCursor);
                                                            }

                                                            // Force UI update after a short delay to show fulfillment changes
                                                            setTimeout(() => {
                                                                $wire.$refresh();
                                                            }, 100);
                                                        });
                                                    }, delay);
                                                }
                                            }"
                                            x-on:input="handleInput()"
                                            class="form-control form-control-solid text-left productQuantity"
                                            value="{{$product['cartQuantity']}}" />
                                    </td>
                                    @else
                                    <!-- Limited or unavailable products - collapsible card -->
                                    <td colspan="3" class="p-1">
                                        @php
                                            // Check if the product has fulfillment arranged
                                            $hasFulfillment = isset($product['has_fulfillment']) && $product['has_fulfillment'] === true;
                                            $hasBranchFulfillment = isset($product['fulfillment_from_branch']);
                                            $hasDeliveryFulfillment = isset($product['fulfillment_by_delivery']);

                                            // Set badge and icon based on fulfillment status
                                            $badgeClass = $product['fulfillment_status'] == 'Partially Fulfillable' ? 'badge-light-warning' : 'badge-light-danger';
                                            $iconClass = $product['fulfillment_status'] == 'Partially Fulfillable' ? 'ki-warning-circle text-warning' : 'ki-cross-circle text-danger';

                                            // Only change the border color to green if fulfillment is arranged
                                            if ($hasFulfillment || $hasBranchFulfillment || $hasDeliveryFulfillment) {
                                                $cardClass = 'border-success';
                                            } else {
                                                $cardClass = $product['fulfillment_status'] == 'Partially Fulfillable' ? 'border-warning' : 'border-danger';
                                            }

                                            $collapseId = 'product_collapse_' . ($product["cart_id"] ?? $product["id"]);
                                            $collapseId = preg_replace('/[^a-zA-Z0-9_]/', '_', $collapseId);
                                        @endphp

                                        <div class="card card-flush border {{$cardClass}} border-2 mb-2">
                                            <div class="card-header py-3">
                                                <div class="card-title d-flex align-items-center">
                                                    <!-- Remove button -->
                                                    <button type="button" wire:click="RemoveProduct('{{$product["cart_id"] ?? $product["id"]}}')"
                                                        class="btn btn-icon btn-xs btn-light btn-icon-gray-400 me-3">
                                                        <i class="ki-duotone ki-cross-square fs-1">
                                                            <i class="path1"></i>
                                                            <i class="path2"></i>
                                                        </i>
                                                    </button>

                                                    <!-- Product thumbnail -->
                                                    <div class="symbol symbol-50px me-3">
                                                        <span class="symbol-label" style="background-image:url({{asset("storage").'/'.$product['thumbnail']}})"></span>
                                                    </div>

                                                    <!-- Product name and status -->
                                                    <div>
                                                        <a
                                                            href="#"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#prodcut_details"
                                                            wire:click="chooseProductDetail([{{$key}}, {{$product['cart_id'] ?? $product['id']}}])"
                                                            class="text-gray-900 text-hover-primary fw-bold fs-5 cursor-pointer"
                                                        >
                                                            @if(isset($product['is_variant']) && $product['is_variant'])
                                                                {{$product['variant_name'] ?? $product['display_name'] ?? $product['name']}}
                                                                <span class="badge badge-light-primary">{{__('messages.variant')}}</span>
                                                            @else
                                                                {{$product['name']}}
                                                            @endif
                                                        </a>
                                                        @if(isset($product['is_return']) && $product['is_return'])
                                                            <span class="badge badge-light-danger ms-2">{{__('messages.returned')}} - مرتجع</span>
                                                        @endif
                                                        <span class="badge {{$badgeClass}} ms-2">{{__('messages.' . strtolower(str_replace(' ', '_', $product['fulfillment_status'])))}}</span>

                                                        <!-- Price and barcode info - similar to original design -->
                                                        <div class="fw-semibold fs-7">{{__('messages.price')}}:
                                                            @if(isset($product['has_promotion']) && $product['has_promotion'])
                                                                @if(!$wholesale)
                                                                    @if(isset($product['original_sell_price']))
                                                                        <span>{{$product['sell_price']}}</span>
                                                                        <div class="d-flex align-items-center mt-1">
                                                                            <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                                                            <span class="text-muted text-decoration-line-through fs-8">{{$product['original_sell_price']}}</span>
                                                                        </div>
                                                                    @else
                                                                        <span>{{$product['sell_price']}}</span>
                                                                    @endif
                                                                @else
                                                                    @if(isset($product['original_wholesale_price']))
                                                                        <span>{{$product['wholesale_price']}}</span>
                                                                        <div class="d-flex align-items-center mt-1">
                                                                            <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                                                            <span class="text-muted text-decoration-line-through fs-8">{{$product['original_wholesale_price']}}</span>
                                                                        </div>
                                                                    @else
                                                                        <span>{{$product['wholesale_price']}}</span>
                                                                    @endif
                                                                @endif
                                                            @else
                                                                @if(!$wholesale)
                                                                    <span>{{$product['sell_price']}}</span>
                                                                @else
                                                                    <span>{{$product['wholesale_price']}}</span>
                                                                @endif
                                                            @endif
                                                        </div>
                                                        <div class="text-muted fs-7">{{__('messages.barcode')}}:
                                                            {{$product['barcode']}}</div>
                                                    </div>
                                                </div>

                                                <div class="card-toolbar d-flex align-items-center flex-nowrap">
                                                    <!-- Quantity input - same size and style as original -->
                                                    <input type="text" wire:key="qty_stable_{{$product["cart_id"] ?? $product["id"]}}"
                                                        wire:model='changedQuantity.{{$product["cart_id"] ?? $product["id"]}}'
                                                        x-data="{
                                                            debounceTimer: null,
                                                            sanitizeInput(value) {
                                                                // Remove any non-numeric and non-decimal characters
                                                                let numericValue = value.replace(/[^0-9.٠-٩]/g, '');

                                                                // Convert Arabic numerals to English numerals
                                                                const arabicToEnglish = {
                                                                    '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                                                    '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
                                                                };

                                                                for (let arabic in arabicToEnglish) {
                                                                    numericValue = numericValue.replace(new RegExp(arabic, 'g'), arabicToEnglish[arabic]);
                                                                }

                                                                return numericValue;
                                                            },
                                                            handleInput() {
                                                                // Store cursor position and current value
                                                                const cursorPos = $el.selectionStart;
                                                                const currentValue = $el.value;

                                                                // Sanitize input immediately but don't trigger server call yet
                                                                const sanitized = this.sanitizeInput(currentValue);
                                                                if (currentValue !== sanitized) {
                                                                    $el.value = sanitized;
                                                                    $wire.set('changedQuantity.{{$product["cart_id"] ?? $product["id"]}}', sanitized, false);

                                                                    // Restore cursor position after a small delay
                                                                    setTimeout(() => {
                                                                        if (document.activeElement === $el) {
                                                                            $el.setSelectionRange(cursorPos, cursorPos);
                                                                        }
                                                                    }, 0);
                                                                }

                                                                clearTimeout(this.debounceTimer);
                                                                const isEmpty = $el.value.trim() === '';
                                                                const delay = isEmpty ? 1000 : 400;
                                                                this.debounceTimer = setTimeout(() => {
                                                                    // Store the current input state before server call
                                                                    const beforeValue = $el.value;
                                                                    const beforeCursor = $el.selectionStart;
                                                                    const wasFocused = document.activeElement === $el;

                                                                    $wire.EditQuantityInCart('{{$product["cart_id"] ?? $product["id"]}}').then(() => {
                                                                        // Restore input state after server response
                                                                        if (wasFocused && $el.value === beforeValue) {
                                                                            $el.focus();
                                                                            $el.setSelectionRange(beforeCursor, beforeCursor);
                                                                        }

                                                                        // Force UI update after a short delay to show fulfillment changes
                                                                        setTimeout(() => {
                                                                            $wire.$refresh();
                                                                        }, 100);
                                                                    });
                                                                }, delay);
                                                            }
                                                        }"
                                                        x-on:input="handleInput()"
                                                        class="form-control form-control-solid text-left productQuantity"
                                                        style="width: 80px;"
                                                        value="{{$product['cartQuantity']}}" />

                                                    <!-- Explicit collapse toggle button with margin -->
                                                    <button type="button"
                                                        class="btn btn-sm btn-icon btn-light ms-2"
                                                        data-bs-toggle="collapse"
                                                        data-bs-target="#{{$collapseId}}">
                                                        <i class="ki-duotone ki-arrow-down fs-2">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                        </i>
                                                    </button>
                                                </div>
                                            </div>

                                            <div id="{{$collapseId}}" class="collapse">
                                                <div class="card-body pt-2">
                                                    <div class="d-flex align-items-center mb-3">
                                                        <i class="ki-duotone {{$iconClass}} fs-2 me-2">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                        </i>
                                                        <div class="fs-7 fw-semibold text-gray-700">
                                                            <span class="me-4">{{__('messages.available')}}: {{$product['quantity'] ?? 0}}</span>
                                                            <span>{{__('messages.requested')}}: {{$product['cartQuantity']}}</span>
                                                        </div>
                                                    </div>

                                                    <!-- Fulfillment options -->
                                                    @if(!($hasFulfillment || $hasBranchFulfillment || $hasDeliveryFulfillment))
                                                    <div class="mt-2">
                                                        <div class="fw-bold fs-7 mb-2">{{__('messages.fulfillment_options')}}:</div>
                                                        <div class="d-flex flex-wrap gap-2">
                                                            <!-- Branch fulfillment option -->
                                                            <button
                                                                class="btn btn-sm btn-light-primary d-flex align-items-center"
                                                                wire:click="openBranchFulfillmentModal('{{$product['cart_id'] ?? $product['id']}}')"
                                                            >
                                                                <i class="ki-duotone ki-building fs-6 me-1">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                                <span>{{__('messages.other_branch')}}</span>
                                                                <i class="ki-duotone ki-arrow-right fs-7 ms-1">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                            </button>

                                                            <!-- Delivery fulfillment option -->
                                                            <button
                                                                class="btn btn-sm btn-light-success d-flex align-items-center"
                                                                wire:click="openDeliveryFulfillmentModal('{{$product['cart_id'] ?? $product['id']}}')"
                                                            >
                                                                <i class="ki-duotone ki-truck fs-6 me-1">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                                <span>{{__('messages.delivery')}}</span>
                                                                <i class="ki-duotone ki-arrow-right fs-7 ms-1">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    @else
                                                    <div class="mt-2">
                                                        <div class="fw-bold fs-7 mb-2">{{__('messages.fulfillment_options')}}:</div>
                                                        <div class="alert alert-success py-2 mb-0">
                                                            <div class="d-flex align-items-center">
                                                                <i class="ki-duotone ki-check-circle fs-2 me-2">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                                <div class="fs-7 fw-semibold">
                                                                    {{__('messages.fulfillment_arranged')}}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    @endif
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                        </div>
                        @if(!$products)
                        <div class="card-body d-flex flex-column flex-center">
                            <!--begin::Heading-->
                            <div class="mb-2">
                                <div class="py-10 text-center">
                                    <i class="ki-duotone ki-handcart fs-7x text-primary">
                                    </i>
                                </div>

                                <!--begin::Title-->
                                <h1 class="fw-semibold text-gray-800 text-center lh-lg">
                                    {{__('messages.add_product_msg')}}
                                    <br />
                                    <span class="fw-bolder">{{__('messages.add_product_msg2')}}</span>
                                </h1>
                                <!--end::Title-->
                                <!--begin::Illustration-->
                                <!--end::Illustration-->
                            </div>
                            <!--end::Heading-->
                        </div>
                        @endif
                    </div>

                    <!--end::Table-->
                </div>
            </div>
            <!--end::Card header-->
        </div>
        <!--end::Order details-->
        <!--begin::Order details-->
        <!--end::Order details-->
    </div>
    <!--end::Main column-->
</div>
@if(count($products))
<div wire:ignore.self class="modal fade" tabindex="-1" id="prodcut_details" aria-labelledby="product_details_label">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="product_details_label">{{__('messages.product_details')}}</h3>

                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body">
                <div>
                        <table class="table table-auto align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_edit_order_product_table">
                            <div class="d-flex align-items-center mb-5" data-kt-ecommerce-edit-order-filter="product"
                                data-kt-ecommerce-edit-order-id="product_1">
                                <!--begin::Thumbnail-->
                                <a class="symbol symbol-100px">
                                    <span class="symbol-label" style="background-image:url({{asset("storage").'/'.$products[$productDetailKey]['thumbnail']}});"></span>
                                </a>
                                <!--end::Thumbnail-->
                                <div class="ms-5">
                                    <!--begin::Title-->
                                    <a class="text-gray-800 text-hover-primary fs-5 fw-bold">
                                        @if(isset($products[$productDetailKey]['is_variant']) && $products[$productDetailKey]['is_variant'])
                                            {{$products[$productDetailKey]['variant_name'] ?? $products[$productDetailKey]['display_name'] ?? $products[$productDetailKey]['name']}}
                                            <span class="badge badge-light-primary">{{__('messages.variant')}}</span>
                                        @else
                                            {{$products[$productDetailKey]['name']}}
                                        @endif

                                        @if(isset($products[$productDetailKey]['is_return']) && $products[$productDetailKey]['is_return'])
                                            <span class="badge badge-light-danger ms-2">{{__('messages.returned')}} - مرتجع</span>
                                        @endif
                                    </a>
                                    <!--end::Title-->
                                    <!--begin::Price-->
                                    <div class="fw-semibold fs-7">{{__('messages.price')}}:
                                        @if(isset($products[$productDetailKey]['has_promotion']) && $products[$productDetailKey]['has_promotion'] && isset($products[$productDetailKey]['original_sell_price']))
                                            <span data-kt-ecommerce-edit-order-filter="price">{{$products[$productDetailKey]['sell_price']}}</span>
                                            <div class="d-flex align-items-center mt-1">
                                                <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                                <span class="text-muted text-decoration-line-through fs-8">{{$products[$productDetailKey]['original_sell_price']}}</span>
                                            </div>
                                        @else
                                            <span data-kt-ecommerce-edit-order-filter="price">{{$products[$productDetailKey]['sell_price']}}</span>
                                        @endif
                                    </div>
                                    <!--end::Price-->
                                    <!--begin::Price-->
                                    <div class="fw-semibold fs-7">{{__('messages.wholesale_price')}}:
                                        @if(isset($products[$productDetailKey]['has_promotion']) && $products[$productDetailKey]['has_promotion'] && isset($products[$productDetailKey]['original_wholesale_price']))
                                            <span data-kt-ecommerce-edit-order-filter="price">{{$products[$productDetailKey]['wholesale_price']}}</span>
                                            <div class="d-flex align-items-center mt-1">
                                                <small class="text-muted me-1">{{__('messages.direction') == 'rtl' ? 'سابقًا:' : 'Before:'}}</small>
                                                <span class="text-muted text-decoration-line-through fs-8">{{$products[$productDetailKey]['original_wholesale_price']}}</span>
                                            </div>
                                        @else
                                            <span data-kt-ecommerce-edit-order-filter="price">{{$products[$productDetailKey]['wholesale_price']}}</span>
                                        @endif
                                    </div>
                                    <!--end::Price-->
                                    <!--begin::SKU-->
                                    <div class="text-muted fs-7">{{__('messages.barcode')}}:
                                        {{$products[$productDetailKey]['barcode']}}
                                    </div>
                                    <!--end::SKU-->
                                </div>
                            </div>
                            <thead>
                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-25px pe-2"></th>
                                    <th class="min-w-200px">{{__('messages.branch_name')}}</th>
                                    <th class="min-w-100px pe-5">{{__('messages.qty')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($productBranchQuantities as $branch)
                                <tr>
                                    <td class="w-25px pe-2"></td>
                                    <td>
                                        <div class="d-flex align-items-center" data-kt-ecommerce-edit-order-filter="product" data-kt-ecommerce-edit-order-id="product_1">
                                            <div class="ms-1">
                                                @if(isset($products[$productDetailKey]['is_variant']) && $products[$productDetailKey]['is_variant'])
                                                    <span data-kt-ecommerce-edit-order-filter="price">{{$branch->branch->branch_name ?? 'N/A'}}</span>
                                                @else
                                                    <span data-kt-ecommerce-edit-order-filter="price">{{$branch?->merchantBranch?->branch_name}}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center" data-kt-ecommerce-edit-order-filter="product" data-kt-ecommerce-edit-order-id="product_1">
                                            <div class="ms-1">
                                                <span data-kt-ecommerce-edit-order-filter="price">
                                                    @if(isset($products[$productDetailKey]['is_variant']) && $products[$productDetailKey]['is_variant'])
                                                        {{$branch['quantity'] ?? 0}}
                                                    @elseif($products[$productDetailKey]['type'] === \App\Enums\Application\ProductTypeEnum::COMPOSITE->value)
                                                        {{$branch['max_possible_quantity']}}
                                                        <small class="text-muted">({{__('messages.composite_possible')}})</small>
                                                    @else
                                                        {{$branch['quantity']}}
                                                    @endif
                                                </span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.close')}}</button>
            </div>
        </div>
    </div>
</div>
@endif
<div wire:ignore.self class="modal fade" tabindex="-1" id="kt_modal_1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">{{__('messages.choose_customer_header')}}</h3>

                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body" wire:ignore.self>
                @if($pendingAction === 'delivery_fulfillment')
                <div class="alert alert-info mb-5">
                    <div class="d-flex">
                        <i class="ki-duotone ki-information-5 fs-2 me-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <div>
                            <h4 class="mb-1 text-dark">{{__('messages.select_customer_for_delivery')}}</h4>
                            <span>{{__('messages.select_customer_for_delivery_note')}}</span>
                        </div>
                    </div>
                </div>
                @elseif($pendingAction === 'branch_fulfillment')
                <div class="alert alert-info mb-5">
                    <div class="d-flex">
                        <i class="ki-duotone ki-information-5 fs-2 me-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <div>
                            <h4 class="mb-1 text-dark">{{__('messages.select_customer_for_branch')}}</h4>
                            <span>{{__('messages.select_customer_for_branch_note')}}</span>
                        </div>
                    </div>
                </div>
                @endif
                <!-- Tabs -->
                <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_pane_1">{{__('messages.select_customer')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_2">{{__('messages.create_customer')}}</a>
                    </li>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <!-- Select Customer Tab -->
                    <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel">
                        <div class="d-flex align-items-center position-relative mb-n7">
                            <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>

                            <input type="text" wire:input.debounce.500ms="findCustomer"
                                class="form-control form-control-solid w-100 w-lg-100 ps-12" wire:model="inputCustomerValue"
                                id='inputCustomer' placeholder="{{__('messages.search_customer')}}" />
                        </div>
                        <div class="mt-5">
                    @if(strlen($inputCustomerValue) > 0)
                        <!-- Loading state (always visible when searching) -->
                        <div wire:loading.delay wire:target="findCustomer" class="bg-gray-100 bg-opacity-70 m-0 px-6 py-5">
                            <div class="d-flex justify-content-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <span class="spinner-border spinner-border-sm text-primary mb-2" role="status"></span>
                                    <span class="text-gray-600">{{__('messages.loading')}}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Results list (only visible when not loading) -->
                        <div wire:loading.remove wire:target="findCustomer">
                            @if($showCustomerList && count($searchCustomerResults) > 0)
                                <div id="searchResults" class="relative bg-gray-100 bg-opacity-70 m-0 px-6 py-5">
                                    <div class="m-0">
                                        <ul style="list-style: none">
                                            @foreach($searchCustomerResults as $customer)
                                            <li style="padding: 5px" data-bs-dismiss="modal"
                                                wire:click="customerAdded({{$customer}},'{{$customer?->name}}')"
                                                class="mb-1 bg-hover-light-dark">
                                                <!--begin::Title-->
                                                <a class="text-gray-800 text-hover-primary fs-5 fw-bold">{{$customer?->name}}</a>
                                                <!--end::Title-->
                                            </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            @else
                                <!-- No results message -->
                                <div class="d-flex flex-column align-items-center py-6 bg-gray-100 bg-opacity-70 m-0 px-6">
                                    <i class="ki-duotone ki-information-5 fs-3x text-primary mb-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    <span class="text-gray-600 text-center">{{__('messages.no_match_error')}}</span>
                                </div>
                            @endif
                        </div>
                    @else
                        <!-- Show default customers when no search input and no customer is selected -->
                        @if((!isset($chosenCustomer['id']) || $chosenCustomer['id'] <= 0) && count($customers) > 0)
                            <!-- Loading state (always visible when loading customers) -->
                            <div wire:loading.delay wire:target="getCustomers" class="bg-gray-100 bg-opacity-70 m-0 px-6 py-5">
                                <div class="d-flex justify-content-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <span class="spinner-border spinner-border-sm text-primary mb-2" role="status"></span>
                                        <span class="text-gray-600">{{__('messages.loading')}}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer list (only visible when not loading) -->
                            <div wire:loading.remove wire:target="getCustomers" id="searchResultCustomers" class="relative bg-gray-100 bg-opacity-70 m-0 px-6 py-5">
                                <!--begin::Stats-->
                                <div class="m-0">
                                    <div class="mb-3">
                                        <small class="text-muted">{{__('messages.recent_customers')}}</small>
                                    </div>
                                    <!--begin::Number-->
                                    <ul style="list-style: none">
                                        @foreach($customers as $customer)
                                        <li style="padding: 5px" data-bs-dismiss="modal" {{!$this->plan->is_pos_wholesale_price_supported?"data-bs-toggle=modal data-bs-target=#needsSubscription":''}}
                                        wire:click="customerAdded({{$customer}},'{{$customer['name']}}')"
                                            class="mb-1 bg-hover-light-dark">
                                            <!--begin::Title-->
                                            <a class="text-gray-800 text-hover-primary fs-5 fw-bold">{{$customer['name']}}</a>
                                            <!--end::Title-->
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                                <!--end::Stats-->
                            </div>
                        @elseif((!isset($chosenCustomer['id']) || $chosenCustomer['id'] <= 0) && count($customers) == 0)
                            <!-- No customers available message - only show when no customer is selected -->
                            <div class="d-flex flex-column align-items-center py-6 bg-gray-100 bg-opacity-70 m-0 px-6">
                                <i class="ki-duotone ki-information-5 fs-3x text-primary mb-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <span class="text-gray-600 text-center">{{__('messages.no_customers_found')}}</span>
                                <small class="text-muted text-center mt-2">{{__('messages.start_typing_to_search')}}</small>
                            </div>
                        @endif
                    @endif

                    @if(isset($chosenCustomer['id']) && $chosenCustomer['id'] > 0 && count($customers) == 0 && strlen($inputCustomerValue) == 0)
                        <div id="searchResults" class="relative bg-gray-100 bg-opacity-70 m-0 px-6 py-5">
                            <!--begin::Stats-->
                            <div class="m-0">
                                <div class="mb-3">
                                    <small class="text-muted">{{__('messages.selected_customer')}}</small>
                                </div>

                                <!-- Selected customer display -->
                                <div class="d-flex align-items-center justify-content-between p-3 bg-light-success rounded mb-3">
                                    <div class="d-flex align-items-center">
                                        <i class="ki-duotone ki-check-circle fs-2 text-success me-3">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        <div>
                                            <div class="fs-5 fw-bold text-gray-800">{{$chosenCustomer['name']}}</div>
                                            <small class="text-muted">{{__('messages.currently_selected')}}</small>
                                        </div>
                                    </div>
                                    <button type="button"
                                            class="btn btn-sm btn-light-danger btn-icon"
                                            wire:click="customerRemove"
                                            data-bs-dismiss="modal"
                                            data-bs-toggle="tooltip"
                                            title="{{__('messages.remove_customer')}}">
                                        <i class="ki-duotone ki-cross fs-3">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                    </button>
                                </div>

                                <!-- Help text -->
                                <div class="text-center">
                                    <small class="text-muted">{{__('messages.click_remove_to_select_different_customer')}}</small>
                                </div>
                            </div>
                            <!--end::Stats-->
                        </div>
                    @endif
                        </div>
                    </div>

                    <!-- Create Customer Tab -->
                    <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel">
                        @livewire('pos-customer-create')
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.close')}}</button>
            </div>
        </div>
    </div>
</div>
<div wire:ignore.self class="modal fade" tabindex="-1" id="needsSubscription">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">{{__('messages.needs_subscription_page_first')}}</h3>

                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal"
                    aria-label="Close">
                    <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body" wire:ignore.self>
                <div class="mt-5">
                    <div class="card-body pt-0 table-responsive">
                        <!--begin::Table-->
                        <div class="card-body d-flex flex-center flex-column pt-12 p-9">
                            <!--begin::Avatar-->
                            <img src="{{asset('marble/src/')}}/svg/access-denied.svg" class=" w-250px" alt="image" />
                            <!--end::Avatar-->
                            <!--begin::Name-->
                            <span class="fs-3 text-gray-800 text-hover-primary fw-bold mb-0 mt-12">{{__('messages.needs_subscription_page_first')}}</span>
                            @if(isset($message))
                                <span class="fs-3 text-gray-800 text-hover-primary fw-bold mb-0 mt-3">{{$message}}</span>
                            @endif
                            <!--end::Name-->
                            <!--begin::Position-->
                            <div class="fw-semibold text-gray-600 mb-6">{{__('messages.needs_subscription_page_first_note')}} <a href="{{url('subscription')}}">{{__('messages.here')}}</a> </div>
                            <!--end::Position-->
                        </div>
                        <!--end::Table-->
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.close')}}</button>
            </div>
        </div>
    </div>
</div>

<!-- Branch Fulfillment Modal -->
<div wire:ignore.self class="modal fade" tabindex="-1" id="branch_fulfillment_modal" aria-labelledby="branch_fulfillment_modal_title">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="branch_fulfillment_modal_title">{{__('messages.fulfill_from_another_branch')}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if($selectedFulfillmentProduct)
                <!-- Product Info -->
                <div class="d-flex align-items-center mb-5">
                    <div class="symbol symbol-50px me-3">
                        <span class="symbol-label" style="background-image:url({{asset("storage").'/'.$selectedFulfillmentProduct['thumbnail']}});"></span>
                    </div>
                    <div>
                        <div class="fs-5 fw-bold text-dark">
                            @if(isset($selectedFulfillmentProduct['is_variant']) && $selectedFulfillmentProduct['is_variant'])
                                {{$selectedFulfillmentProduct['variant_name'] ?? $selectedFulfillmentProduct['display_name'] ?? $selectedFulfillmentProduct['name']}}
                            @else
                                {{$selectedFulfillmentProduct['name']}}
                            @endif
                        </div>
                        <div class="text-muted fs-7">
                            {{__('messages.requested')}}: <span class="fw-bold">{{$selectedFulfillmentProduct['cartQuantity']}}</span> |
                            {{__('messages.available_in_current_branch')}}: <span class="fw-bold text-danger">{{$selectedFulfillmentProduct['quantity'] ?? 0}}</span>
                        </div>
                    </div>
                </div>

                <!-- Branch Selection -->
                <div class="mb-5">
                    <label class="form-label">{{__('messages.select_branch')}}</label>
                    <select wire:model="selectedFulfillmentBranch" class="form-select" data-placeholder="{{__('messages.select_branch')}}">
                        <option value="">{{__('messages.select_branch')}}</option>
                        @foreach($branchesForFulfillment as $branch)
                            @if($branch['id'] != $branchId)
                                <option value="{{$branch['id']}}">{{$branch['name']}} ({{__('messages.available')}}: {{$branch['quantity'] ?? 0}})</option>
                            @endif
                        @endforeach
                    </select>
                </div>

                <!-- Quantity Input -->
                <div class="mb-5">
                    <label class="form-label">{{__('messages.quantity_to_fulfill')}}</label>
                    <input wire:model.live="fulfillmentQuantity" type="text" class="form-control form-control-solid text-left productQuantity" min="1" max="{{$maxFulfillmentQuantity}}">
                    <div class="form-text">{{__('messages.maximum_available')}}: {{$maxFulfillmentQuantity}}</div>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.cancel')}}</button>
                <button type="button" class="btn btn-primary" wire:click="confirmBranchFulfillment">
                    <i class="ki-duotone ki-check fs-2 me-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    {{__('messages.confirm_fulfillment')}}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delivery Fulfillment Modal -->
<div wire:ignore.self class="modal fade" tabindex="-1" id="delivery_fulfillment_modal" aria-labelledby="delivery_fulfillment_modal_title">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="delivery_fulfillment_modal_title">{{__('messages.fulfill_by_delivery')}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if($selectedFulfillmentProduct)
                <!-- Product Info -->
                <div class="d-flex align-items-center mb-5">
                    <div class="symbol symbol-50px me-3">
                        <span class="symbol-label" style="background-image:url({{asset("storage").'/'.$selectedFulfillmentProduct['thumbnail']}});"></span>
                    </div>
                    <div>
                        <div class="fs-5 fw-bold text-dark">
                            @if(isset($selectedFulfillmentProduct['is_variant']) && $selectedFulfillmentProduct['is_variant'])
                                {{$selectedFulfillmentProduct['variant_name'] ?? $selectedFulfillmentProduct['display_name'] ?? $selectedFulfillmentProduct['name']}}
                            @else
                                {{$selectedFulfillmentProduct['name']}}
                            @endif
                        </div>
                        <div class="text-muted fs-7">
                            {{__('messages.requested')}}: <span class="fw-bold">{{$selectedFulfillmentProduct['cartQuantity']}}</span> |
                            {{__('messages.available_in_current_branch')}}: <span class="fw-bold text-danger">{{$selectedFulfillmentProduct['quantity'] ?? 0}}</span>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="mb-5">
                    <h6 class="mb-3">{{__('messages.customer_info')}}</h6>
                    <div class="border rounded p-3 bg-light-primary bg-opacity-50 mb-3">
                        @if(isset($chosenCustomer) && $chosenCustomer['id'] > 0)
                            <div class="fs-6 fw-bold mb-1">{{$chosenCustomer['name']}}</div>
                            <div class="text-muted fs-7">{{$chosenCustomer['phone'] ?? ''}}</div>
                        @else
                            <div class="fs-6 text-muted">{{__('messages.no_customer_selected')}}</div>
                        @endif
                    </div>
                </div>

                <!-- Delivery Address -->
                <div class="mb-5">
                    <label class="form-label">{{__('messages.delivery_address')}}</label>
                    <textarea wire:model="deliveryAddress" class="form-control" rows="3" placeholder="{{__('messages.enter_delivery_address')}}" readonly></textarea>
                </div>

                <!-- Delivery Notes -->
                <div class="mb-5">
                    <label class="form-label">{{__('messages.delivery_notes')}}</label>
                    <textarea wire:model="deliveryNotes" class="form-control" rows="2" placeholder="{{__('messages.delivery_notes_placeholder')}}"></textarea>
                </div>

                <!-- Inventory Note -->
                <div class="alert alert-info mb-0">
                    <div class="d-flex">
                        <i class="ki-duotone ki-information-5 fs-2 me-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <div>
                            <h4 class="mb-1 text-dark">{{__('messages.delivery_fulfillment_note')}}</h4>
                            <span>{{__('messages.delivery_inventory_note')}}</span>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.cancel')}}</button>
                <button type="button" class="btn btn-success" wire:click="confirmDeliveryFulfillment">
                    <i class="ki-duotone ki-truck fs-2 me-1">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                    {{__('messages.schedule_delivery')}}
                </button>
            </div>
        </div>
    </div>
</div>

<!--end: Card Body-->
<script>
    // Listen for Livewire events
    document.addEventListener('livewire:initialized', () => {
        // Handle closeModal event
        Livewire.on('closeModal', () => {
            // Close the customer modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('kt_modal_1'));
            if (modal) {
                modal.hide();
            }
        });

        // Handle showToast event
        Livewire.on('showToast', (data) => {
            console.log('showtoast')
            if (data.message) {
                // Check if toastr is available
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message);
                } else {
                    // Fallback to alert if toastr is not available
                    alert(data.message);
                }
            }
        });
    });

    // Listen for Livewire browser events (Livewire 3 syntax)
    document.addEventListener('livewire:initialized', () => {
        Livewire.on('browser-event', (data) => {
            if (data.event === 'close-modal') {
                const modal = bootstrap.Modal.getInstance(document.getElementById('kt_modal_1'));
                if (modal) {
                    modal.hide();
                }
            }
        });
    });

    document.addEventListener("DOMContentLoaded", function() {
        var searchInput = document.getElementById("search");
        searchInput.focus();

        // Note: Input processing is now handled by Alpine.js in the input elements
        // Fulfillment status updates happen immediately but silently to prevent flickering
    });

    document.addEventListener("keydown", function(event) {
            const inputElements = document.querySelectorAll('.productQuantity'); // Select all input elements
            const multiPayInputs = document.querySelectorAll('.multiPayInputs'); // Select all input elements
            const customerFormInputs = document.querySelectorAll('.customer-form-input'); // Select all customer form inputs
            const inputCustomer = document.getElementById("inputCustomer");
            const discount = document.getElementById("discount");
            const paid_amount = document.getElementById("paid_amount");
            let isAnyInputFocused = false;

            if (document.activeElement === inputCustomer) {
                isAnyInputFocused = true;
            } else if(document.activeElement === discount) {
                isAnyInputFocused = true;
            } else if(document.activeElement === paid_amount) {
                isAnyInputFocused = true;
            } else {
                // Check if any customer form input is focused
                customerFormInputs.forEach(function(inputElement) {
                    if (document.activeElement === inputElement) {
                        isAnyInputFocused = true;
                        return; // Exit the loop if any input is focused
                    }
                });
            }

            inputElements.forEach(function(inputElement) {
                if (document.activeElement === inputElement) {
                    isAnyInputFocused = true;
                    return; // Exit the loop if any input is focused
                }
            });

            multiPayInputs.forEach(function(inputElement) {
                if (document.activeElement === inputElement) {
                    isAnyInputFocused = true;
                    return; // Exit the loop if any input is focused
                }
            });

            if (isAnyInputFocused) {
            } else {
                var key = event.key;
                // Check if the key is a letter, number, or space
                if (/^[a-zA-Z0-9\u0600-\u06FF\u0660-\u0669\s]$/.test(key) || key === "Enter") {
                    var searchInput = document.getElementById("search");
                    if (key !== "Enter") {
                        searchInput.focus();
                    }else{
                    setTimeout(() => {
                        const inputElements = document.querySelectorAll('.productQuantity');
                        inputElements.forEach(function(inputElement) {
                        inputElement.addEventListener('click', function(event) {
                            this.select();
                            event.stopPropagation();
                        });
                    });

                // Handle the update
            }, 500); // Adjust the delay time as needed

                    }
                }
            }

            const inputs = document.querySelectorAll('input[type="text"]');

            // Add event listeners to each input
            inputs.forEach((input, index) => {
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowDown') {
                        // Move to the next input (if available)
                        const nextIndex = (index + 1) % inputs.length;
                        inputs[nextIndex].focus();
                    } else if (e.key === 'ArrowUp') {
                        // Move to the previous input (if available)
                        const prevIndex = (index - 1 + inputs.length) % inputs.length;
                        inputs[prevIndex].focus();
                    }
                });
            });


        });


        document.addEventListener('click', function (event) {
            var myDiv = document.getElementById('searchResults');
            var searchInput = document.getElementById('search');
            var targetElement = event.target;

            if(myDiv != null){
                // Only hide if click is outside both the search results and the search input
                if (!myDiv.contains(targetElement) && targetElement !== searchInput) {
                    myDiv.style.display = 'none';
                }
            }

            setTimeout(() => {
                const inputElements = document.querySelectorAll('.productQuantity');
                inputElements.forEach(function(inputElement) {
                    inputElement.addEventListener('click', function(event) {
                        this.select();
                        event.stopPropagation();
                    });
                });

                // Handle the update
            }, 500); // Adjust the delay time as needed
        });


        // Get all text inputs
        const audio = new Audio("{{url('')}}/storage/error.mp3");
        const inputElement = document.getElementById('quantity');


        function convertArabicToEnglish(input) {
            var arabicNumerals = ['١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩', '٠'];
            var englishNumerals = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
            for (var i = 0; i < arabicNumerals.length; i++) {
                var regex = new RegExp(arabicNumerals[i], 'g');
                input = input.replace(regex, englishNumerals[i]);
            }

            return input;
        }


</script>

<script>
    // Functions to show fulfillment modals
    function showBranchFulfillmentModal(event) {
        event.preventDefault();
        event.stopPropagation();
        const modal = new bootstrap.Modal(document.getElementById('branch_fulfillment_modal'));
        modal.show();
    }

    function showDeliveryFulfillmentModal(event) {
        event.preventDefault();
        event.stopPropagation();
        const modal = new bootstrap.Modal(document.getElementById('delivery_fulfillment_modal'));
        modal.show();
    }

    // Using event delegation for modal events since the modal may not be in the DOM initially
    document.addEventListener('DOMContentLoaded', function() {
        // Track which collapses were open before modal opened
        let openCollapseIds = [];

        // Use event delegation on document body for modal events
        document.body.addEventListener('show.bs.modal', function(event) {
            // Only handle events for our specific modal
            if (event.target.id !== 'prodcut_details') return;

            // Stop event propagation
            event.stopPropagation();

            // Store the current state of all collapses
            const openCollapses = document.querySelectorAll('.collapse.show');
            openCollapseIds = Array.from(openCollapses).map(el => el.id);
        });

        // Set focus to close button when modal is fully shown
        document.body.addEventListener('shown.bs.modal', function(event) {
            // Only handle events for our specific modal
            if (event.target.id !== 'prodcut_details') return;

            const closeButton = event.target.querySelector('.modal-footer .btn');
            if (closeButton) {
                closeButton.focus();
            }
        });

        // Restore collapse states after modal is hidden
        document.body.addEventListener('hidden.bs.modal', function(event) {
            // Only handle events for our specific modal
            if (event.target.id !== 'prodcut_details') return;

            // Restore collapse states
            openCollapseIds.forEach(id => {
                const collapseEl = document.getElementById(id);
                if (collapseEl && !collapseEl.classList.contains('show')) {
                    new bootstrap.Collapse(collapseEl).show();
                }
            });

            // Clear the stored IDs
            openCollapseIds = [];
        });
    });

</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Modal event handlers
        window.addEventListener('open-branch-fulfillment-modal', function() {
            const modal = new bootstrap.Modal(document.getElementById('branch_fulfillment_modal'));
            modal.show();
        });

        window.addEventListener('close-branch-fulfillment-modal', function() {
            const modalElement = document.getElementById('branch_fulfillment_modal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        });

        window.addEventListener('open-delivery-fulfillment-modal', function() {
            const modal = new bootstrap.Modal(document.getElementById('delivery_fulfillment_modal'));
            modal.show();
        });

        window.addEventListener('close-delivery-fulfillment-modal', function() {
            const modalElement = document.getElementById('delivery_fulfillment_modal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        });

        // Open customer modal when needed
        window.addEventListener('open-customer-modal', function() {
            const modal = new bootstrap.Modal(document.getElementById('kt_modal_1'));
            modal.show();

            // Focus on the customer search input field after the modal is fully shown
            document.getElementById('kt_modal_1').addEventListener('shown.bs.modal', function() {
                const inputCustomer = document.getElementById('inputCustomer');
                if (inputCustomer) {
                    setTimeout(() => {
                        inputCustomer.focus();
                    }, 50); // Small delay to ensure the modal is fully rendered
                }
            }, { once: true }); // Use once: true to ensure the event listener is removed after it's triggered
        });
    });
</script>
</div>
