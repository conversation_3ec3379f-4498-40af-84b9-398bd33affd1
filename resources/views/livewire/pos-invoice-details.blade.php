<div>
    @if($loading)
    <div class="d-flex justify-content-center py-10">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    @elseif($invoiceData)
    <div class="modal-body p-0">
        <div class="card card-flush">
            <!-- Invoice header -->
            <div class="card-header pt-5">
                <div class="d-flex flex-column flex-md-row justify-content-between">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold text-gray-800 fs-2">{{__('messages.invoice_details')}}</span>
                        <span class="text-gray-400 mt-1 fw-semibold fs-6">{{__('messages.invoice_id')}}: {{$invoiceData['invoice']->pretty_id}}</span>
                    </h3>
                    <div class="d-flex align-items-center mt-3 mt-md-0">
                        <a href="{{url('invoices/print'.'/'.$invoiceData['invoice']->id)}}" target="_blank" class="btn btn-sm btn-light-primary me-3">
                            <i class="ki-duotone ki-printer fs-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </i>
                            {{__('messages.print')}}
                        </a>
                        @if($invoiceData['invoice']->has_postpay && ($main_account || $permissions['invoices_receive']))
                        <a href="{{url('invoices/receive'.'/'.$invoiceData['invoice']->id)}}" class="btn btn-sm btn-light-warning me-3">
                            {{__('messages.receive_payment')}}
                        </a>
                        @endif
                        @if(($main_account || $permissions['invoices_return']) && $invoiceData['invoice']->invoice_type == 'sell')
                        <button type="button" class="btn btn-sm btn-light-danger" onclick="openReturnModal()">
                            {{__('messages.return')}}
                        </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Invoice details -->
            <div class="card-body pt-0">
                <div class="row mb-5">
                    <!-- Left column -->
                    <div class="col-md-6">
                        <div class="fw-bold fs-5 text-gray-800 mb-3">{{__('messages.invoice_info')}}</div>
                        <div class="d-flex flex-column">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-gray-600">{{__('messages.date_created')}}:</span>
                                <span class="fw-bold">{{\Carbon\Carbon::parse($invoiceData['invoice']->created_at)->format('Y-m-d H:i')}}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-gray-600">{{__('messages.type')}}:</span>
                                <span class="fw-bold {{$invoiceData['invoice']->invoice_type == 'sell' ? 'text-primary' : 'text-danger'}}">
                                    {{$invoiceData['invoice']->invoice_type == 'sell' ? __('messages.sale') : __('messages.return')}}
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-gray-600">{{__('messages.branch')}}:</span>
                                <span class="fw-bold">{{$invoiceData['branchName']}}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Right column -->
                    <div class="col-md-6">
                        <div class="fw-bold fs-5 text-gray-800 mb-3">{{__('messages.customer_info')}}</div>
                        <div class="d-flex flex-column">
                            @if($invoiceData['customer'])
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-gray-600">{{__('messages.name')}}:</span>
                                <span class="fw-bold">{{$invoiceData['customer']->name}}</span>
                            </div>
                            @if($invoiceData['customer']->phone_number)
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-gray-600">{{__('messages.phone_number')}}:</span>
                                <span class="fw-bold">{{$invoiceData['customer']->phone_number}}</span>
                            </div>
                            @endif
                            @if($invoiceData['customer']->vat_number)
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-gray-600">{{__('messages.vat_number')}}:</span>
                                <span class="fw-bold">{{$invoiceData['customer']->vat_number}}</span>
                            </div>
                            @endif
                            @else
                            <div class="text-gray-600">{{__('messages.no_customer')}}</div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Products table -->
                <div class="fw-bold fs-5 text-gray-800 mb-3">{{__('messages.products')}}</div>
                <div class="table-responsive">
                    <table class="table align-middle table-row-dashed fs-6 gy-4 mb-0">
                        <thead>
                            <tr class="border-bottom border-gray-200 text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                <th class="min-w-150px">{{__('messages.product')}}</th>
                                <th class="min-w-70px text-end">{{__('messages.qty')}}</th>
                                <th class="min-w-100px text-end">{{__('messages.unit_price')}}</th>
                                <th class="min-w-100px text-end">{{__('messages.total')}}</th>
                            </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">
                            @foreach($invoiceData['products'] as $product)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="ms-5">
                                            <div class="fw-bold">{{$product['product_name_ar']}}</div>
                                            @if($product['unit_name'])
                                            <div class="fs-7 text-muted">{{$product['unit_name']}}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="text-end">{{$product['cart_quantity']}}</td>
                                <td class="text-end">{{$product['price_tax_exclusive']}}</td>
                                <td class="text-end">{{$product['total_price_tax_exclusive']}}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Payment summary -->
                <div class="d-flex flex-column flex-md-row justify-content-between mt-5">
                    <!-- Payment methods -->
                    <div class="col-md-6 mb-5 mb-md-0">
                        <div class="fw-bold fs-5 text-gray-800 mb-3">{{__('messages.payment_methods')}}</div>
                        <div class="table-responsive">
                            <table class="table align-middle table-row-dashed fs-6 gy-4 mb-0">
                                <thead>
                                    <tr class="border-bottom border-gray-200 text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                        <th class="min-w-150px">{{__('messages.payment_method')}}</th>
                                        <th class="min-w-100px text-end">{{__('messages.amount')}}</th>
                                    </tr>
                                </thead>
                                <tbody class="fw-semibold text-gray-600">
                                    @foreach($invoiceData['paymentMethods'] as $payment)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="ms-5">
                                                    <div class="fw-bold">{{$payment->paymentMethod->name}}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">{{$payment->amount}}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Totals -->
                    <div class="col-md-5">
                        <div class="fw-bold fs-5 text-gray-800 mb-3">{{__('messages.summary')}}</div>
                        <div class="bg-light-secondary rounded p-5">
                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-gray-600">{{__('messages.subtotal')}}:</span>
                                <span class="fw-bold">{{$invoiceData['invoice']->total_tax_exclusive}}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-gray-600">{{__('messages.tax')}}:</span>
                                <span class="fw-bold">{{$invoiceData['invoice']->total_tax}}</span>
                            </div>
                            @if($invoiceData['invoice']->is_discounted)
                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-gray-600">{{__('messages.discount')}}:</span>
                                <span class="fw-bold text-danger">-{{$invoiceData['invoice']->total_discount_tax_rounded ?? 0}}</span>
                            </div>
                            @endif
                            <div class="separator my-5"></div>
                            <div class="d-flex justify-content-between">
                                <span class="fs-5 text-gray-800 fw-bold">{{__('messages.total')}}:</span>
                                <span class="fs-5 text-gray-800 fw-bold">{{$invoiceData['invoice']->total_tax_inclusive}}</span>
                            </div>
                            @if($invoiceData['invoice']->has_postpay)
                            <div class="separator my-5"></div>
                            <div class="d-flex justify-content-between mb-3">
                                <span class="text-gray-600">{{__('messages.paid_amount')}}:</span>
                                <span class="fw-bold">{{BCMathExtended\BC::add($invoiceData['total'], $invoiceData['totalReceived'], 10)}}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span class="fs-5 text-gray-800 fw-bold">{{__('messages.remaining_amount')}}:</span>
                                <span class="fs-5 text-danger fw-bold">{{BCMathExtended\BC::add($invoiceData['totalPostPay'], $invoiceData['totalReceived'], 10)}}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @else
    <div class="d-flex flex-column align-items-center py-10">
        <i class="ki-duotone ki-information-5 fs-5x text-primary mb-5">
            <span class="path1"></span>
            <span class="path2"></span>
            <span class="path3"></span>
        </i>
        <div class="text-gray-600 fs-3 text-center">{{__('messages.invoice_not_found')}}</div>
    </div>
    @endif

    <!-- Hidden iframe for printing -->
    <iframe style="display:none" id="invoice-frame" src="about:blank" name="invoice-frame" frameborder="0"></iframe>

    <!-- Return Invoice Modal -->
    <div class="modal fade" id="return_invoice_modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">{{__('messages.return')}}</h3>
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                        <i class="ki-duotone ki-cross fs-1"><span class="path1"></span><span class="path2"></span></i>
                    </div>
                    <!--end::Close-->
                </div>
                <div class="modal-body">
                    <div class="mb-5">
                        <h4 class="fw-bold text-gray-800 mb-3">{{__('messages.select_products_to_return')}}</h4>
                        <div class="alert alert-info mb-4">
                            <div class="d-flex align-items-center">
                                <i class="ki-duotone ki-information-5 fs-2 me-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <div>{{__('messages.return_quantity_info')}}</div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table align-middle table-row-dashed fs-6 gy-4 mb-0">
                                <thead>
                                    <tr class="border-bottom border-gray-200 text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                        <th class="min-w-150px">{{__('messages.product')}}</th>
                                        <th class="min-w-70px text-center">{{__('messages.quantity')}}</th>
                                        <th class="min-w-100px text-center">{{__('messages.unit_price')}}</th>
                                        <th class="min-w-100px text-center">{{__('messages.remaining')}}</th>
                                        <th class="min-w-100px text-center">{{__('messages.return_quantity')}}</th>
                                    </tr>
                                </thead>
                                <tbody class="fw-semibold text-gray-600">
                                    @if(isset($invoiceData['products']) && is_array($invoiceData['products']))
                                        @foreach($invoiceData['products'] as $index => $product)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="ms-5">
                                                        <div class="fw-bold">{{ $product['product_name_ar'] ?? 'N/A' }}</div>
                                                        @if(isset($product['unit_name']) && $product['unit_name'])
                                                        <div class="fs-7 text-muted">{{ $product['unit_name'] }}</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center">{{ $product['cart_quantity'] ?? 0 }}</td>
                                            <td class="text-center">{{ $product['price_tax_exclusive'] ?? 0 }}</td>
                                            <td class="text-center">
                                                @php
                                                    $isVariant = isset($product['is_variant']) && $product['is_variant'];
                                                    $hasVariantId = isset($product['product_variant_id']) && $product['product_variant_id'];
                                                    $productKey = ($isVariant && $hasVariantId)
                                                        ? $product['product_id'] . '-' . $product['product_variant_id']
                                                        : $product['product_id'];
                                                    $remainingQty = $sameProducts[$productKey] ?? 0;
                                                @endphp
                                                <span class="{{ $remainingQty > 0 ? 'text-success' : 'text-danger' }} fw-bold">
                                                    {{ $remainingQty }}
                                                </span>
                                            </td>
                                            <td class="text-center" style="display:flex;align-items:center;justify-content:center">
                                                <!--begin::Dialer-->
                                                <div class="input-group w-md-50"
                                                data-kt-dialer="true"
                                                data-kt-dialer-min="0"
                                                data-kt-dialer-max="{{ $remainingQty > 0 ? $remainingQty : ($product['cart_quantity'] ?? 0) }}"
                                                data-kt-dialer-step="1"
                                                data-kt-dialer-prefix="">

                                                <!--begin::Decrease control-->
                                                <button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="decrease">
                                                    <i class="ki-duotone ki-minus fs-2"></i>
                                                </button>
                                                <!--end::Decrease control-->

                                                <!--begin::Input control-->
                                                <input type="text" class="form-control text-center" placeholder="Quantity" name="return_qty_{{$index}}" value="0" data-product-id="{{ $product['product_id'] ?? 0 }}" data-kt-dialer-control="input"/>
                                                <!--end::Input control-->

                                                <!--begin::Increase control-->
                                                <button class="btn btn-icon btn-outline btn-active-color-primary" type="button" data-kt-dialer-control="increase">
                                                    <i class="ki-duotone ki-plus fs-2"></i>
                                                </button>
                                                <!--end::Increase control-->
                                                </div>
                                                <!--end::Dialer-->
                                            </td>
                                        </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="4" class="text-center">
                                                <div class="text-gray-600 fs-5">{{__('messages.no_products_found')}}</div>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">{{__('messages.cancel')}}</button>
                    <button type="button" class="btn btn-primary" id="confirm_return_btn" data-bs-dismiss="modal">{{__('messages.add')}}</button>
                </div>
            </div>
        </div>
    </div>
    <!-- End Return Invoice Modal -->

    <script>
        // Global function for opening the return modal
        function openReturnModal() {
            console.log('Opening return modal');
            const modalElement = document.getElementById('return_invoice_modal');
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Initialize quantity dialers after modal is shown
            setTimeout(() => {
                initQuantityDialers();
                initReturnSubmitButton();
            }, 300);
        }

        // Initialize quantity dialers
        function initQuantityDialers() {
            const dialers = document.querySelectorAll('[data-kt-dialer="true"]');

            dialers.forEach(dial => {
                const min = dial.getAttribute('data-kt-dialer-min');
                const max = dial.getAttribute('data-kt-dialer-max');
                const step = dial.getAttribute('data-kt-dialer-step');
                const input = dial.querySelector('[data-kt-dialer-control="input"]');
                const decreaseButton = dial.querySelector('[data-kt-dialer-control="decrease"]');
                const increaseButton = dial.querySelector('[data-kt-dialer-control="increase"]');

                // Set initial value
                input.value = min || '0';

                // Decrease button click handler
                decreaseButton.addEventListener('click', () => {
                    let value = parseInt(input.value);
                    value = Math.max(parseInt(min || '0'), value - parseInt(step || '1'));
                    input.value = value;
                });

                // Increase button click handler
                increaseButton.addEventListener('click', () => {
                    let value = parseInt(input.value);
                    value = Math.min(parseInt(max || '100'), value + parseInt(step || '1'));
                    input.value = value;
                });

                // Input change handler to enforce min/max
                input.addEventListener('change', () => {
                    let value = parseInt(input.value);
                    if (isNaN(value)) {
                        value = parseInt(min || '0');
                    }
                    value = Math.max(parseInt(min || '0'), Math.min(parseInt(max || '100'), value));
                    input.value = value;
                });
            });
        }

        // Initialize the return submit button
        function initReturnSubmitButton() {
            const confirmReturnBtn = document.getElementById('confirm_return_btn');
            if (confirmReturnBtn) {
                confirmReturnBtn.addEventListener('click', () => {
                    // Get all the return quantities
                    const returnQtyInputs = document.querySelectorAll('[name^="return_qty_"]');
                    const returnQuantities = {};

                    // Add each return quantity to the object
                    returnQtyInputs.forEach(input => {
                        returnQuantities[input.name] = input.value;
                    });

                    // Submit the return quantities to the Livewire component
                    Livewire.dispatch('returnInvoiceSubmit', {returnQuantities: returnQuantities});
                });
            }
        }

    </script>

    <script>
        document.addEventListener('livewire:initialized', () => {
            // Handle print invoice event
            Livewire.on('print-invoice', (data) => {
                try {
                    const invoiceId = data.invoiceId;
                    console.log('Printing invoice ID:', invoiceId);

                    // Create the print URL
                    const printUrl = `/invoices/print/${invoiceId}`;
                    console.log('Print URL:', printUrl);

                    // Try direct window approach first as it's more reliable
                    const printWindow = window.open(printUrl, '_blank');
                    if (printWindow) {
                        console.log('Print window opened successfully');
                        printWindow.focus();
                        // Let the browser handle printing
                    } else {
                        console.log('Failed to open print window, falling back to iframe');
                        // Fallback to iframe if popup is blocked
                        const iframe = document.getElementById('invoice-frame');
                        iframe.setAttribute('src', printUrl);

                        iframe.onload = function() {
                            console.log('Iframe loaded successfully');
                            try {
                                window.frames['invoice-frame'].focus();
                                window.frames['invoice-frame'].print();
                                console.log('Print command sent to iframe');
                            } catch (printError) {
                                console.error('Error printing from iframe:', printError);
                                alert('Could not print automatically. Please try again or use the browser print function.');
                            }
                        };
                    }
                } catch (error) {
                    console.error('Error in print-invoice handler:', error);
                    alert('An error occurred while trying to print. Please try again.');
                }
            });

            // Handle close-invoice-modal event
            Livewire.on('close-invoice-modal', () => {
                console.log('Closing invoice details modal');

                // Close the return modal if it's open
                const returnModal = bootstrap.Modal.getInstance(document.getElementById('return_invoice_modal'));
                if (returnModal) {
                    returnModal.hide();
                }

                // Close the invoice details modal
                const invoiceDetailsModal = bootstrap.Modal.getInstance(document.getElementById('invoice_details_modal'));
                if (invoiceDetailsModal) {
                    invoiceDetailsModal.hide();
                }
            });
        });
    </script>
</div>
