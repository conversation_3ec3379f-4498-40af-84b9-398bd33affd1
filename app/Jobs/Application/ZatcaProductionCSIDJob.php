<?php

namespace App\Jobs\Application;

use App\Actions\Application\Web\Notification\CreateNotificationAction;
use App\Dto\Application\Web\NotificationDto;
use App\Enums\Application\InstalledAppStatus;
use App\Enums\Application\NotificationTypeEnum;
use App\Models\MerchantCustomer;
use App\Models\MerchantRegistery;
use App\Models\MerchantsProduct;
use App\Models\MerchantUser;
use App\Models\OasisMarketplace;
use App\Services\GenerateZatcaCSIDService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ZatcaProductionCSIDJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public string $zatcaBinarySecurityToken,
        public string $zatcaSecret,
        public string $zatcaRequestId,
        public Model $step,
        public MerchantUser|MerchantRegistery $actor,
        public OasisMarketplace $marketplace,
        public int $merchantId,
        public int $businessId,
    )
    {
        $this->onQueue('zatca');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        setBusinessContext($this->merchantId, $this->businessId);

        $response = app(GenerateZatcaCSIDService::class)->generatePCSID(
            $this->zatcaBinarySecurityToken,
            $this->zatcaSecret,
            $this->zatcaRequestId,
        );

        if (isset($response['error'])) {
            \Log::alert($response['error']);
        }

        if ($this->actor instanceof MerchantUser) {
            $this->actor->setting()->update([
                'zatca_request_id' => data_get($response,'requestID'),
                'zatca_binary_security_token' => data_get($response,'binarySecurityToken'),
                'zatca_secret' => data_get($response,'secret'),
            ]);

        } else {
            $this->actor->update([
                'zatca_request_id' => data_get($response,'requestID'),
                'zatca_binary_security_token' => data_get($response,'binarySecurityToken'),
                'zatca_secret' => data_get($response,'secret'),
            ]);
        }

        $this->step->update(['pcsid' => true]);

        $this->updateMarketplaceInstalledAppStatus();
    }


    final public function updateMarketplaceInstalledAppStatus(): void
    {
        $merchantUser = MerchantUser::query()
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->first();
        $notCompletedRegistriesCount = $merchantUser->zatcaIntegrationStep()
            ->withoutGlobalScopes()
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->where('pcsid', false)
            ->count();

        if ($notCompletedRegistriesCount === 0) {
            $merchantUser->installedApps()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->where('app_id', $this->marketplace->id)
                ->update([
                    'status' => InstalledAppStatus::ACTIVE,
                ]);

            $notificationKey = 'zatca.integration.success';

            $users = MerchantUser::query()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->select('id')
                ->get();

            app(CreateNotificationAction::class)(
                NotificationDto::from([
                    'users' => $users,
                    'notifiable_type' => MerchantUser::class,
                    'notification_key' => $notificationKey,
                    'type' => NotificationTypeEnum::HIGH->value,
                    'data' => [
                        'app' => $this->marketplace->id,
                        'notification_key' => $notificationKey,
                    ],
                ])
            );

            $customers = MerchantCustomer::query()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->whereNotNull('vat_number')
                ->where(function ($query) {
                    $query->whereRaw('CHAR_LENGTH(vat_number) < 15')
                        ->orWhere('vat_number', 'not like', '3%3')
                        ->orWhereHas('address', function ($query) {
                            $query->whereNull('state_id')
                                ->orWhereNull('city_id')
                                ->orWhereNull('street')
                                ->orWhereNull('postal_code')
                                ->orWhereNull('building_number')
                                ->orWhereNull('district');
                        });
                })
                ->select('id', 'name', 'vat_number')
                ->get();

            if ($customers->count() > 0)
            {
                $customerNotificationKey = 'zatca.customers_need_fix';

                app(CreateNotificationAction::class)(
                    NotificationDto::from([
                        'users' => $users,
                        'notifiable_type' => MerchantUser::class,
                        'notification_key' => $customerNotificationKey,
                        'type' => NotificationTypeEnum::MEDIUM->value,
                        'data' => [
                            'app' => $this->marketplace->id,
                            'notification_key' => $customerNotificationKey,
                        ],
                    ])
                );
            }

            $products = MerchantsProduct::query()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->where(function ($query) {
                    $query->whereNull('name_en')
                        ->orWhereHas('tax', function ($query) {
                            $query->where('percentage', 0)
                                ->where('code', TaxCodeEnum::NONE->value);
                        });
                })
                ->select('id', 'name', 'name_en')
                ->get();

            if ($products->count() > 0)
            {
                $productNotificationKey = 'zatca.products_need_fix';

                app(CreateNotificationAction::class)(
                    NotificationDto::from([
                        'users' => $users,
                        'notifiable_type' => MerchantUser::class,
                        'notification_key' => $productNotificationKey,
                        'type' => NotificationTypeEnum::MEDIUM->value,
                        'data' => [
                            'app' => $this->marketplace->id,
                            'notification_key' => $productNotificationKey,
                        ],
                    ])
                );
            }
        }
    }
}
