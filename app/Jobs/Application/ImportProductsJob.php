<?php

namespace App\Jobs\Application;

use App\Models\MerchantBranch;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantsProduct;
use App\Services\BusinessContextService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ImportProductsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public array $products,
        public int $merchantId,
        public array $categories,
        public int $businessId
    )
    {
        $this->onQueue('imports');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::transaction(function (){
            $this->products = $this->mapfields($this->products);


            foreach ($this->products as &$product) {
                $product['business_id'] = $this->businessId;
            }


            MerchantsProduct::query()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->insert($this->products);

            // Use the inContext scope to apply the current context
            $latestProducts = MerchantsProduct::query()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->latest()
                ->take(count($this->products))
                ->select(['id', 'quantity','sell_price','sell_price_tax_exclusive','sell_price_tax_exclusive_rounded','wholesale_price','wholesale_price_tax_exclusive','wholesale_price_tax_exclusive_rounded'])
                ->get();

            // Use the inContext scope for branches too
            $branches = MerchantBranch::query()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->select('id')
                ->get();

            $branchQuantities = [];
            foreach ($latestProducts as $product) {
                foreach ($branches as $branch) {
                    $branchQuantities[] = [
                        'merchant_id' => $this->merchantId,
                        'business_id' => $this->businessId,
                        'branch_id' => $branch->id,
                        'product_id' => $product->id,
                        'quantity' => $product->quantity ?? 0,
                        'sell_price' => $product->sell_price ?? 0,
                        'sell_price_tax_exclusive' => $product->sell_price_tax_exclusive ?? 0,
                        'sell_price_tax_exclusive_rounded' => $product->sell_price_tax_exclusive_rounded ?? 0,
                        'wholesale_price' => $product->wholesale_price ?? 0,
                        'wholesale_price_tax_exclusive' => $product->wholesale_price_tax_exclusive ?? 0,
                        'wholesale_price_tax_exclusive_rounded' => $product->wholesale_price_tax_exclusive_rounded ?? 0,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }
            }

            if (!empty($branchQuantities)) {
                MerchantProductBranchQuantity::query()
                    ->withoutGlobalScopes()
                    ->where('merchant_id', $this->merchantId)
                    ->where('business_id', $this->businessId)
                    ->insert($branchQuantities);
            }
        });
    }

    private function mapfields(array $products): array
    {
        $newProducts = [];

        foreach ($products as $product) {
            if (isset($product['category_id'])) {
                $categoryName = strtolower($product['category_id']);

                if (array_key_exists($categoryName, array_change_key_case($this->categories))) {
                    $product['category_id'] = $this->categories[$categoryName];
                } else {
                    $product['category_id'] = null;
                }
            }
            $newProducts[] = $product;
        }

        return $newProducts;
    }
}
