<?php

namespace App\Jobs\Application;

use App\Actions\Application\Web\Notification\CreateNotificationAction;
use App\Dto\Application\Web\NotificationDto;
use App\Enums\Application\NotificationTypeEnum;
use App\Models\MerchantUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class ZatcaUsersQuarterReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $merchantId,
        public int $businessId,
    ){
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $notificationKey = 'zatca.quarter_reminder';

         $users = MerchantUser::query()
             ->withoutGlobalScopes()
             ->where('merchant_id', $this->merchantId)
             ->where('business_id', $this->businessId)
             ->select('id')
             ->get();


        app(CreateNotificationAction::class)(
            NotificationDto::from([
                'users' => $users,
                'notifiable_type' => MerchantUser::class,
                'notification_key' => $notificationKey,
                'type' => NotificationTypeEnum::MEDIUM->value,
                'data' => [
                    'notification_key' => $notificationKey,
                ],
            ])
        );
    }
}
