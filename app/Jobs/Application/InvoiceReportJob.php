<?php

namespace App\Jobs\Application;

use App\Actions\Application\Web\Notification\CreateNotificationAction;
use App\Dto\Application\Web\NotificationDto;
use App\Enums\Application\NotificationTypeEnum;
use App\Enums\Application\Zatca\ZatcaInvoiceStatus;
use App\Models\MerchantInvoice;
use App\Models\MerchantUser;
use App\Services\SendInvoiceService;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class InvoiceReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * Create a new job instance.
     */
    public function __construct(
         public string $zatcaBinarySecurityToken,
         public string $zatcaSecret,
         public array $signedData,
         public int $invoiceId,
         public int $merchantId,
         public int $businessId
    )
    {
        $this->onQueue('zatca');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $response = app(SendInvoiceService::class)->simplified(
            userName: $this->zatcaBinarySecurityToken,
            secret: $this->zatcaSecret,
            invoiceHash: data_get($this->signedData, 'invoiceHash'),
            uuid: data_get($this->signedData, 'uuid'),
            invoice: data_get($this->signedData, 'invoice')
        );

        if ($response instanceof ConnectException || isset($response['error'])) {
            throw new \Error($response['error']);
        }

        if (
            $response['validationResults']['status'] &&
            $response['reportingStatus'] === ZatcaInvoiceStatus::REPORTED->value
        ) {
            MerchantInvoice::query()
                ->withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->whereId($this->invoiceId)->update([
                    'zatca_status' => ZatcaInvoiceStatus::REPORTED->value,
                    'zatca_encoded_invoice' => null,
                ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $invoice = MerchantInvoice::query()
            ->withoutGlobalScopes()
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->whereId($this->invoiceId)
            ->first();

        $invoice->update([
            'zatca_status' => ZatcaInvoiceStatus::FAILED->value,
            'zatca_encoded_invoice' => data_get($this->signedData, 'invoice'),
        ]);

        $notificationKey = 'zatca.report_invoice_failed';

        app(CreateNotificationAction::class)(
            NotificationDto::from([
                'users' => getUsers(),
                'notifiable_type' => MerchantUser::class,
                'notification_key' => $notificationKey,
                'type' => NotificationTypeEnum::MEDIUM->value,
                'data' => [
                    'invoice' => $invoice->pretty_id,
                    'notification_key' => $notificationKey,
                ],
            ])
        );

        Log::error('Invoice Report Job failed', [
            'invoiceId' => $this->invoiceId,
            'exception' => $exception->getMessage(),
            'merchantId' => $this->merchantId,
            'businessId' => $this->businessId
        ]);
    }
}
