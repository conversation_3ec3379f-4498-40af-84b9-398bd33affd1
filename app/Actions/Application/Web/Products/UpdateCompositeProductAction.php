<?php

namespace App\Actions\Application\Web\Products;

use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Http\Requests\Application\Web\Product\UpdateCompositeProductRequest;
use App\Models\MerchantBranch;
use App\Models\MerchantCompositeComponent;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantProductTag;
use App\Models\MerchantsProduct;
use App\Models\MerchantTag;
use App\Models\TaxType;
use BCMathExtended\BC;
use Illuminate\Support\Facades\DB;

class UpdateCompositeProductAction
{
    public function __invoke(MerchantsProduct $product, UpdateCompositeProductRequest $request)
    {
        $request['returnable'] = $request->returnable ? $request->returnable : 0;
        $request['purchaseable'] = $request->purchaseable ? $request->purchaseable : 0;
        $request['sellable'] = $request->sellable ? $request->sellable : 0;

        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('merchant_products');
        } else {
            $path = $product->thumbnail;
        }

        if ($request['zero_tax']) {
            $taxQuery = TaxType::where('code', TaxCodeEnum::NONE->value)->first();
            $taxPercentage = $taxQuery->percentage;
            $tax_id = $taxQuery->id;
        } else {
            $taxPercentage = TaxType::where( "id", $request->tax_id)->first()->percentage;
            $tax_id = $request->tax_id;
        }

        $merchantBranches = MerchantBranch::query()->pluck('id');

        DB::transaction(function () use ($product, $request, $path, $merchantBranches, $tax_id, $taxPercentage) {
            // Handle tags in a single operation
            $newTags = [];
            if ($request->input('tags')) {
                $newTags = collect(json_decode($request->input('tags')))
                    ->map(function($tag) {
                        return trim($tag->value);
                    })
                    ->toArray();
            }

            // Get existing tag names
            $existingTags = MerchantProductTag::with('tag')
                ->where('product_id', $product->id)
                ->get()
                ->pluck('tag.name')
                ->toArray();

            // Find tags to remove
            $tagsToRemove = array_diff($existingTags, $newTags);

            // Find tags to add
            $tagsToAdd = array_diff($newTags, $existingTags);

            // Remove tags that are no longer needed
            if (!empty($tagsToRemove)) {
                $tagIds = MerchantTag::whereIn('name', $tagsToRemove)
                    ->pluck('id');

                MerchantProductTag::where('product_id', $product->id)
                    ->whereIn('tag_id', $tagIds)
                    ->delete();
            }

            // Add new tags
            foreach ($tagsToAdd as $tagName) {
                $tag = MerchantTag::firstOrCreate(
                    ['name' => $tagName]
                );

                MerchantProductTag::firstOrCreate([
                    'product_id' => $product->id,
                    'tag_id' => $tag->id,
                ]);
            }


            // Update product basic information
            $product->update([
                "name" => $request->get('name'),
                "name_en" => $request->get('name_en'),
                'thumbnail' => $path,
                'barcode' => $request->barcode,
                'tax_id' => $tax_id,
                'category_id' => $request->get('category'),
                'status' => $request->get('status'),
                'returnable' => $request->get('returnable'),
                'tax_schema' => $request->get('tax_schema'),
                'purchaseable' => $request->get('purchaseable'),
                'sellable' => $request->get('sellable'),
            ]);

            // Handle composite components
            if ($request->has('products')) {
                $newProductIds = array_keys($request->input('products'));
                MerchantCompositeComponent::where('composite_product_id', $product->id)
                    ->whereNotIn('component_product_id', $newProductIds)
                    ->delete();

                $compositeComponents = MerchantCompositeComponent::where('composite_product_id', $product->id);

                // Update or create components from the request
                foreach ($request->input('products') as $id => $quantity) {
                    if ($compositeComponents->where('component_product_id', $id)->exists()) {
                        $compositeComponents->where('component_product_id', $id)
                            ->update(['quantity' => $quantity]);
                    } else {
                        MerchantCompositeComponent::create([
                            'composite_product_id' => $product->id,
                            'component_product_id' => $id,
                            'quantity' => $quantity,
                        ]);
                    }
                }
            }

            // Update branch quantities and prices
            $priceInputs = $request->input('price_per_branch');
            $wholesaleInputs = $request->input('wholesale_price_per_branch');

            foreach ($merchantBranches as $branch) {
                $sellPricePerBranch = isset($priceInputs[$branch]) ? $priceInputs[$branch] : 0;
                $wholesalePricePerBranch = isset($wholesaleInputs[$branch]) ? $wholesaleInputs[$branch] : 0;

                $sellPricePerBranchTaxExclusive = Helper::excludeTax($sellPricePerBranch, $taxPercentage);
                $wholesalePricePerBranchTaxExclusive = Helper::excludeTax($wholesalePricePerBranch, $taxPercentage);

                MerchantProductBranchQuantity::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'branch_id' => $branch,
                    ],
                    [
                        'quantity' => 0,
                        'sell_price' => $sellPricePerBranch,
                        'wholesale_price' => $wholesalePricePerBranch,
                        'sell_price_tax_exclusive' => $sellPricePerBranchTaxExclusive,
                        'sell_price_tax_exclusive_rounded' => BC::round($sellPricePerBranchTaxExclusive, 2),
                        'wholesale_price_tax_exclusive' => $wholesalePricePerBranchTaxExclusive,
                        'wholesale_price_tax_exclusive_rounded' => BC::round($wholesalePricePerBranchTaxExclusive, 2),
                    ]
                );
            }
        });

        return redirect('/products')->with('success', __('messages.updated_successfully'));
    }
}
