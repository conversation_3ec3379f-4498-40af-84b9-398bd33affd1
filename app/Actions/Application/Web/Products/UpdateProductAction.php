<?php

namespace App\Actions\Application\Web\Products;

use App\Enums\Application\ProductTypeEnum;
use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Models\MerchantInventoryChange;
use App\Models\MerchantProductTag;
use App\Models\MerchantsProduct;
use App\Models\MerchantTag;
use App\Models\TaxType;
use App\Rules\EnglishCharactersOnlyRule;
use App\Services\BusinessContextService;
use BCMathExtended\BC;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;

class UpdateProductAction
{
    public function __invoke(MerchantsProduct $product, Request $request)
    {
        $user = Auth::user();
        $originalInputs = $request->session()->get('mproduct');

        $originalInputs['cost'] = !$originalInputs['cost'] ? 0 : $originalInputs['cost'];
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        $activeProductCount = MerchantsProduct::query()->active()->count();

        if ($activeProductCount > $user->plan->product_numbers && $user->plan->product_numbers != 0 && $request->status != 'disabled') {
            return View("theme::marble.needs-subscription", ['title' => 'تعديل منتج', 'message' => 'باقتك تسمح باضافة 1000 منتج بحد أقصى.']);
        }

        foreach ($originalInputs['branches'] as $key => $branch) {
            $branch = $branch->branchQuantity;
            $originalInputs['branch']['quantity'][$branch['branch_id']] = !$branch['quantity'] ? 0 : $branch['quantity'];
            $originalInputs['branch']['price_per_branch'][$branch['branch_id']] = !$branch['sell_price'] ? 0 : $branch['sell_price'];
            $originalInputs['branch']['wholesale_price_per_branch'][$branch['branch_id']] = !$branch['wholesale_price'] ? 0 : $branch['wholesale_price'];
        }
        unset($originalInputs['branches']);
        $request['zero_tax'] = !$request?->zero_tax ? false : true;
        $request['returnable'] = $request->returnable?$request->returnable:0;
        $request['purchaseable'] = $request->purchaseable?$request->purchaseable:0;
        $request['sellable'] = $request->sellable?$request->sellable:0;


        $roundingPoints = getRoundingPoints($product->type);

        $changedInputs = [];

        foreach ($originalInputs as $name => $value) {
            if ($request->hasFile('thumbnail')) {
                $changedInputs['thumbnail'] = $request->file('thumbnail');
            }
            // getting the name originally as branches
            if ($name == 'branch') {
                foreach ($originalInputs['branch'] as $key => $result) {
                    if ($key == 'quantity') {
                        foreach ($result as $branch => $quantity) {
                            if (isset($request['quantity']) && isset($request['quantity'][$branch]) && $request['quantity'][$branch] != $quantity) {
                                $changedInputs['quantity'][$branch] = !$request['quantity'][$branch] ? 0 : $request['quantity'][$branch];
                            } else {
                                unset($changedInputs['quantity'][$branch]);
                                $originalInputs['quantity'][$branch] = !$request['quantity'][$branch] ? 0 : $request['quantity'][$branch];
                            }
                        }
                    } elseif ($key == 'price_per_branch') {
                        foreach ($result as $branch => $sell_price)
                            if ($request['price_per_branch'][$branch] != $sell_price) {
                                $changedInputs['price_per_branch'][$branch] = !$request['price_per_branch'][$branch] ? 0 : $request['price_per_branch'][$branch];
                            } else {
                                unset($changedInputs['price_per_branch'][$branch]);
                                $originalInputs['price_per_branch'][$branch] = !$request['price_per_branch'][$branch] ? 0 : $request['price_per_branch'][$branch];
                            }
                    } elseif ($key == 'wholesale_price_per_branch') {
                        foreach ($result as $branch => $wholesale_price)
                            if ($request['wholesale_price_per_branch'][$branch] != $wholesale_price) {
                                $changedInputs['wholesale_price_per_branch'][$branch] = !$request['wholesale_price_per_branch'][$branch] ? 0 : $request['wholesale_price_per_branch'][$branch];
                            } else {
                                unset($changedInputs['wholesale_price_per_branch'][$branch]);
                                $originalInputs['wholesale_price_per_branch'][$branch] = !$request['wholesale_price_per_branch'][$branch] ? 0 : $request['wholesale_price_per_branch'][$branch];
                            }
                    }
                }
            } else if ($request->input($name) != $value && $name != 'thumbnail') {
                if ($name == 'cost') {
                    // this so if the user does not have permissions it would take the cost from the db (originalInputs)
                    if ($main_account || ($permissions['products_cost_show'] && $permissions['products_cost_edit'])) {
                        $changedInputs[$name] = !$request->input($name) ? 0 : $request->input($name);
                    } else {
                        $changedInputs[$name] = !$originalInputs['cost'] ? 0 : $originalInputs['cost'];
                    }
                }  elseif ($name == 'returnable' || $name == 'purchaseable' || $name == 'sellable') {
                    $changedInputs[$name] = $request[$name] ? $request[$name] : 0;
                } elseif ($name == 'tax_id') {

                    $taxType = TaxType::where('id', $value)->first();
                    if ($taxType->id !== $request->get('tax_id') && !$request->get('zero_tax')) {
                        $changedInputs['tax_id'] = $request->get('tax_id');
                    } elseif ($taxType->percentage == 0 && $taxType->code == TaxCodeEnum::NONE->value && $request->zero_tax) {
                        $changedInputs['tax_id'] = 'unchanged';
                    }
                } else {
                    $changedInputs[$name] = $request[$name];
                }
            } else {
                $changedInputs[$name] = "unchanged";
            }
        }

        foreach ($changedInputs as $key => $value) {
            if ($value === 'unchanged') {
                unset($changedInputs[$key]);
            }
        }

        $validator = Validator::make($changedInputs, [
            'name' => [
                'sometimes',
                'string',
                Rule::unique('merchants_products')->where(function ($query) {
                    return $query->where('merchant_id', BusinessContextService::getMerchantId())
                        ->where('business_id', BusinessContextService::getBusinessId())
                        ->whereNull('deleted_at');
                }),
            ],
            'name_en' => [
                'sometimes',
                'nullable',
                'string',
                Rule::unique('merchants_products')->where(function ($query) {
                    return $query->where('merchant_id', BusinessContextService::getMerchantId())
                        ->where('business_id', BusinessContextService::getBusinessId())
                        ->whereNull('deleted_at');
                }),
                Rule::requiredIf(getCurrentUser()->isZatcaEnabled),
                new EnglishCharactersOnlyRule()
            ],
            'thumbnail' => [
                File::image()->max(1024 * 5)
            ],
            'barcode' => [
                'sometimes',
                'string',
                'min_digits:3',
                Rule::unique('merchants_products')->where(function ($query) {
                    return $query->where('merchant_id', BusinessContextService::getMerchantId())
                        ->where('business_id', BusinessContextService::getBusinessId())
                        ->whereNull('deleted_at');
                })
            ],
            'cost' => ['sometimes', 'numeric'],
            'tax_id' => ['nullable', 'integer', 'exists:tax_types,id'],
            'zero_tax' => ['sometimes', 'boolean'],
            'quantity.*' => ['sometimes', 'numeric'],
            'price_per_branch.*' => ['sometimes', 'numeric'],
            'wholesale_price_per_branch.*' => ['sometimes', 'numeric'],
            'status' => ['sometimes', 'string'],
            'returnable' => ['sometimes', 'boolean'],
            'purchaseable' => ['sometimes', 'boolean'],
            'sellable' => ['sometimes', 'boolean'],
            'tax_schema' => ['nullable', 'string'],
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        $path = !empty($request->file('thumbnail')) ? $request->file('thumbnail')->store('merchant_products') : '';

        if (!empty($path)) {
            $changedInputs['thumbnail'] = $path;
        }

        if(isset($changedInputs['tax_id'])){
            $taxPercentage = TaxType::where('id', $request->tax_id)
                ->first()
                ->percentage;
            $tax_id =  $request->tax_id;
            $changedInputs['tax_id'] = $tax_id;
            unset($changedInputs['zero_tax']);
        }

        if(isset($changedInputs['zero_tax']) && $changedInputs['zero_tax']){
            $taxQuery = TaxType::where('code', TaxCodeEnum::NONE->value)
                ->first();
            $taxPercentage = $taxQuery->percentage;
            $tax_id = $taxQuery->id;
            $changedInputs['tax_id'] = $tax_id;
            unset($changedInputs['zero_tax']);
        }

        if (!in_array('tax_id', array_keys($changedInputs))) {
            $product->load('tax');
            $taxPercentage = $product->tax->percentage;
        }


        if (in_array("price_per_branch", array_keys($changedInputs))) {
            foreach ($changedInputs['price_per_branch'] as $key => $sell_price) {

                if ($product->type === ProductTypeEnum::WEIGHTED->value)
                {
                    $sell_price = Helper::setCostOrPriceInBaseUnit($sell_price);
                }

                $changedInputs['price_per_branch'][$key] = [];
                $changedInputs['price_per_branch'][$key]['sell_price'] =  $sell_price;
                $sellPriceTaxExclusive = Helper::excludeTax($changedInputs['price_per_branch'][$key]['sell_price'], $taxPercentage);
                $changedInputs['price_per_branch'][$key]['sell_price_tax_exclusive'] = $sellPriceTaxExclusive;
                $changedInputs['price_per_branch'][$key]['sell_price_tax_exclusive_rounded'] = BC::round($sellPriceTaxExclusive, $roundingPoints);
            }
        }
        if (in_array("wholesale_price_per_branch", array_keys($changedInputs))) {
            if ($main_account || ($permissions['products_wholesale'])) {
                foreach ($changedInputs['wholesale_price_per_branch'] as $key => $wholesale_price) {

                    if ($product->type === ProductTypeEnum::WEIGHTED->value)
                    {
                        $wholesale_price = Helper::setCostOrPriceInBaseUnit($wholesale_price);
                    }

                    $changedInputs['wholesale_price_per_branch'][$key] = [];
                    $changedInputs['wholesale_price_per_branch'][$key]['wholesale_price'] =  $wholesale_price;
                    $sellPriceTaxExclusive = Helper::excludeTax($changedInputs['wholesale_price_per_branch'][$key]['wholesale_price'], $taxPercentage);
                    $changedInputs['wholesale_price_per_branch'][$key]['wholesale_price_tax_exclusive'] = $sellPriceTaxExclusive;
                    $changedInputs['wholesale_price_per_branch'][$key]['wholesale_price_tax_exclusive_rounded'] = BC::round($sellPriceTaxExclusive, $roundingPoints);
                }
            }
        }


        if (in_array("cost", array_keys($changedInputs))) {
            $cost = $request->cost;
            if ($product->type === ProductTypeEnum::WEIGHTED->value)
            {
                $cost = Helper::setCostOrPriceInBaseUnit($cost);
            }

            $costTaxExclusive = Helper::excludeTax($cost, $taxPercentage);
            $changedInputs['cost'] = $cost;
            $changedInputs['cost_tax_exclusive'] = $costTaxExclusive;
            $changedInputs['cost_tax_exclusive_rounded'] = BC::round($costTaxExclusive, $roundingPoints);
        }

        if (in_array("tax_id", array_keys($changedInputs))) {
            foreach ($request['price_per_branch'] as $key => $sell_price) {

                if ($product->type === ProductTypeEnum::WEIGHTED->value)
                {
                    $sell_price = Helper::setCostOrPriceInBaseUnit($sell_price);
                }

                $changedInputs['price_per_branch'][$key] = [];
                $changedInputs['price_per_branch'][$key]['sell_price'] =  $sell_price;
                $price_per_branch_tax_exclusive = Helper::excludeTax($changedInputs['price_per_branch'][$key]['sell_price'], $taxPercentage);
                $changedInputs['price_per_branch'][$key]['sell_price_tax_exclusive'] = $price_per_branch_tax_exclusive;
                $changedInputs['price_per_branch'][$key]['sell_price_tax_exclusive_rounded'] = BC::round($price_per_branch_tax_exclusive, $roundingPoints);
            }

            foreach ($request['wholesale_price_per_branch'] as $key => $wholesale_price) {

                if ($product->type === ProductTypeEnum::WEIGHTED->value)
                {
                    $wholesale_price = Helper::setCostOrPriceInBaseUnit($wholesale_price);
                }

                $changedInputs['wholesale_price_per_branch'][$key] = [];
                $changedInputs['wholesale_price_per_branch'][$key]['wholesale_price'] =  $wholesale_price;
                $wholesale_price_per_branch_tax_exclusive = Helper::excludeTax($changedInputs['wholesale_price_per_branch'][$key]['wholesale_price'], $taxPercentage);
                $changedInputs['wholesale_price_per_branch'][$key]['wholesale_price_tax_exclusive'] = $wholesale_price_per_branch_tax_exclusive;
                $changedInputs['wholesale_price_per_branch'][$key]['wholesale_price_tax_exclusive_rounded'] = BC::round($wholesale_price_per_branch_tax_exclusive, $roundingPoints);
            }

            $cost = $request->cost;
            if ($product->type === ProductTypeEnum::WEIGHTED->value)
            {
                $cost = Helper::setCostOrPriceInBaseUnit($cost);
            }

            $costTaxExclusive = Helper::excludeTax($cost, $taxPercentage);
            $changedInputs['cost'] = $cost;
            $changedInputs['cost_tax_exclusive'] = $costTaxExclusive;
            $changedInputs['cost_tax_exclusive_rounded'] = BC::round($costTaxExclusive, $roundingPoints);
        }
        if (in_array("category", array_keys($changedInputs))) {
            $changedInputs['category_id'] = $changedInputs['category'];
            unset($changedInputs['category']);
        }

        if (in_array('tax_id', array_keys($changedInputs)) || in_array('cost', array_keys($changedInputs)) || in_array('quantity', array_keys($changedInputs))) {
            $productId = $product->id;

            $cost = $request->cost;
            if ($product->type === ProductTypeEnum::WEIGHTED->value) {
                $cost = Helper::setCostOrPriceInBaseUnit($cost);
            }

            // Only process quantity changes for non-composite, non-service products
            if (in_array('quantity', array_keys($changedInputs)) &&
                $product->type !== ProductTypeEnum::SERVICE->value) {
                $costTaxExclusive = Helper::excludeTax($cost, $taxPercentage);
            }

            if (array_key_exists('quantity', $changedInputs) &&
                $product->type !== ProductTypeEnum::SERVICE->value) {
                foreach ($changedInputs['quantity'] as $branchId => $each) {
                    $branchQuantity = $product->branchQuantities()->where('branch_id', $branchId)->first();
                    if ($product->type === ProductTypeEnum::WEIGHTED->value) {
                        $branchQuantity = Helper::setQuantityInBaseUnit($branchQuantity->quantity);
                    }else{
                        $branchQuantity = $branchQuantity->quantity;
                    }

                    MerchantInventoryChange::create([
                        'product_id' => $productId,
                        'branch_id' => $branchId,
                        'cost' =>  $cost,
                        'quantity_before' => $branchQuantity,
                        'quantity' => $each,
                        'cost_tax_exclusive' => $costTaxExclusive,
                        'cost_tax_exclusive_rounded' => BC::round($costTaxExclusive, $roundingPoints),
                        'manually_impacts_cost' => 1,
                    ]);
                }
            }
        }

        DB::transaction(function () use ($product, $request, $changedInputs,$originalInputs) {

            // Handle tags in a single operation
            $newTags = [];
            if ($request->input('tags')) {
                $newTags = collect(json_decode($request->input('tags')))
                    ->map(function($tag) {
                        return trim($tag->value);
                    })
                    ->toArray();
            }

            // Get existing tag names
            $existingTags = $originalInputs['existingTags']
                ? $originalInputs['existingTags']->pluck('tag.name')->toArray()
                : [];

            // Find tags to remove
            $tagsToRemove = array_diff($existingTags, $newTags);

            // Find tags to add
            $tagsToAdd = array_diff($newTags, $existingTags);

            // Remove tags that are no longer needed
            if (!empty($tagsToRemove)) {
                $tagIds = MerchantTag::whereIn('name', $tagsToRemove)
                    ->pluck('id');

                MerchantProductTag::where('product_id', $product->id)
                    ->whereIn('tag_id', $tagIds)
                    ->delete();
            }

            // Add new tags
            foreach ($tagsToAdd as $tagName) {
                $tag = MerchantTag::firstOrCreate(['name' => $tagName]);

                MerchantProductTag::firstOrCreate([
                    'product_id' => $product->id,
                    'tag_id' => $tag->id,
                ]);
            }

            if(!empty($changedInputs)){
                if(in_array("quantity", array_keys($changedInputs)) &&
                   count($changedInputs) > 0 &&
                   $product->type !== ProductTypeEnum::SERVICE->value) {
                    foreach($changedInputs['quantity'] as $branchId => $quantity){
                        if ($product->type === ProductTypeEnum::WEIGHTED->value) {
                            $quantity = Helper::setQuantityInBaseUnit($quantity);
                        }

                        $product->branchQuantities()->where('branch_id',$branchId)->update(['quantity' => $quantity]);
                    }
                    unset($changedInputs['quantity']);
                }
                if(in_array("price_per_branch", array_keys($changedInputs)) && count($changedInputs) > 0){
                    foreach ($changedInputs['price_per_branch'] as $branchId => $productPrices) {
                        $sell_price = $productPrices['sell_price'];
                        $sell_price_tax_exclusive = $productPrices['sell_price_tax_exclusive'];
                        $sell_price_tax_exclusive_rounded = $productPrices['sell_price_tax_exclusive_rounded'];

                        $product->branchQuantities()->where('branch_id', $branchId)->update(
                            [
                                'sell_price' => $sell_price,
                                'sell_price_tax_exclusive' => $sell_price_tax_exclusive,
                                'sell_price_tax_exclusive_rounded' => $sell_price_tax_exclusive_rounded,
                            ]
                        );
                    }
                    unset($changedInputs['price_per_branch']);
                }
                if(in_array("wholesale_price_per_branch", array_keys($changedInputs)) && count($changedInputs) > 0){
                    foreach ($changedInputs['wholesale_price_per_branch'] as $branchId => $productPrices) {
                        $wholesale_price = $productPrices['wholesale_price'];
                        $wholesale_price_tax_exclusive = $productPrices['wholesale_price_tax_exclusive'];
                        $wholesale_price_tax_exclusive_rounded = $productPrices['wholesale_price_tax_exclusive_rounded'];

                        $product->branchQuantities()->where('branch_id', $branchId)->update(
                            [
                                'wholesale_price' => $wholesale_price,
                                'wholesale_price_tax_exclusive' => $wholesale_price_tax_exclusive,
                                'wholesale_price_tax_exclusive_rounded' => $wholesale_price_tax_exclusive_rounded,
                            ]
                        );
                    }
                    unset($changedInputs['wholesale_price_per_branch']);
                }

                $product->update($changedInputs);
            }
        });

        if (request()->has('redirect_url'))
        {
            return redirect(request()->get('redirect_url'))->with('success', __('messages.updated_successfully'));
        }

        return redirect('/products')->with('success', __('messages.updated_successfully'));
    }
}
