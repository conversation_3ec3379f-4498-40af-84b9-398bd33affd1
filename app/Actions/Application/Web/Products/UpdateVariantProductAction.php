<?php

namespace App\Actions\Application\Web\Products;

use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Http\Requests\UpdateVariantProductRequest;
use App\Models\MerchantBranch;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantProductVariant;
use App\Models\MerchantProductVariantAttribute;
use App\Models\MerchantsProduct;
use App\Models\TaxType;
use BCMathExtended\BC;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateVariantProductAction
{
    public function __invoke(MerchantsProduct $product, UpdateVariantProductRequest $request)
    {
        // Process checkbox fields
        $request['returnable'] = $request->returnable ? 1 : 0;
        $request['purchaseable'] = $request->purchaseable ? 1 : 0;
        $request['sellable'] = $request->sellable ? 1 : 0;

        // Handle product thumbnail
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('merchant_products');
        } else {
            $path = $product->thumbnail;
        }

        // Get tax information
        if ($request['zero_tax']) {
            $taxQuery = TaxType::where('code', TaxCodeEnum::NONE->value)->first();
            $taxPercentage = $taxQuery->percentage;
            $taxId = $taxQuery->id;
        } else {
            $taxQuery = TaxType::where("id", $request->tax_id)->first();
            $taxPercentage = $taxQuery ? $taxQuery->percentage : 0;
            $taxId = $request->tax_id;
        }

        DB::transaction(function () use ($product, $request, $path, $taxId, $taxPercentage) {
            // 1. Update the main product information
            $product->update([
                "name" => $request->get('name'),
                "name_en" => $request->get('name_en'),
                'thumbnail' => $path,
                'tax_id' => $taxId,
                'category_id' => $request->get('category'),
                'status' => $request->get('status'),
                'returnable' => $request->get('returnable'),
                'tax_schema' => $request->get('tax_schema'),
                'purchaseable' => $request->get('purchaseable'),
                'sellable' => $request->get('sellable'),
            ]);

            // 2. Get all existing product variants for this product
            $existingVariants = MerchantProductVariant::where('product_id', $product->id)
                ->with('variantAttributes')
                ->get();
            $existingVariantIds = $existingVariants->pluck('id')->toArray();

            Log::debug('Updating variant product', [
                'product_id' => $product->id,
                'existing_variants' => $existingVariantIds,
                'generated_products_count' => count($request->generatedProducts)
            ]);

            // 3. Track which variant IDs are still in use to determine which ones to delete
            $processedVariantIds = [];

            // 4. Process each generated product
            foreach ($request->generatedProducts as $generatedProduct) {
                if (empty($generatedProduct['product_name'])) {
                    continue;
                }

                // Extract variant and option IDs
                $variantIds = $generatedProduct['variant_id'];
                $optionIds = $generatedProduct['variant_option_id'];

                // Convert from JSON string to array if needed
                if (is_string($variantIds)) {
                    $variantIds = json_decode($variantIds, true);
                }
                if (is_string($optionIds)) {
                    $optionIds = json_decode($optionIds, true);
                }

                // Make sure we have arrays
                if (!is_array($variantIds) || !is_array($optionIds)) {
                    Log::warning('Invalid variant or option IDs format', [
                        'variant_ids' => $variantIds,
                        'option_ids' => $optionIds
                    ]);
                    continue;
                }

                // Create a unique combination key to match variants
                $combinationKey = $this->createCombinationKey($variantIds, $optionIds);

                // Try to find an existing variant with the same combination
                $matchingVariant = null;
                foreach ($existingVariants as $variant) {
                    $variantAttributes = MerchantProductVariantAttribute::where('product_variant_id', $variant->id)->get();

                    // Skip if no attributes found
                    if ($variantAttributes->isEmpty()) {
                        continue;
                    }

                    // Extract variant_ids and option_ids
                    $existingVariantIds = $variantAttributes->pluck('variant_id')->toArray();
                    $existingOptionIds = $variantAttributes->pluck('variant_option_id')->toArray();

                    // Create a combination key and check if it matches
                    $existingCombinationKey = $this->createCombinationKey($existingVariantIds, $existingOptionIds);

                    if ($existingCombinationKey === $combinationKey) {
                        $matchingVariant = $variant;
                        break;
                    }
                }

                // If a matching variant was found, update it, otherwise create a new one
                if ($matchingVariant) {
                    // Mark this variant as processed (don't delete it)
                    $processedVariantIds[] = $matchingVariant->id;

                    // Get cost from the first branch
                    $branchData = reset($generatedProduct['branch_values']);
                    $cost = $branchData['cost'] ?? 0;
                    $costTaxExclusive = Helper::excludeTax($cost, $taxPercentage);

                    // Update the variant
                    $matchingVariant->update([
                        'barcode' => $generatedProduct['barcode'] ?? $matchingVariant->barcode,
                        'cost' => $cost,
                        'cost_tax_exclusive' => $costTaxExclusive,
                        'cost_tax_exclusive_rounded' => BC::round($costTaxExclusive, 2),
                    ]);

                    // Update existing attributes if needed
                    // First, remove any existing attributes
                    MerchantProductVariantAttribute::where('product_variant_id', $matchingVariant->id)->delete();

                    // Create new attributes
                    foreach (array_keys($variantIds) as $index) {
                        MerchantProductVariantAttribute::create([
                            'product_variant_id' => $matchingVariant->id,
                            'variant_id' => $variantIds[$index],
                            'variant_option_id' => $optionIds[$index],
                        ]);
                    }

                    $variantId = $matchingVariant->id;
                } else {
                    // Create a new variant
                    // Get cost from the first branch
                    $branchData = reset($generatedProduct['branch_values']);
                    $cost = $branchData['cost'] ?? 0;
                    $costTaxExclusive = Helper::excludeTax($cost, $taxPercentage);

                    $newVariant = MerchantProductVariant::create([
                        'product_id' => $product->id,
                        'barcode' => $generatedProduct['barcode'] ?? generateBarcode(),
                        'cost' => $cost,
                        'cost_tax_exclusive' => $costTaxExclusive,
                        'cost_tax_exclusive_rounded' => BC::round($costTaxExclusive, 2),
                    ]);

                    // Create variant attributes
                    foreach (array_keys($variantIds) as $index) {
                        MerchantProductVariantAttribute::create([
                            'product_variant_id' => $newVariant->id,
                            'variant_id' => $variantIds[$index],
                            'variant_option_id' => $optionIds[$index],
                        ]);
                    }

                    $processedVariantIds[] = $newVariant->id;
                    $variantId = $newVariant->id;
                }

                // Process branch quantities for this variant
                foreach ($generatedProduct['branch_values'] as $branchId => $branchData) {
                    // Default values if missing
                    $quantity = $branchData['quantity'] ?? 0;
                    $cost = $branchData['cost'] ?? 0;
                    $sellPrice = $branchData['sell_price'] ?? 0;
                    $wholesalePrice = $branchData['wholesale_price'] ?? 0;

                    // Calculate tax exclusive values
                    $sellPriceTaxExclusive = Helper::excludeTax($sellPrice, $taxPercentage);
                    $wholesalePriceTaxExclusive = Helper::excludeTax($wholesalePrice, $taxPercentage);
                    $costTaxExclusive = Helper::excludeTax($cost, $taxPercentage);

                    // Check if branch quantity record already exists
                    $existingBranchQuantity = MerchantProductBranchQuantity::where('product_id', $product->id)
                        ->where('product_variant_id', $variantId)
                        ->where('branch_id', $branchId)
                        ->first();

                    if ($existingBranchQuantity) {
                        // Update existing branch quantity
                        $existingBranchQuantity->update([
                            'quantity' => $quantity,
                            'sell_price' => $sellPrice,
                            'sell_price_tax_exclusive' => $sellPriceTaxExclusive,
                            'sell_price_tax_exclusive_rounded' => BC::round($sellPriceTaxExclusive, 2),
                            'wholesale_price' => $wholesalePrice,
                            'wholesale_price_tax_exclusive' => $wholesalePriceTaxExclusive,
                            'wholesale_price_tax_exclusive_rounded' => BC::round($wholesalePriceTaxExclusive, 2),
                        ]);
                    } else {
                        // Create new branch quantity record
                        MerchantProductBranchQuantity::create([
                            'product_id' => $product->id,
                            'product_variant_id' => $variantId,
                            'branch_id' => $branchId,
                            'quantity' => $quantity,
                            'sell_price' => $sellPrice,
                            'sell_price_tax_exclusive' => $sellPriceTaxExclusive,
                            'sell_price_tax_exclusive_rounded' => BC::round($sellPriceTaxExclusive, 2),
                            'wholesale_price' => $wholesalePrice,
                            'wholesale_price_tax_exclusive' => $wholesalePriceTaxExclusive,
                            'wholesale_price_tax_exclusive_rounded' => BC::round($wholesalePriceTaxExclusive, 2),
                        ]);
                    }
                }
            }

            // 5. Delete variants and related records that are no longer present in the generated products
            // First, extract all variant+option combinations from the generated products
            $generatedCombinations = [];
            foreach ($request->generatedProducts as $generatedProduct) {
                if (empty($generatedProduct['product_name'])) {
                    continue;
                }

                // Extract variant and option IDs
                $variantIds = $generatedProduct['variant_id'];
                $optionIds = $generatedProduct['variant_option_id'];

                // Convert from JSON string to array if needed
                if (is_string($variantIds)) {
                    $variantIds = json_decode($variantIds, true);
                }
                if (is_string($optionIds)) {
                    $optionIds = json_decode($optionIds, true);
                }

                // Make sure we have arrays
                if (!is_array($variantIds) || !is_array($optionIds)) {
                    continue;
                }

                // Create a unique combination key and add to the list
                $combinationKey = $this->createCombinationKey($variantIds, $optionIds);
                $generatedCombinations[] = $combinationKey;
            }

            // Now check each existing variant against the generated combinations
            $variantsToDelete = [];
            foreach ($existingVariants as $variant) {
                // Skip if this variant was already processed (matched to a generated product)
                if (in_array($variant->id, $processedVariantIds)) {
                    continue;
                }

                // Get the variant attributes
                $variantAttributes = $variant->variantAttributes;

                // Skip if no attributes found
                if ($variantAttributes->isEmpty()) {
                    // This is an invalid variant, so we'll delete it
                    $variantsToDelete[] = $variant->id;
                    continue;
                }

                // Extract variant_ids and option_ids
                $existingVariantIds = $variantAttributes->pluck('variant_id')->toArray();
                $existingOptionIds = $variantAttributes->pluck('variant_option_id')->toArray();

                // Create a combination key
                $existingCombinationKey = $this->createCombinationKey($existingVariantIds, $existingOptionIds);

                // If this combination doesn't exist in the generated products, mark for deletion
                if (!in_array($existingCombinationKey, $generatedCombinations)) {
                    $variantsToDelete[] = $variant->id;
                }
            }

            foreach ($variantsToDelete as $variantIdToDelete) {
                // Delete branch quantities first
                MerchantProductBranchQuantity::where('product_variant_id', $variantIdToDelete)->delete();

                // Delete variant attributes
                MerchantProductVariantAttribute::where('product_variant_id', $variantIdToDelete)->delete();

                // Delete the variant itself
                MerchantProductVariant::where('id', $variantIdToDelete)->delete();
            }

            Log::debug('Variant product update complete', [
                'product_id' => $product->id,
                'processed_variants' => $processedVariantIds,
                'variant_combinations_in_request' => $generatedCombinations,
                'deleted_variants' => $variantsToDelete
            ]);
        });

        if (request()->has('redirect_url'))
        {
            return redirect(request()->get('redirect_url'))->with('success', __('messages.updated_successfully'));
        }

        return redirect('/products')->with('success', __('messages.updated_successfully'));
    }

    /**
     * Create a unique key for a combination of variant IDs and option IDs
     *
     * @param array $variantIds
     * @param array $optionIds
     * @return string
     */
    private function createCombinationKey($variantIds, $optionIds)
    {
        // Sort both arrays to ensure consistent ordering
        if (!is_array($variantIds)) $variantIds = [$variantIds];
        if (!is_array($optionIds)) $optionIds = [$optionIds];

        // Create pairs of variant_id:option_id
        $pairs = [];
        foreach (array_keys($variantIds) as $index) {
            if (isset($variantIds[$index]) && isset($optionIds[$index])) {
                $pairs[] = $variantIds[$index] . ':' . $optionIds[$index];
            }
        }

        // Sort the pairs for consistency
        sort($pairs);

        // Join with a pipe to create a unique key
        return implode('|', $pairs);
    }
}
