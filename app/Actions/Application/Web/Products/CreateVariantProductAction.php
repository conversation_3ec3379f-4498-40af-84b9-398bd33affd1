<?php

namespace App\Actions\Application\Web\Products;

use App\Enums\Application\ProductTypeEnum;
use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Http\Requests\CreateVariantProductRequest;
use App\Models\MerchantBranch;
use App\Models\MerchantInventoryChange;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantsProduct;
use App\Models\TaxType;
use BCMathExtended\BC;
use Illuminate\Support\Facades\DB;
use App\Models\MerchantCompositeComponent;
use App\Models\MerchantVariant;
use App\Models\MerchantVariantOption;
use App\Models\MerchantProductVariant;
use App\Models\MerchantProductVariantAttribute;

class CreateVariantProductAction
{
    public function __invoke(CreateVariantProductRequest $request)
    {
        $user = getCurrentUser();

        $activeProductCount = MerchantsProduct::query()->active()->count();

        if($activeProductCount > $user->plan->product_numbers && $user->plan->product_numbers != 0 && $request->status !='disabled'){
            return View("theme::marble.needs-subscription", ['title' => 'إضافة منتج' ,'message' => 'باقتك تسمح باضافة 1000 منتج بحد أقصى.']);
        }

        $request['returnable'] = $request->returnable ? $request->returnable : 0;
        $request['purchaseable'] = $request->purchaseable ? $request->purchaseable : 0;
        $request['sellable'] = $request->sellable ? $request->sellable : 0;

        $path = !empty($request->file('thumbnail'))
            ? $request->file('thumbnail')->store('merchant_products')
            : 'merchant_products/blank-image.png';

        if($request['zero_tax']){
            $taxQuery = TaxType::where('code', TaxCodeEnum::NONE->value)->first();
            $taxPercentage = $taxQuery->percentage;
            $tax_id = $taxQuery->id;
        }else{
            $taxPercentage = TaxType::where('id',$request->tax_id)->first()->percentage;
            $tax_id =  $request->tax_id;
        }

        DB::transaction(function () use ($request, $path, $tax_id, $taxPercentage) {
             //Create the main product
            $createProduct = MerchantsProduct::create([
                "name" => $request->get('name'),
                "name_en" => $request->get('name_en'),
                'thumbnail' => $path,
                'sell_price' => 0,
                'wholesale_price' => 0,
                'tax_id' => $tax_id,
                'category_id' => $request->get('category'),
                'type' => ProductTypeEnum::VARIANT->value,
                'status' => $request->get('status'),
                'sell_price_tax_exclusive' => 0,
                'sell_price_tax_exclusive_rounded' => 0,
                'wholesale_price_tax_exclusive' => 0,
                'wholesale_price_tax_exclusive_rounded' => 0,
                'returnable' => $request->get('returnable'),
                'tax_schema' => $request->get('tax_schema'),
                'purchaseable' => $request->get('purchaseable'),
                'sellable' => $request->get('sellable'),
                'barcode' => $request->generatedProducts[0]['barcode'] ?? generateBarcode(),
            ]);

            $productId = $createProduct->id;

            // Process generated products - each one becomes a product variant
            foreach($request->generatedProducts as $generatedProduct) {
                // Create a single product variant for each combination
                $productVariant = MerchantProductVariant::create([
                    'product_id' => $productId,
                    'barcode' => $generatedProduct['barcode'],
                    'thumbnail' => null,
                    'cost' => $generatedProduct['branch_values'][array_key_first($generatedProduct['branch_values'])]['cost'] ?? 0,
                    'cost_tax_exclusive' => Helper::excludeTax(
                        $generatedProduct['branch_values'][array_key_first($generatedProduct['branch_values'])]['cost'] ?? 0,
                        $taxPercentage
                    ),
                    'cost_tax_exclusive_rounded' => BC::round(
                        Helper::excludeTax(
                            $generatedProduct['branch_values'][array_key_first($generatedProduct['branch_values'])]['cost'] ?? 0,
                            $taxPercentage
                        ),
                        2
                    ),
                ]);

                // Create variant attributes (multiple per variant)
                // Handle both array and string JSON representation of arrays
                $variantIds = $generatedProduct['variant_id'];
                $optionIds = $generatedProduct['variant_option_id'];

                // Convert from JSON string to array if needed
                if (is_string($variantIds)) {
                    $variantIds = json_decode($variantIds, true);
                }

                if (is_string($optionIds)) {
                    $optionIds = json_decode($optionIds, true);
                }

                // Make sure we have arrays
                if (!is_array($variantIds) || !is_array($optionIds)) {
                    continue; // Skip if not valid
                }

                // Create the variant attributes
                foreach (array_keys($variantIds) as $index) {
                    if (!isset($variantIds[$index]) || !isset($optionIds[$index])) {
                        continue; // Skip if index is not valid
                    }

                    $variantId = $variantIds[$index];
                    $optionId = $optionIds[$index];

                    MerchantProductVariantAttribute::create([
                        'product_variant_id' => $productVariant->id,
                        'variant_id' => $variantId,
                        'variant_option_id' => $optionId,
                    ]);
                }

                // Process branch quantities for this variant
                foreach ($generatedProduct['branch_values'] as $branchId => $branchData) {
                    MerchantProductBranchQuantity::create([
                        'branch_id' => $branchId,
                        'product_id' => $productId,
                        'product_variant_id' => $productVariant->id,
                        'quantity' => $branchData['quantity'],
                        'sell_price' => $branchData['sell_price'],
                        'sell_price_tax_exclusive' => Helper::excludeTax($branchData['sell_price'], $taxPercentage),
                        'sell_price_tax_exclusive_rounded' => BC::round(Helper::excludeTax($branchData['sell_price'], $taxPercentage), 2),
                        'wholesale_price' => $branchData['wholesale_price'],
                        'wholesale_price_tax_exclusive' => Helper::excludeTax($branchData['wholesale_price'], $taxPercentage),
                        'wholesale_price_tax_exclusive_rounded' => BC::round(Helper::excludeTax($branchData['wholesale_price'], $taxPercentage), 2),
                    ]);
                }
            }
        });

        if (request()->has('redirect_url'))
        {
            return redirect(request()->get('redirect_url'))->with('success', __('messages.created_successfully'));
        }

        return redirect('/products')->with('success', __('messages.created_successfully'));
    }
}
