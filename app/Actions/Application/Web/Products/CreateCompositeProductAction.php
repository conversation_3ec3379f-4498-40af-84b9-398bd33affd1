<?php

namespace App\Actions\Application\Web\Products;

use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Http\Requests\Application\Web\Product\CreateCompositeProductRequest;
use App\Models\MerchantBranch;
use App\Models\MerchantCompositeComponent;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantProductTag;
use App\Models\MerchantsProduct;
use App\Models\MerchantTag;
use App\Models\TaxType;
use BCMathExtended\BC;
use Illuminate\Support\Facades\DB;

class CreateCompositeProductAction
{
    public function __invoke(CreateCompositeProductRequest $request)
    {
        $user = getCurrentUser();

        $activeProductCount = MerchantsProduct::query()->active()->count();
        if($activeProductCount > $user->plan->product_numbers && $user->plan->product_numbers != 0 && $request->status !='disabled'){
            return View("theme::marble.needs-subscription", ['title' => 'إضافة منتج' ,'message' => 'باقتك تسمح باضافة 1000 منتج بحد أقصى.']);
        }
        $request['returnable'] = $request->returnable?$request->returnable:0;
        $request['purchaseable'] = $request->purchaseable?$request->purchaseable:0;
        $request['sellable'] = $request->sellable?$request->sellable:0;

            $path = !empty($request->file('thumbnail'))? $path = $request->file('thumbnail')->store('merchant_products'):'merchant_products/blank-image.png';

            if($request['zero_tax']){
                $taxQuery = TaxType::where('code', TaxCodeEnum::NONE->value)->first();
                $taxPercentage = $taxQuery->percentage;
                $tax_id = $taxQuery->id;
            }else{
                $taxPercentage = TaxType::where("id", $request->tax_id)->first()->percentage;
                $tax_id =  $request->tax_id;
            }


            $barcode = empty($request->barcode) ? generateBarcode() : $request->barcode;
            $merchantBranches = MerchantBranch::query()->pluck('id');

            DB::transaction(function () use ($request,$path,$barcode,$merchantBranches,$tax_id,$taxPercentage) {
                $createProduct = MerchantsProduct::create([
                    "name" => $request->get('name'),
                    "name_en" => $request->get('name_en'),
                    'thumbnail' => $path,
                    'barcode' => $barcode,
                    'sell_price' => 0,
                    'wholesale_price' => 0,
                    'tax_id' => $tax_id,
                    'category_id' => $request->get('category'),
                    'type' => 'composite',
                    'status' => $request->get('status'),
                    'sell_price_tax_exclusive' => 0,
                    'sell_price_tax_exclusive_rounded' => 0,
                    'wholesale_price_tax_exclusive' => 0,
                    'wholesale_price_tax_exclusive_rounded' => 0,
                    'returnable' => $request->get('returnable'),
                    'tax_schema' => $request->get('tax_schema'),
                    'purchaseable' => $request->get('purchaseable'),
                    'sellable' => $request->get('sellable'),
                ]);
                $productId = $createProduct->id;
                $tags = $request->input('tags');

                if($tags){
                    $tags = json_decode($tags);
                    foreach ($tags as $tag) {
                        $tag = trim($tag->value);
                        $tag = MerchantTag::firstOrCreate(['name' => $tag]);
                        MerchantProductTag::create(['product_id' => $productId, 'tag_id' => $tag->id]);
                    }
                }

                // Handle composite product components
                    $products = $request->input('products');
                    foreach ($products as $id => $quantity) {
                        MerchantCompositeComponent::create([
                            'composite_product_id' => $productId,
                            'component_product_id' => $id,
                            'quantity' => $quantity,
                        ]);
                    }
                    $priceInputs = $request->input('price_per_branch');
                    $wholesaleInputs = $request->input('wholesale_price_per_branch');

                    // For composite products, create branch entries with zero quantity
                    foreach ($merchantBranches as $branch) {
                        $sellPricePerBranch = isset($priceInputs[$branch]) ? $priceInputs[$branch] : 0;
                        $wholesalePricePerBranch = isset($wholesaleInputs[$branch]) ? $wholesaleInputs[$branch] : 0;

                        $sellPricePerBranchTaxExclusive = Helper::excludeTax($sellPricePerBranch, $taxPercentage);
                        $wholesalePricePerBranchTaxExclusive = Helper::excludeTax($wholesalePricePerBranch, $taxPercentage);


                        MerchantProductBranchQuantity::create([
                            'product_id' => $productId,
                            'branch_id' => $branch,
                            'quantity' => 0,
                            'sell_price' => $sellPricePerBranch,
                            'wholesale_price' => $wholesalePricePerBranch,
                            'sell_price_tax_exclusive' => $sellPricePerBranchTaxExclusive,
                            'sell_price_tax_exclusive_rounded' => BC::round($sellPricePerBranchTaxExclusive,2),
                            'wholesale_price_tax_exclusive' => $wholesalePricePerBranchTaxExclusive,
                            'wholesale_price_tax_exclusive_rounded' => BC::round($wholesalePricePerBranchTaxExclusive,2),
                        ]);
                    }
            });

        if (request()->has('redirect_url'))
        {
            return redirect(request()->get('redirect_url'))->with('success', __('messages.created_successfully'));
        }

        return redirect('/products')->with('success', __('messages.created_successfully'));
    }
}
