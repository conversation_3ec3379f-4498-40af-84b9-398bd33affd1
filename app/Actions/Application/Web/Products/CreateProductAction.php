<?php

namespace App\Actions\Application\Web\Products;

use App\Enums\Application\ProductTypeEnum;
use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Http\Requests\Application\Web\Product\CreateProductRequest;
use App\Models\MerchantBranch;
use App\Models\MerchantInventoryChange;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantProductTag;
use App\Models\MerchantsProduct;
use App\Models\MerchantTag;
use App\Models\TaxType;
use BCMathExtended\BC;
use Illuminate\Support\Facades\DB;

class CreateProductAction
{
    public function __invoke(CreateProductRequest $request)
    {
        $user = getCurrentUser();

        $activeProductCount = MerchantsProduct::active()->count();
        if($activeProductCount > $user->plan->product_numbers && $user->plan->product_numbers != 0 && $request->status !='disabled'){
            return View("theme::marble.needs-subscription", ['title' => 'إضافة منتج' ,'message' => 'باقتك تسمح باضافة 1000 منتج بحد أقصى.']);
        }

        $request['cost'] = !$request->cost ? 0: $request->cost;
        $request['quantity']= !$request->quantity? 0: $request->quantity;
        $request['wholesale_price'] = !$request->wholesale_price? 0: $request->wholesale_price;
        $request['returnable'] = $request->returnable?$request->returnable:0;
        $request['purchaseable'] = $request->purchaseable?$request->purchaseable:0;
        $request['sellable'] = $request->sellable?$request->sellable:0;

        $path = !empty($request->file('thumbnail')) ? $request->file('thumbnail')->store('merchant_products') : 'merchant_products/blank-image.png';

        if($request['zero_tax']){
            $taxQuery = TaxType::where('code', TaxCodeEnum::NONE->value)->first();
            $taxPercentage = $taxQuery->percentage;
            $tax_id = $taxQuery->id;
        }else{
            $taxPercentage = TaxType::where("id", $request->tax_id )->first()->percentage;
            $tax_id =  $request->tax_id;
        }

        $cost = $request->cost;

        if ($request->get('type') == ProductTypeEnum::WEIGHTED->value)
        {
            $cost = Helper::setCostOrPriceInBaseUnit($request->cost);
        }

        $costTaxExclusive = Helper::excludeTax($cost,$taxPercentage);
        $barcode = empty($request->barcode) ? generateBarcode() : $request->barcode;

        $merchantBranches = MerchantBranch::query()->pluck('id');

        DB::transaction(function () use ($request,$path,$barcode,$costTaxExclusive,$merchantBranches,$tax_id,$taxPercentage, $cost) {
            $createProduct = MerchantsProduct::create([
                "name" => $request->get('name'),
                "name_en" => $request->get('name_en'),
                'thumbnail' => $path,
                'barcode' => $barcode,
                'sell_price' => 0,
                'wholesale_price' => 0,
                'cost' => $cost,
                'tax_id' => $tax_id,
                'category_id' => $request->get('category'),
                'type' => $request->get('type'),
                'status' => $request->get('status'),
                'sell_price_tax_exclusive' => 0,
                'sell_price_tax_exclusive_rounded' => 0,
                'wholesale_price_tax_exclusive' => 0,
                'wholesale_price_tax_exclusive_rounded' =>0,
                'cost_tax_exclusive' => $costTaxExclusive,
                'cost_tax_exclusive_rounded' => BC::round($costTaxExclusive,2),
                'returnable' => $request->get('returnable'),
                'tax_schema' => $request->get('tax_schema'),
                'purchaseable' => $request->get('purchaseable'),
                'sellable' => $request->get('sellable'),
            ]);
            $productId = $createProduct->id;
            $tags = $request->input('tags');
            if($tags){
                $tags = json_decode($tags);
                foreach ($tags as $tag) {
                    $tag = trim($tag->value);
                    $tag = MerchantTag::firstOrCreate(['name' => $tag]);
                    MerchantProductTag::create(['product_id' => $productId, 'tag_id' => $tag->id]);
                }
            }


            $quantityInputs = $request->input('quantity');
            $priceInputs = $request->input('price_per_branch');
            $wholesaleInputs = $request->input('wholesale_price_per_branch');

            // Skip quantity operations for service products
            if ($request->get('type') !== ProductTypeEnum::SERVICE->value) {
                $totalQuantity = array_sum($quantityInputs);

                if ($request->get('type') == ProductTypeEnum::WEIGHTED->value) {
                    $totalQuantity = Helper::setQuantityInBaseUnit($totalQuantity);
                }

                foreach ($merchantBranches as $branch) {
                    $quantity = !isset($quantityInputs[$branch]) ? 0 : $quantityInputs[$branch];

                    $sellPricePerBranch = isset($priceInputs[$branch])? $priceInputs[$branch]: 0;
                    $wholeSalePricePerBranch = isset($wholesaleInputs[$branch])? $wholesaleInputs[$branch]: 0;

                    if ($request->get('type') == ProductTypeEnum::WEIGHTED->value)
                    {
                        $quantity = Helper::setQuantityInBaseUnit($quantity);
                        $sellPricePerBranch = Helper::setCostOrPriceInBaseUnit($sellPricePerBranch);
                        $wholeSalePricePerBranch = Helper::setCostOrPriceInBaseUnit($wholeSalePricePerBranch);
                    }

                    $sellPricePerBranchTaxExclusive = Helper::excludeTax($sellPricePerBranch, $taxPercentage);
                    $wholesalePricePerBranchTaxExclusive = Helper::excludeTax($wholeSalePricePerBranch, $taxPercentage);

                    MerchantProductBranchQuantity::create([
                        'product_id' => $productId,
                        'branch_id' => $branch,
                        'quantity' => $quantity,
                        'sell_price' => $sellPricePerBranch,
                        'wholesale_price' => $wholeSalePricePerBranch,
                        'sell_price_tax_exclusive' => $sellPricePerBranchTaxExclusive,
                        'sell_price_tax_exclusive_rounded' => BC::round($sellPricePerBranchTaxExclusive,2),
                        'wholesale_price_tax_exclusive' => $wholesalePricePerBranchTaxExclusive,
                        'wholesale_price_tax_exclusive_rounded' => BC::round($wholesalePricePerBranchTaxExclusive,2),

                    ]);

                    if ($totalQuantity > 0 && $cost > 0) {
                        MerchantInventoryChange::create([
                            'product_id' => $productId,
                            'branch_id' => $branch,
                            'quantity' => $totalQuantity,
                            'quantity_before' => 0,
                            'cost' => $cost,
                            'cost_tax_exclusive' => $costTaxExclusive,
                            'cost_tax_exclusive_rounded' => BC::round($costTaxExclusive, 2),
                            'manually_impacts_cost' => 1,
                        ]);
                    }
                }
            } else {
                // For service products, create branch entries without quantity
                foreach ($merchantBranches as $branch) {
                    $sellPricePerBranch = isset($priceInputs[$branch]) ? $priceInputs[$branch] : 0;
                    $wholeSalePricePerBranch = isset($wholesaleInputs[$branch]) ? $wholesaleInputs[$branch] : 0;

                    // Calculate tax exclusive prices for service products
                    $sellPriceTaxExclusive = Helper::excludeTax($sellPricePerBranch, $taxPercentage);
                    $wholeSalePriceTaxExclusive = Helper::excludeTax($wholeSalePricePerBranch, $taxPercentage);

                    MerchantProductBranchQuantity::create([
                        'product_id' => $productId,
                        'branch_id' => $branch,
                        'quantity' => 0,
                        'sell_price' => $sellPricePerBranch,
                        'wholesale_price' => $wholeSalePricePerBranch,
                        'sell_price_tax_exclusive' => $sellPriceTaxExclusive,
                        'sell_price_tax_exclusive_rounded' => BC::round($sellPriceTaxExclusive, 2),
                        'wholesale_price_tax_exclusive' => $wholeSalePriceTaxExclusive,
                        'wholesale_price_tax_exclusive_rounded' => BC::round($wholeSalePriceTaxExclusive, 2),
                    ]);
                }
            }
        });

        if (request()->has('redirect_url'))
        {
            return redirect(request()->get('redirect_url'))->with('success', __('messages.created_successfully'));
        }

        return redirect('/products')->with('success', __('messages.created_successfully'));
    }
}
