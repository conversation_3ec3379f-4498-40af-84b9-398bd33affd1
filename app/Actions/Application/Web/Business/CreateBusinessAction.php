<?php

namespace App\Actions\Application\Web\Business;

use App\Enums\Application\BusinessStatus;
use App\Enums\Application\TaxCodeEnum;
use App\Enums\UnitNames;
use App\Enums\WeightedProductStatus;
use App\Models\Business;
use App\Models\MerchantUser;
use App\Models\Unit;
use App\Models\UserBusinessGroup;
use App\Services\BusinessContextService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CreateBusinessAction
{
    public function __invoke(string $name, string $description): RedirectResponse
    {
        DB::beginTransaction();

        try {

            $isFirst = Business::withoutGlobalScopes()
                    ->where('merchant_id', getCurrentUser()->merchant_id)
                    ->count() === 0;

            // Create new business
            $business = Business::create([
                'merchant_id' => getCurrentUser()->merchant_id,
                'name' => $name,
                'description' => $description,
                'is_default' => $isFirst,
                'status' => BusinessStatus::ACTIVE->value,
            ]);

            // If this is the first business, set it as active
            if ($isFirst) {
                session(['active_business_id' => $business->id]);
                BusinessContextService::setBusinessId($business->id);
            }

            $merchantUser = MerchantUser::where('id', getCurrentUser()->merchant_id)->first();

            // Create group for this business
            $group = $merchantUser->group()->create([
                'name' => 'مدير',
                'merchant_id' => $merchantUser->id,
                'business_id' => $business->id,
            ]);

            // Create business-user-group relationship
            UserBusinessGroup::create([
                'merchant_id' => $merchantUser->id,
                'user_id' => $merchantUser->id,
                'business_id' => $business->id,
                'group_id' => $group->id,
                'is_default' => $isFirst,
            ]);

            $perms = [
                "dashboard_sales_show" => 1,
                "dashboard_net_profit_show" => 1,
                "dashboard_transactions_show" => 1,
                "dashbaord_payment_methods_show" => 1,
                "dashboard_expenses_show" => 1,
                "dashbaord_sales_last_45_show" => 1,
                "cashier_open" => 1,
                "cashier_close" => 1,
                "cashier_pause_sale" => 1,
                "cashier_wholesale" => 1,
                "cashier_add_customer" => 1,
                "cashier_postpay" => 1,
                "cashier_multipay" => 1,
                "cashier_discount" => 1,
                "cashier_discount_max_number" => "1",
                "cashier_discount_max_percentage" => "1",
                "reports_sales_summary_show" => 1,
                "reports_sales_summary_export" => 1,
                "reports_sales_product_show" => 1,
                "reports_sales_product_export" => 1,
                "reports_sales_payments_show" => 1,
                "reports_sales_payments_export" => 1,
                "reports_tax_show" => 1,
                "reports_tax_export" => 1,
                "reports_cashier_recons_show" => 1,
                "products_show" => 1,
                "products_import" => 1,
                "products_export" => 1,
                "products_add" => 1,
                "products_edit" => 1,
                "products_remove" => 1,
                "products_category_show" => 1,
                "products_category_add" => 1,
                "products_category_edit" => 1,
                "products_category_remove" => 1,
                "products_status" => 1,
                "products_wholesale" => 1,
                "products_cost_show" => 1,
                "products_cost_edit" => 1,
                "invoices_show" => 1,
                "invoices_export" => 1,
                "invoices_receive" => 1,
                "invoices_return" => 1,
                "expenses_add" => 1,
                "expenses_edit" => 1,
                "expenses_delete" => 1,
                "expenses_show" => 1,
                "customers_add" => 1,
                "customers_numbers" => 1,
                "customers_show" => 1,
                "suppliers_show" => 1,
                "suppliers_add" => 1,
                "suppliers_edit" => 1,
                "suppliers_numbers" => 1,
                "settings_main_show" => 1,
                "settings_show_payment_methods" => 1,
                "settings_show_taxes" => 1,
                "settings_show_currencies" => 1,
                "settings_main_edit" => 1,
                "settings_add_payment_methods" => 1,
                "settings_edit_payment_methods" => 1,
                "settings_add_taxes" => 1,
                "settings_edit_taxes" => 1,
                "settings_add_currencies" => 1,
                "settings_edit_currencies" => 1,
                "employees_add" => 1,
                "employees_show" => 1,
                "employees_edit" => 1,
                "employee_permissions_add" => 1,
                "employee_permissions_edit" => 1,
                "employee_permissions_show" => 1,
                "purchase_order_show" => 1,
                "purchase_order_add" => 1,
                "transfer_orders_show" => 1,
                "transfer_orders_add" => 1,
                "stock_count_orders_show" => 1,
                "stock_count_orders_add" => 1,
                "remove_orders_show" => 1,
                "remove_orders_add" => 1,
                "branches_registers_show_add_edit" => 1,
                "subscription_show" => 1,
                "subscription_edit" => 1,
                "reports_expenses_show" => 1,
                "reports_expenses_export" => 1,
                "add_weighted_purchase_order" => 1,
                "add_weighted_remove_order" => 1,
                "add_weighted_stock_count" => 1,
                "add_weighted_transfer_order" => 1,
                "change_weighted_product_setting" => 1,
                "edit_weighted_products" => 1,
                "add_weighted_products" => 1,
                'group_id' => $group->id,
                'business_id' => $business->id,
            ];

            $merchantUser->permissions()->create($perms);

            $merchantUser->setting()->create([
                'name' => $name,
                'CR' => 0,
                'business_id' => $business->id,
            ]);

            $merchantUser->taxTypes()->createMany([
                [
                    'merchant_id' => $merchantUser->id,
                    'name' => 'ضريبة القيمة المضافة',
                    'percentage' => '15',
                    'code' => TaxCodeEnum::TAX->value,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'business_id' => $business->id,
                ],
                [
                    'merchant_id' => $merchantUser->id,
                    'name' => 'من غير ضريبة',
                    'percentage' => '0',
                    'code' => TaxCodeEnum::NONE->value,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'business_id' => $business->id,
                ],
                [
                    'merchant_id' => $merchantUser->id,
                    'name' => 'الضريبة الصفرية',
                    'percentage' => '0',
                    'code' => TaxCodeEnum::ZERO->value,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'business_id' => $business->id,
                ]
            ]);

            $paymentTypes = $merchantUser->paymentMethods()->createMany([
                [
                    'name' => 'شبكة',
                    'is_cash' => 0,
                    'is_postpay' => 0,
                    'is_rounding_activiated' => 0,
                    'is_default' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'business_id' => $business->id,
                ],
                [
                    'name' => 'كاش',
                    'is_cash' => 1,
                    'is_postpay' => 0,
                    'is_rounding_activiated' => 0,
                    'is_default' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'business_id' => $business->id,
                ],
                [
                    'name' => 'دفع بالآجل',
                    'is_cash' => 0,
                    'is_postpay' => 1,
                    'is_rounding_activiated' => 0,
                    'is_default' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                    'business_id' => $business->id,
                ],
            ]);

            $merchantUser->address()->create([
                'street' => 'riyadh',
                'building_number' => 0,
                'postal_code' => 12345,
                'state_id' => 1,
                'city_id' => 1,
                'district' => 'Malqa',
                'merchant_id' => $merchantUser->id,
                'business_id' => $business->id,
            ]);

            $currencies = $merchantUser->currencies()->createMany([
                [
                    'merchant_id' => $merchantUser->id,
                    'name' => 'ريال سعودي',
                    'symbol' => 'SAR',
                    'business_id' => $business->id,
                ],
            ]);

            $currencyId = $currencies->first()->id;

            $createBranch = $merchantUser->merchantBranches()->create([
                'branch_name' => 'الفرع الرئيسي',
                'branch_address' => 'الفرع الرئيسي',
                'currency_id' => $currencyId,
                'business_id' => $business->id,
            ]);

            $createRegistry = $merchantUser->registries()->create([
                "name" => 'صندوق الفرع الرئيسي',
                "branch_id" => $createBranch->id,
                'uuid' => Str::uuid(),
                'business_id' => $business->id,
            ]);

            $merchantUser->registerSetting()->create([
                'register_id' => $createRegistry->id,
                'screen' => 'sale_default',
                'open_close_register' => 0,
                'business_id' => $business->id,
            ]);

            $paymentTypes->where('name', '!==', 'دفع بالآجل')->each(function ($paymentType) use ($createRegistry, $merchantUser, $business) {
                $merchantUser->registerPaymentMethods()->create([
                    'register_id' => $createRegistry->id,
                    'payment_id' => $paymentType->id,
                    'business_id' => $business->id,
                ]);
            });

            $merchantUser->details()->create([
                'branch_numbers' => 1,
                'register_numbers' => 1,
                'member_numbers' => 1,
                'business_id' => $business->id,
            ]);

            $merchantUser->weightedProductSetting()->create([
                'merchant_id' => $merchantUser->id,
                'unit_id' => Unit::query()->where('name', 'like', UnitNames::GRAM)->first()->id,
                'status' => WeightedProductStatus::ACTIVE->value,
                'business_id' => $business->id,
            ]);

            DB::commit();

            return redirect()->route('business.index')
                ->with('success', 'New business created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to create new business: ' . $e->getMessage());
        }
    }
}
