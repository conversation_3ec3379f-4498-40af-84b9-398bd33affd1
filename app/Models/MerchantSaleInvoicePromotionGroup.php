<?php

namespace App\Models;

use App\Traits\BusinessFiltering;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Model for tracking promotion groups in invoices
 * 
 * A promotion group represents a complete, indivisible unit of a promotion
 * that must be returned together (e.g., buy 2 get 1 free = one group)
 */
class MerchantSaleInvoicePromotionGroup extends Model
{
    use BusinessFiltering, SoftDeletes;

    protected $fillable = [
        'merchant_id',
        'business_id',
        'invoice_id',
        'group_id',
        'original_promotion_id',
        'promotion_snapshot',
        'buy_x_quantity_required',
        'get_y_quantity_given',
        'group_number',
        'promotion_type'
    ];

    protected $casts = [
        'promotion_snapshot' => 'array',
        'buy_x_quantity_required' => 'integer',
        'get_y_quantity_given' => 'integer',
        'group_number' => 'integer'
    ];

    /**
     * Get the invoice this promotion group belongs to
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(MerchantInvoice::class, 'invoice_id');
    }

    /**
     * Get the original promotion (may be null if promotion was deleted)
     */
    public function originalPromotion(): BelongsTo
    {
        return $this->belongsTo(MerchantPromotion::class, 'original_promotion_id');
    }

    /**
     * Get all products that belong to this promotion group
     */
    public function products(): HasMany
    {
        return $this->hasMany(MerchantSaleInvoiceProduct::class, 'promotion_group_id', 'group_id');
    }

    /**
     * Get only the buy_x products in this group
     */
    public function buyXProducts(): HasMany
    {
        return $this->products()->where('promotion_product_type', 'buy_x');
    }

    /**
     * Get only the get_y products in this group
     */
    public function getYProducts(): HasMany
    {
        return $this->products()->where('promotion_product_type', 'get_y');
    }

    /**
     * Check if this promotion group can be partially returned
     * 
     * @return bool Always false - promotion groups must be returned completely
     */
    public function canBePartiallyReturned(): bool
    {
        return false;
    }

    /**
     * Get the promotion snapshot data
     * 
     * @return array The original promotion data at time of sale
     */
    public function getPromotionSnapshot(): array
    {
        return $this->promotion_snapshot ?? [];
    }

    /**
     * Get a human-readable description of this promotion group
     *
     * @return string Description using the actual promotion name
     */
    public function getDescription(): string
    {
        $snapshot = $this->getPromotionSnapshot();

        // Use the actual promotion name if available
        if (isset($snapshot['promotion_name']) && !empty($snapshot['promotion_name'])) {
            return $snapshot['promotion_name'];
        }

        // Fallback to generic description for backward compatibility
        if ($this->promotion_type === 'buy_x_get_y') {
            $getYType = $snapshot['get_y_type'] ?? 'free';

            if ($getYType === 'free') {
                return "Buy {$this->buy_x_quantity_required}, Get {$this->get_y_quantity_given} Free";
            } else {
                $discount = $snapshot['get_y_discount_percentage'] ?? 0;
                return "Buy {$this->buy_x_quantity_required}, Get {$this->get_y_quantity_given} at {$discount}% off";
            }
        }

        return $snapshot['name'] ?? 'Promotion Group';
    }

    /**
     * Calculate the total value of this promotion group
     * 
     * @return array ['total_value' => float, 'discount_value' => float]
     */
    public function calculateGroupValue(): array
    {
        $buyXProducts = $this->buyXProducts;
        $getYProducts = $this->getYProducts;
        
        $totalValue = 0;
        $discountValue = 0;
        
        // Calculate buy_x products value (full price)
        foreach ($buyXProducts as $product) {
            $totalValue += $product->price_tax_inclusive * $product->cart_quantity;
        }
        
        // Calculate get_y products value and discount
        foreach ($getYProducts as $product) {
            $originalValue = $product->price_tax_inclusive * $product->cart_quantity;
            $discountAmount = $product->discount ?? 0;
            
            $totalValue += $originalValue;
            $discountValue += $discountAmount;
        }
        
        return [
            'total_value' => $totalValue,
            'discount_value' => $discountValue
        ];
    }
}
