<?php

namespace App\Models;

use App\Traits\BusinessFiltering;
use Database\Factories\PaymentMethodFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class MerchantPaymentMethod extends Model
{
    use HasFactory, SoftDeletes, BusinessFiltering;

    protected $fillable = ['merchant_id', 'business_id', 'name', 'is_cash', 'is_rounding_activiated', 'is_postpay', 'is_default'];

    protected $casts = [
        'is_cash' => 'boolean',
        'is_rounding_activiated' => 'boolean',
        'is_postpay' => 'boolean',
        'is_default' => 'boolean',
    ];

    final public function merchantUser(): BelongsTo
    {
        return $this->belongsTo(MerchantUser::class, 'merchant_id', 'merchant_id')->where('main_account', true);
    }

    protected static function newFactory(): PaymentMethodFactory
    {
        return PaymentMethodFactory::new();
    }
}
