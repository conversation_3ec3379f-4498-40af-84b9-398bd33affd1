<?php

namespace App\Models;

use App\Enums\Application\Zatca\ZatcaInvoiceName;
use App\Enums\Application\Zatca\ZatcaInvoiceStatus;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Database\Factories\MerchantUserFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use App\Services\BusinessContextService;


/**
 * @property bool $isZatcaEnabled
 * @property bool $isMainRegistrieZatcaEnabled
 * @property bool $main_account
 * @property int $merchant_id
 */
class MerchantUser extends Authenticatable
{
    use HasApiTokens, HasFactory, SoftDeletes, Notifiable;

    protected $fillable = ['name','email','password','merchant_id','business_id','group_id','phone_number','main_account','plan_id'];

    /**
     * @return bool
     */
    // public function canImpersonate()
    // {
    //     // If user is admin they can impersonate
    //     $user = new User();
    //     return $user->hasRole('admin');

    // }

    // /**
    //  * @return bool
    //  */
    // public function canBeImpersonated()
    // {
    //     // Any user that is not an admin can be impersonated
    //     $user = new User();
    //     return !$user->hasRole('admin');
    // }


    public function plan(): BelongsTo
    {
        return $this->belongsTo(OasisPlanFeature::class, 'plan_id', 'plan_id');
    }

    final public function registries(): HasMany
    {
        return $this->hasMany(MerchantRegistery::class, 'merchant_id', 'merchant_id');
    }

    final public function registerSetting(): HasOne
    {
        return $this->hasOne(MerchantRegisterSetting::class, 'merchant_id','merchant_id');
    }

    final public function weightedProductSetting(): HasOne
    {
        return $this->hasOne(WeightedProductSetting::class, 'merchant_id','merchant_id');
    }

    final public function installedApps(): HasMany
    {
        return $this->hasMany(MerchantInstalledApp::class, 'merchant_id','merchant_id');
    }

    final public function registerPaymentMethods():HasMany
    {
        return $this->hasMany(MerchantPaymentMethodRegister::class, 'merchant_id','merchant_id');
    }

    final public function registryRecords(): HasMany
    {
        return $this->hasMany(MerchantRegisterRecord::class, 'merchant_id', 'merchant_id');
    }

    final public function saleInvoices(): HasMany
    {
        return $this->hasMany(MerchantInvoice::class, 'merchant_id', 'merchant_id');
    }

    final public function paymentMethods(): HasMany
    {
        return $this->hasMany(MerchantPaymentMethod::class, 'merchant_id', 'merchant_id');
    }

    public function mainAccount()
    {
        if(!$this->main_account)
        {
            return $this->hasOne(MerchantUser::class, 'merchant_id','merchant_id')->where('main_account', 1);

        }
    }

    final public function details():HasOne
    {
        return $this->hasOne(MerchantUserMainDetail::class, 'merchant_id','merchant_id');
    }

//    final public function group(): BelongsTo
//    {
//        return $this->belongsTo(MerchantGroup::class,'group_id', 'id');
//    }

    final public function permissions(): HasMany
    {
        return $this->hasMany(MerchantGroupPermission::class, 'merchant_id','merchant_id');
    }

    final public function setting(): HasOne
    {
        return $this->hasOne(MerchantsSetting::class, 'merchant_id','merchant_id');
    }

    final public function addresses(): HasMany
    {
        return $this->hasMany(Address::class, 'merchant_id','merchant_id');
    }

    final public function address(): MorphOne
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    final public function branches(): HasMany
    {
        return $this->hasMany(MerchantUserBranch::class, 'user_id','id');
    }

    final public function merchantBranches(): HasMany
    {
        return $this->hasMany(MerchantBranch::class, 'merchant_id','merchant_id');
    }

    final public function categories(): HasMany
    {
        return $this->hasMany(MerchantCategory::class, 'merchant_id','merchant_id');
    }

    final public function taxTypes(): HasMany
    {
        return $this->hasMany(TaxType::class, 'merchant_id','merchant_id');
    }

    final public function currencies(): HasMany
    {
        return $this->hasMany(MerchantCurrency::class, 'merchant_id','merchant_id');
    }

    final public function customers(): HasMany
    {
        return $this->hasMany(MerchantCustomer::class, 'merchant_id','merchant_id');
    }

    final public function suppliers(): HasMany
    {
        return $this->hasMany(MerchantSupplier::class, 'merchant_id','merchant_id');
    }

    final public function zatcaIntegrationStep(): MorphOne
    {
        return $this->morphOne(ZatcaIntegrationStep::class, 'actorable');
    }

    final public function zatcaIntegrationSteps(): HasMany
    {
        return $this->hasMany(ZatcaIntegrationStep::class, 'merchant_id', 'merchant_id');
    }

    final public function scopeOwner(Builder $query): Builder
    {
        return $query->where('main_account', 1);
    }

    protected static function newFactory(): MerchantUserFactory
    {
        return MerchantUserFactory::new();
    }

    protected function isZatcaEnabled(): Attribute
    {
        return Attribute::make(get: fn () =>
            $this->isMainRegistrieZatcaEnabled &&
            $this->isRegistriesZatcaEnabled &&
            $this->isAllRegistriesZatcaIntegrated()

        );
    }

    protected function isMainRegistrieZatcaEnabled(): Attribute
    {
        return Attribute::make(get: fn () =>
            !is_null($this->setting->zatca_request_id) &&
            !is_null($this->setting->zatca_binary_security_token) &&
            !is_null($this->setting->zatca_secret)
        );
    }

    protected function isRegistriesZatcaEnabled(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->registries()
                    ->where(function (Builder $query) {
                        $query->whereNull('zatca_request_id')
                            ->orWhereNull('zatca_binary_security_token')
                            ->orWhereNull('zatca_secret');
                    })
                    ->count() === 0
        );
    }

    private function isAllRegistriesZatcaIntegrated(): bool
    {
        return $this->zatcaIntegrationSteps()
                ->where('pcsid', false)
                ->count() === 0;
    }

    private function hasMoreThanOneZatcaIntegrationSteps(): bool
    {
        $date = $this->zatcaIntegrationStep()
            ->where('pcsid', true)
            ->latest()
            ->first();

        return $this->zatcaIntegrationStep()->withTrashed()->count() > 1 &&
            $date?->updated_at->between(now()->subDay(), now());
    }

    private function hasNotInvoices(): bool
    {
        $latestIntegrationWithZatca = $this->zatcaIntegrationStep()
            ->where('pcsid', true)
            ->select('id', 'updated_at')
            ->latest()
            ->first();

        return  $this->saleInvoices()
            ->where('zatca_invoice_name', ZatcaInvoiceName::SIMPLIFIED->value)
            ->whereNull('zatca_offline_sequence')
            ->where(function (Builder $query){
                $query->where('zatca_status', ZatcaInvoiceStatus::FAILED->value)
                ->orWhereNull('zatca_status');
            })
            ->whereBetween('created_at', [
                now()->setDateTimeFrom($latestIntegrationWithZatca->updated_at)->subDay(),
                now()->setDateTimeFrom($latestIntegrationWithZatca->updated_at),
            ])
            ->count() > 0;

    }

    protected function hasNonClearedInvoice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->hasMoreThanOneZatcaIntegrationSteps() && $this->hasNotInvoices()
        );
    }

    // Many-to-many relationship with businesses through the pivot
    public function businesses(): BelongsToMany
    {
        return $this->belongsToMany(Business::class, UserBusinessGroup::class, 'user_id', 'business_id')
                    ->withPivot('group_id', 'is_default')
                    ->withTimestamps();
    }

    // Relationship to get all business-role assignments
    public function businessGroups(): HasMany
    {
        return $this->hasMany(UserBusinessGroup::class, 'user_id', 'id');
    }

    /**
     * Get the user's current group with permissions
     */
    public function group(): HasOneThrough
    {
        return $this->hasOneThrough(
            MerchantGroup::class,
            UserBusinessGroup::class,
            'user_id',
            'id',
            'id',
            'group_id'
        )
        ->where('user_business_groups.business_id', BusinessContextService::getBusinessId())
        ->where('merchant_groups.merchant_id', BusinessContextService::getMerchantId())
        ->with('permissions');
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token): void
    {
        $this->notify(new ResetPasswordNotification($token));
    }
}
