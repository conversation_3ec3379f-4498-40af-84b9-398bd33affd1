<?php

namespace App\Models;

use App\Traits\BusinessFiltering;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;


/**
 * @property int $cart_quantity
 * @property float $price_tax_inclusive
 * @property float $price_tax_exclusive
 * @property string $tax_code
 * @property string $name
 * @property string $name_ar
 */
class MerchantSaleInvoiceProduct extends Model
{
    use SoftDeletes, BusinessFiltering;

    protected $guarded = ['id'];

    public function product(): BelongsTo
    {
        return $this->belongsTo(MerchantsProduct::class, 'product_id', 'id');
    }

    public function tax(): BelongsTo
    {
        return $this->belongsTo(TaxType::class, 'tax_type', 'id');
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(MerchantInvoice::class, 'invoice_id', 'id');
    }

    public function fulfillmentProducts(): HasMany
    {
        return $this->hasMany(MerchantFulfillmentProduct::class, 'invoice_product_id');
    }

    public function variant(): BelongsTo
    {
        return $this->belongsTo(MerchantProductVariant::class, 'product_variant_id');
    }

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(MerchantPromotion::class, 'promotion_id');
    }

    /**
     * Get the promotion group this product belongs to
     */
    public function promotionGroup(): BelongsTo
    {
        return $this->belongsTo(MerchantSaleInvoicePromotionGroup::class, 'promotion_group_id', 'group_id');
    }

    /**
     * Check if this product is part of a promotion group
     */
    public function isInPromotionGroup(): bool
    {
        return !is_null($this->promotion_group_id);
    }

    /**
     * Check if this product is a buy_x product in a promotion
     */
    public function isBuyXProduct(): bool
    {
        return $this->promotion_product_type === 'buy_x';
    }

    /**
     * Check if this product is a get_y product in a promotion
     */
    public function isGetYProduct(): bool
    {
        return $this->promotion_product_type === 'get_y';
    }

    /**
     * Get other products in the same promotion group
     */
    public function getGroupSiblings()
    {
        if (!$this->isInPromotionGroup()) {
            return collect();
        }

        return self::where('promotion_group_id', $this->promotion_group_id)
            ->where('id', '!=', $this->id)
            ->get();
    }

    /**
     * Check if this product can be returned individually
     * Products in promotion groups cannot be returned individually
     */
    public function canBeReturnedIndividually(): bool
    {
        return !$this->isInPromotionGroup();
    }
}
