<?php

namespace App\Exports\Sheets;

use App\Enums\Application\ProductTypeEnum;
use App\Enums\UnitNames;
use App\Models\MerchantsProduct;
use App\Models\TaxType;
use App\Models\WeightedProductSetting;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromGenerator;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ProductBranchSheet implements FromGenerator, WithHeadings, WithTitle, ShouldAutoSize
{
    private array $taxCache = [];
    private array $branchQuantityCache = [];
    private array $variantCache = [];
    private ?string $weightedUnit = null;

    public function __construct(
        private int $branchId,
        private string $branchName,
        private int $merchantId,
        private int $businessId,
        private ?string $search = null,
        private ?string $status = null
    ) {
        // Pre-load data to avoid repeated queries
        $this->preloadTaxData();
        $this->preloadBranchQuantities();
        $this->preloadVariants();
        $this->loadWeightedProductUnit();
    }

    /**
     * Preload tax data to reduce database queries
     */
    private function preloadTaxData(): void
    {
        $taxes = TaxType::withoutGlobalScopes()
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->select('id', 'percentage')
            ->get();

        $this->taxCache = $taxes->pluck('percentage', 'id')
            ->map(fn($percentage) => (string)$percentage)
            ->toArray();
    }

    /**
     * Preload branch quantities for this specific branch
     */
    private function preloadBranchQuantities(): void
    {
        // Load quantities for regular products for this branch only
        $regularQuantities = DB::table('merchant_product_branch_quantities')
            ->whereNull('product_variant_id')
            ->whereNotNull('product_id')
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->where('branch_id', $this->branchId)
            ->select('product_id', 'quantity', 'sell_price', 'wholesale_price')
            ->get();

        $this->branchQuantityCache['product'] = $regularQuantities
            ->keyBy('product_id')
            ->map(fn($item) => [
                'quantity' => $item->quantity,
                'sell_price' => $item->sell_price,
                'wholesale_price' => $item->wholesale_price,
            ])
            ->toArray();

        // Load quantities for variant products for this branch only
        $variantQuantities = DB::table('merchant_product_branch_quantities')
            ->whereNotNull('product_variant_id')
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->where('branch_id', $this->branchId)
            ->select('product_variant_id', 'quantity', 'sell_price', 'wholesale_price')
            ->get();

        $this->branchQuantityCache['variant'] = $variantQuantities
            ->keyBy('product_variant_id')
            ->map(fn($item) => [
                'quantity' => $item->quantity,
                'sell_price' => $item->sell_price,
                'wholesale_price' => $item->wholesale_price,
            ])
            ->toArray();
    }

    /**
     * Preload variant data to reduce database queries
     */
    private function preloadVariants(): void
    {
        $variants = DB::table('merchant_product_variants')
            ->where('merchant_id', $this->merchantId)
            ->where('business_id', $this->businessId)
            ->select('id', 'product_id', 'barcode', 'cost')
            ->get();

        // Group variants by product_id to handle multiple variants per product
        $this->variantCache = $variants
            ->groupBy('product_id')
            ->map(fn($productVariants) => $productVariants->map(fn($variant) => [
                'id' => $variant->id,
                'barcode' => $variant->barcode,
                'cost' => $variant->cost,
            ])->toArray())
            ->toArray();
    }

    /**
     * Load weighted product unit for displaying units with weighted products
     */
    private function loadWeightedProductUnit(): void
    {
        $weightedSetting = WeightedProductSetting::where('merchant_id', $this->merchantId)
            ->when($this->businessId, function($query) {
                $query->where('business_id', $this->businessId);
            })
            ->with('unit')
            ->first();

        if ($weightedSetting && $weightedSetting->unit) {
            $this->weightedUnit = UnitNames::translateFrom($weightedSetting->unit->name);
        }
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            __('excel.product.export.name'),
            __('excel.product.export.barcode'),
            __('excel.product.export.type'),
            __('excel.product.export.quantity'),
            __('excel.product.export.sell_price'),
            __('excel.product.export.wholesale_price'),
            __('excel.product.export.cost'),
            __('excel.product.export.tax_percentage'),
        ];
    }

    /**
     * Generator method for memory-efficient processing
     * @return \Generator
     */
    public function generator(): \Generator
    {
        // Process products in chunks to avoid memory issues
        $chunkSize = 100;
        $offset = 0;

        do {
            // Create base query
            $query = MerchantsProduct::withoutGlobalScopes()
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->select('id', 'name', 'name_en', 'barcode', 'sell_price', 'cost', 'tax_id', 'type', 'status')
                ->orderBy('id')
                ->offset($offset)
                ->limit($chunkSize);

            // Apply status filter if provided
            if ($this->status) {
                $query->where('status', $this->status);
            }

            // Apply search filter if provided
            if ($this->search) {
                $query->where(function($q) {
                    $q->where('name', 'LIKE', '%' . $this->search . '%')
                      ->orWhere('name_en', 'LIKE', '%' . $this->search . '%')
                      ->orWhere('barcode', 'LIKE', '%' . $this->search . '%')
                      ->orWhereHas('variants', function($vq) {
                          $vq->where('barcode', 'LIKE', '%' . $this->search . '%')
                            ->orWhereHas('variantAttributes.variantOption', function($q) {
                                $q->where('name', 'LIKE', '%' . $this->search . '%')
                                  ->orWhere('name_en', 'LIKE', '%' . $this->search . '%');
                            });
                      });
                });
            }

            $products = $query->get();

            // Process each product in the chunk
            foreach ($products as $product) {
                $taxPercentage = $this->taxCache[$product->tax_id] ?? '0';
                $isVariant = $product->type === ProductTypeEnum::VARIANT->value;

                if ($isVariant) {
                    // For variant products, yield a row for each variant
                    $variants = $this->variantCache[$product->id] ?? [];

                    if (empty($variants)) {
                        yield [
                            $product->name,
                            $product->barcode ?? '',
                            ProductTypeEnum::translateFrom($product->type),
                            $this->formatValueWithUnit('0', $product->type),
                            $this->formatValueWithUnit((string)$product->sell_price, $product->type),
                            $this->formatValueWithUnit((string)$product->sell_price, $product->type),
                            $this->formatValueWithUnit((string)$product->cost, $product->type),
                            $taxPercentage,
                        ];
                    } else {
                        // Yield a row for each variant
                        foreach ($variants as $variant) {
                            $branchData = $this->branchQuantityCache['variant'][$variant['id']] ?? null;
                            $quantity = $branchData['quantity'] ?? 0;
                            $sellPrice = $branchData['sell_price'] ?? $product->sell_price;
                            $wholesalePrice = $branchData['wholesale_price'] ?? $sellPrice;

                            yield [
                                $this->getVariantName($product->id, $variant['id']),
                                $variant['barcode'] ?? '',
                                ProductTypeEnum::translateFrom($product->type),
                                $this->formatValueWithUnit((string)$quantity, $product->type),
                                $this->formatValueWithUnit((string)$sellPrice, $product->type),
                                $this->formatValueWithUnit((string)$wholesalePrice, $product->type),
                                $this->formatValueWithUnit((string)$variant['cost'], $product->type),
                                $taxPercentage,
                            ];
                        }
                    }
                } else {
                    // For regular products, yield a single row
                    $branchData = $this->branchQuantityCache['product'][$product->id] ?? null;
                    $quantity = $branchData['quantity'] ?? 0;
                    $sellPrice = $branchData['sell_price'] ?? $product->sell_price;
                    $wholesalePrice = $branchData['wholesale_price'] ?? $sellPrice;

                    yield [
                        $product->name,
                        $product->barcode ?? '',
                        ProductTypeEnum::translateFrom($product->type),
                        $this->formatValueWithUnit((string)$quantity, $product->type),
                        $this->formatValueWithUnit((string)$sellPrice, $product->type),
                        $this->formatValueWithUnit((string)$wholesalePrice, $product->type),
                        $this->formatValueWithUnit((string)$product->cost, $product->type),
                        $taxPercentage,
                    ];
                }
            }

            $offset += $chunkSize;
            $hasMoreProducts = $products->count() === $chunkSize;

            // Clear memory after each chunk
            unset($products);

        } while ($hasMoreProducts);
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->branchName;
    }

    /**
     * Format value with unit for weighted products
     *
     * @param string $value
     * @param string $productType
     * @return string
     */
    private function formatValueWithUnit(string $value, string $productType): string
    {
        if ($productType === ProductTypeEnum::WEIGHTED->value && $this->weightedUnit) {
            return $value . ' ' . $this->weightedUnit;
        }

        return $value;
    }

    /**
     * Get variant name using a more memory-efficient approach
     *
     * @param int $productId
     * @param int $variantId
     * @return string
     */
    private function getVariantName($productId, $variantId): string
    {
        static $variantNameCache = [];

        if (!isset($variantNameCache[$variantId])) {
            // Get variant options to form the name - optimized raw query with soft delete filter
            $variantOptionNames = DB::table('merchant_product_variant_attributes as variant_attributes')
                ->join('merchant_variant_options as variant_options', 'variant_attributes.variant_option_id', '=', 'variant_options.id')
                ->where('variant_attributes.product_variant_id', $variantId)
                ->where('variant_attributes.merchant_id', $this->merchantId)
                ->where('variant_attributes.business_id', $this->businessId)
                ->whereNull('variant_attributes.deleted_at') // Filter out soft-deleted records
                ->distinct()
                ->pluck('variant_options.name')
                ->unique() // Additional safety to remove duplicates
                ->implode(' - ');

            // Get product name
            $productName = DB::table('merchants_products')
                ->where('id', $productId)
                ->where('merchant_id', $this->merchantId)
                ->where('business_id', $this->businessId)
                ->value('name');

            $variantNameCache[$variantId] = $productName . ($variantOptionNames ? ' - ' . $variantOptionNames : '');
        }

        return $variantNameCache[$variantId];
    }
}
