<?php

namespace App\Exports;

use App\Enums\Application\InvoiceTypeEnum;
use App\Helpers\Helper;
use App\Models\MerchantSaleInvoiceProduct;
use BCMathExtended\BC;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ReportProductExport implements FromCollection, WithHeadings
{
    protected $merchantId;
    protected $date_start;
    protected $date_end;
    protected $search;
    protected $state_id;
    protected $branch_id;
    protected $sortBy = 'productName';
    protected $sortDirection = 'desc';

    public function __construct($merchantId, $date_start = '', $date_end = '', $search = '', $state_id = '', $branch_id = '')
    {
        $this->merchantId = $merchantId;
        $this->date_start = $date_start;
        $this->date_end = $date_end;
        $this->search = $search;
        $this->state_id = $state_id;
        $this->branch_id = $branch_id;
    }

    public function headings(): array
    {
        return [
            'Product',
            'Barcode',
            'Transactions',
            'Sold Quantity',
            'Return Quantity',
            'Net Sales (Tax Exclusive)',
            'Cost of Goods Sold (Tax Exclusive)',
            'Gross Profit (Tax Exclusive)',
            'Gross Profit Margin %',
            'Total Tax'
        ];
    }

    public function collection()
    {
        $user = Auth::user();
        $main_account = $user->main_account;

        // Start building the query with eager loading to reduce database calls
        $query = MerchantSaleInvoiceProduct::query()->with('product');

        // Apply merchant filtering
        if (!$main_account) {
            $permittedBranches = $user->branches->pluck('branch_id')->toArray();
            $query->whereIn('branch_id', $permittedBranches);
        }

        // Apply date filtering if provided
        if (!empty($this->date_start) && !empty($this->date_end)) {
            $startDate = Carbon::parse($this->date_start)->startOfDay();
            $endDate = Carbon::parse($this->date_end)->endOfDay();
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply state filtering if provided
        if (!empty($this->state_id)) {
            $query->whereHas('invoice', function ($q) {
                $q->where('state_id', $this->state_id);
            });
        }

        // Apply branch filtering if provided
        if (!empty($this->branch_id)) {
            $query->where('branch_id', $this->branch_id);
        }

        // Apply search filtering if provided
        if (!empty($this->search)) {
            $search = $this->search;
            $query->whereHas('product', function ($q) use ($search) {
                $q->where('name', 'LIKE', '%' . $search . '%')
                    ->orWhere('barcode', 'LIKE', '%' . $search . '%');
            });
        }

        // Execute the query once and get all results
        $soldProducts = $query->get();

        // Group records by product ID, handling variants properly
        $groupedRecords = $soldProducts->groupBy(function ($record) {
            // Create a unique key for each product, handling variants differently
            return isset($record->is_variant) && $record->is_variant
                ? $record->product_id . '-' . $record->product_variant_id
                : $record->product_id;
        });

        // Process each group to calculate aggregated values
        $aggregatedResults = $groupedRecords->map(function ($group) {
            // Find the latest product info in the group
            $latestItem = $group->sortByDesc('created_at')->first();

            $productName = $latestItem->product_name_ar ?? ($latestItem->product->name );
            $barcode = $latestItem->barcode ??$latestItem->product?->barcode;

            $transactions = $group->count();
            $soldQuantity = 0;
            $returnedQuantity = 0;

            // Calculate sold and returned quantities
            foreach ($group as $record) {
                if ($record->invoice_type === InvoiceTypeEnum::SELL->value) {
                    $soldQuantity = BC::add($soldQuantity, $record->cart_quantity, 10);
                } elseif ($record->invoice_type === InvoiceTypeEnum::RETURN->value) {
                    $returnedQuantity = BC::add($returnedQuantity, $record->cart_quantity, 10);
                }
            }

            // Calculate financial metrics using Helper methods for better precision
            $netSales = Helper::bcCollectionMulSum($group, 'price_tax_exclusive', 'cart_quantity');
            $cogs = Helper::bcCollectionMulSum($group, 'cost_tax_exclusive', 'cart_quantity');
            $totalTax = BC::sub(
                Helper::bcCollectionMulSum($group, 'price_tax_inclusive', 'cart_quantity'),
                Helper::bcCollectionMulSum($group, 'price_tax_exclusive', 'cart_quantity'),
                10
            );

            $grossProfit = BC::sub($netSales, $cogs, 10);
            $grossProfitMargin = 0;

            if (BC::comp($cogs, '0', 10) != 0 && BC::comp($netSales, '0', 10) != 0) {
                $grossProfitMargin = BC::round(BC::mul(BC::div($grossProfit, $netSales, 10), 100, 10), 2);
            }

            return [
                'productName' => $productName,
                'barcode' => $barcode,
                'transactions' => $transactions,
                'soldQuantity' => $soldQuantity,
                'returnedQuantity' => $returnedQuantity,
                'netSales' => $netSales,
                'cogs' => $cogs,
                'grossProfit' => $grossProfit,
                'grossProfitMargin' => $grossProfitMargin,
                'totalTax' => $totalTax
            ];
        });

        // Sort the results if needed
        if (!is_null($this->sortBy)) {
            $aggregatedResults = $aggregatedResults->sortBy($this->sortBy, SORT_REGULAR, $this->sortDirection === 'desc');
        }

        // Format the data for Excel export
        return $aggregatedResults->values()->map(function ($item) {
            return [
                $item['productName'],
                $item['barcode'],
                (string)$item['transactions'],
                (string)$item['soldQuantity'],
                (string)$item['returnedQuantity'],
                (string)$item['netSales'],
                (string)$item['cogs'],
                (string)$item['grossProfit'],
                (string)$item['grossProfitMargin'],
                (string)$item['totalTax']
            ];
        });
    }
}
