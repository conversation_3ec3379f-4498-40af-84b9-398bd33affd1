<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class ProductTemplateExport implements WithHeadings, WithEvents, WithCustomStartCell
{
    // Define the headings for the Excel template
    public function headings(): array
    {
        $array = [
            __('excel.product.template.name'),
            __('excel.product.template.name_en'),
            __('excel.product.template.quantity'),
            __('excel.product.template.tax_percentage'),
            __('excel.product.template.category'),
            __('excel.product.template.sell_price'),
            __('excel.product.template.wholesale_price'),
            __('excel.product.template.cost'),
            __('excel.product.template.barcode'),
            __('excel.product.template.status'),
            __('excel.product.template.returnable'),
        ];

        if (getCurrentUser()->isZatcaEnabled)
        {
            $array + [ __('excel.product.tax_schema')];
        }

        return $array;
    }

    // Start from a specific cell
    public function startCell(): string
    {
        return 'A1';
    }

    // Events to apply dropdowns and formatting
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $sheet = $event->sheet->getDelegate();

                // Set header background color and text wrapping
                $sheet->getStyle('A1:R1')->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['rgb' => 'D3D3D3'],
                    ],
                ]);

                // Enable text wrapping for header row
                $sheet->getStyle('A1:R1')->getAlignment()->setWrapText(true);

                // Set the column widths to allow for better visibility
                foreach (range('A', 'R') as $columnID) {
                    $sheet->getColumnDimension($columnID)->setWidth(20);
                }

                // Enable text wrapping for all data cells (rows 2 to 100)
                $sheet->getStyle('A2:R100')->getAlignment()->setWrapText(true);
            },
        ];
    }
}
