<?php

namespace App\Livewire;

use App\Models\MerchantFulfillment;
use App\Services\FulfillmentService;
use Illuminate\Support\Facades\Lang;
use Livewire\Component;

class FulfillmentUpdate extends Component
{
    public $fulfillment;
    public $fulfillmentId;
    public $products = [];
    public $status;
    public $notes;

    protected $listeners = ['refreshFulfillment'];

    public function mount($fulfillmentId)
    {
        $this->fulfillmentId = $fulfillmentId;
        $this->loadFulfillment();
    }

    public function loadFulfillment()
    {
        $this->fulfillment = MerchantFulfillment::with([
            'sourceBranch',
            'fulfillmentBranch',
            'products' => function($query) {
                $query->with(['product', 'variant']);
            }
        ])->find($this->fulfillmentId);

        if (!$this->fulfillment) {
            return redirect()->route('fulfillments.index');
        }

        $this->status = $this->fulfillment->status;
        $this->notes = $this->fulfillment->notes;

        // Initialize products array with current values
        $this->products = [];
        foreach ($this->fulfillment->products as $product) {
            $this->products[$product->id] = [
                'fulfilled_quantity' => $product->fulfilled_quantity,
                'requested_quantity' => $product->requested_quantity,
                'status' => $product->status
            ];
        }
    }

    public function refreshFulfillment()
    {
        $this->loadFulfillment();
    }

    /**
     * Validate and sanitize fulfilled quantity when updated
     *
     * @param mixed $value
     * @param string $key
     * @return void
     */
    public function updatedProducts($value, $key)
    {
        // Extract the product ID from the key (format: "products.{id}.fulfilled_quantity")
        $parts = explode('.', $key);
        if (count($parts) === 3 && $parts[2] === 'fulfilled_quantity') {
            $productId = $parts[1];

            // Sanitize the input to ensure it's a valid number
            $value = $this->sanitizeInput($value);

            // Ensure the value is within valid range (0 to requested_quantity)
            if (isset($this->products[$productId])) {
                $requestedQuantity = $this->products[$productId]['requested_quantity'];

                if ($value < 0) {
                    $value = 0;
                } elseif ($value > $requestedQuantity) {
                    $value = $requestedQuantity;
                    $this->dispatch('toast', message: Lang::get('messages.fulfilled_quantity_exceeds_requested'));
                }

                // Update the value with the sanitized one
                $this->products[$productId]['fulfilled_quantity'] = $value;
            }
        }
    }

    /**
     * Sanitize numeric input
     *
     * @param mixed $value
     * @return int
     */
    private function sanitizeInput($value)
    {
        // Remove any non-numeric characters
        $value = preg_replace('/[^0-9]/', '', $value);

        // Convert to integer
        return (int) $value;
    }

    public function updateFulfillment()
    {
        // Validate input
        $this->validate([
            'products.*.fulfilled_quantity' => 'required|integer|min:0',
            'notes' => 'nullable|string'
        ]);

        try {
            // Sanitize all fulfilled quantities and ensure they're within valid ranges
            foreach ($this->products as $productId => $data) {
                // Sanitize the fulfilled quantity
                $fulfilledQuantity = $this->sanitizeInput($data['fulfilled_quantity']);

                // Ensure it's within valid range
                if ($fulfilledQuantity < 0) {
                    $fulfilledQuantity = 0;
                } elseif ($fulfilledQuantity > $data['requested_quantity']) {
                    $fulfilledQuantity = $data['requested_quantity'];
                    $this->dispatch('toast', message: Lang::get('messages.fulfilled_quantity_exceeds_requested'));
                }

                // Update with sanitized value
                $this->products[$productId]['fulfilled_quantity'] = $fulfilledQuantity;
            }

            // Check if only notes were changed
            $onlyNotesChanged = true;
            foreach ($this->products as $productId => $data) {
                $fulfillmentProduct = $this->fulfillment->products->find($productId);
                if ($fulfillmentProduct && (string)$fulfillmentProduct->fulfilled_quantity !== (string)$data['fulfilled_quantity']) {
                    $onlyNotesChanged = false;
                    break;
                }
            }

            if ($onlyNotesChanged) {
                // Just update the notes without changing status
                $this->fulfillment->notes = $this->notes;
                $this->fulfillment->save();

                \Illuminate\Support\Facades\Log::info('Only notes were updated for fulfillment #' . $this->fulfillment->id);

                // Redirect to list page with success message
                session()->flash('success', __('messages.updated_successfully'));

                // Add debug log before redirect
                \Illuminate\Support\Facades\Log::info('About to redirect to fulfillments.index after notes update');

                return redirect()->route('fulfillments.index');
            } else {
                // Update fulfillment using service
                $fulfillmentService = app(FulfillmentService::class);
                $fulfillmentService->processFulfillment($this->fulfillment, $this->products);

                // Make sure notes are updated too
                $this->fulfillment->refresh();
                $this->fulfillment->notes = $this->notes;
                $this->fulfillment->save();

                \Illuminate\Support\Facades\Log::info('Fulfillment #' . $this->fulfillment->id . ' updated with new quantities and notes');

                // Redirect to list page with success message
                session()->flash('success', __('messages.updated_successfully'));

                // Add debug log before redirect
                \Illuminate\Support\Facades\Log::info('About to redirect to fulfillments.index after quantity update');

                return redirect()->route('fulfillments.index');
            }

            // This will only run if the redirect doesn't happen for some reason
            $this->dispatch('toast', message: Lang::get('messages.fulfillment_updated'));
            $this->loadFulfillment(); // Reload data
        } catch (\Exception $exception) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::error('Fulfillment update error: ' . $exception->getMessage());
            \Illuminate\Support\Facades\Log::error('Exception trace: ' . $exception->getTraceAsString());
            $this->dispatch('toast', message: Lang::get('messages.fulfillment_update_error'));
        }
    }

    public function render()
    {
        return view('livewire.fulfillment-update');
    }
}
