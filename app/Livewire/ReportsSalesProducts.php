<?php

namespace App\Livewire;

use App\Enums\Application\InvoiceTypeEnum;
use App\Helpers\Helper;
use App\Models\MerchantBranch;
use App\Models\MerchantInvoice;
use App\Models\MerchantSaleInvoiceProduct;
use App\Models\State;
use BCMathExtended\BC;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ReportsSalesProducts extends Component
{
    use WithPagination;
    public $status;
    public $search;
    public $date_start;
    public $date_end;
    public $state_id;
    public $branch_id;

    protected $queryString = [
        'search',
        'status',
        'date_start',
        'date_end',
        'state_id',
        'branch_id',
    ];
    public $sortBy = 'productName';
    public $sortDirection = 'desc';
    public $fullDate;
    public $states = [];
    public $branches = [];

    public function mount()
    {
        $user = Auth::user();

        // Get only states and branches that exist in invoices in a single query
        $invoiceData = MerchantInvoice::query()
            ->select('state_id', 'branch_id')
            ->where(function($invoiceQuery) {
                $invoiceQuery->whereNotNull('state_id')
                            ->orWhereNotNull('branch_id');
            })
            ->distinct()
            ->get();

        $stateIds = $invoiceData->pluck('state_id')->filter()->unique();
        $branchIds = $invoiceData->pluck('branch_id')->filter()->unique();

        $this->states = State::whereIn('id', $stateIds)->get();

        // If user is not main account, only show branches they have access to
        if (!$user->main_account) {
            $permittedBranches = $user->branches->pluck('branch_id')->toArray();
            $branchIds = $branchIds->filter(function($id) use ($permittedBranches) {
                return in_array($id, $permittedBranches);
            });
        }

        // Fetch branches in a single query with proper filtering
        $this->branches = MerchantBranch::whereIn('id', $branchIds)
            ->get();
    }

    public function render()
    {
        $user = getCurrentUser();
        $main_account = $user->main_account;

        // Start building the query
        $query = MerchantSaleInvoiceProduct::query()->with(['product' => function($productQuery) {
            $productQuery->withTrashed(); // Include soft deleted products
        }]);

        // Apply merchant filtering
        if (!$main_account) {
            $permittedBranches = $user->branches->pluck('branch_id')->toArray();
            $query->whereIn('branch_id', $permittedBranches);
        }

        // Apply date filtering if provided
        if (!empty($this->date_start) && !empty($this->date_end)) {
            $startDate = Carbon::parse($this->date_start)->startOfDay();
            $endDate = Carbon::parse($this->date_end)->endOfDay();

            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply state filtering if provided
        if (!empty($this->state_id)) {
            $query->whereHas('invoice', function($invoiceQuery) {
                $invoiceQuery->where('state_id', $this->state_id);
            });
        }

        // Apply branch filtering if provided
        if (!empty($this->branch_id)) {
            $query->where('branch_id', $this->branch_id);
        }

        if (!empty($this->search)) {
            $searchTerm = $this->search;
            $query->where(function($q) use ($searchTerm) {
                $q->whereHas('product', function($productQuery) use ($searchTerm) {
                    $productQuery->where('name', 'LIKE', '%' . $searchTerm . '%')
                                 ->orWhere('barcode', 'LIKE', '%' . $searchTerm . '%');
                })
                ->orWhere('product_name_ar', 'LIKE', '%' . $searchTerm . '%')
                ->orWhere('product_name_en', 'LIKE', '%' . $searchTerm . '%')
                ->orWhere('barcode', 'LIKE', '%' . $searchTerm . '%');
            });
        }

        $soldProducts = $query->get();

        $groupedRecords = $soldProducts->groupBy(function($record) {
            // Create a unique key for each product, handling variants differently
            return isset($record->is_variant) && $record->is_variant
                ? $record->product_id . '-' . $record->product_variant_id
                : $record->product_id;
        });

        $aggregatedResults = $groupedRecords->map(function($group) {
            // Find the latest product info in the group
            $latestItem = $group->sortByDesc('created_at')->first();

            $productName = $latestItem->product_name_ar ?? ($latestItem->product?->name ?? 'Unknown');
            $barcode = $latestItem->barcode ?? ($latestItem->product?->barcode ?? 'N/A');

            $transactions = $group->count();
            $soldQuantity = 0;
            $returnedQuantity = 0;

            foreach ($group as $record) {
                if ($record->invoice_type === InvoiceTypeEnum::SELL->value) {
                    $soldQuantity = BC::add($soldQuantity, $record->cart_quantity, 10);
                } elseif ($record->invoice_type === InvoiceTypeEnum::RETURN->value) {
                    $returnedQuantity = BC::add($returnedQuantity, $record->cart_quantity, 10);
                }
            }

            $netSales = Helper::bcCollectionMulSum($group, 'price_tax_exclusive', 'cart_quantity');
            $cogs = Helper::bcCollectionMulSum($group, 'cost_tax_exclusive', 'cart_quantity');
            $totalTax = BC::sub(
                Helper::bcCollectionMulSum($group, 'price_tax_inclusive', 'cart_quantity'),
                Helper::bcCollectionMulSum($group, 'price_tax_exclusive', 'cart_quantity'),
                10
            );

            $grossProfit = BC::sub($netSales, $cogs, 10);
            $grossProfitMargin = 0;

            if (BC::comp($cogs, '0', 10) != 0 && BC::comp($netSales, '0', 10) != 0) {
                $grossProfitMargin = BC::round(BC::mul(BC::div($grossProfit, $netSales, 10), 100, 10), 2);
            }

            return [
                'barcode' => $barcode,
                'productName' => $productName,
                'transactions' => $transactions,
                'soldQuantity' => $soldQuantity,
                'returnedQuantity' => $returnedQuantity,
                'netSales' => $netSales,
                'cogs' => $cogs,
                'grossProfit' => $grossProfit,
                'totalTax' => $totalTax,
                'grossProfitMargin' => $grossProfitMargin
            ];
        });

        $paginatedData = Helper::paginate($aggregatedResults, 10, $this->sortBy, $this->sortDirection);

        return view('livewire.reports-sales-products', ['products' => $paginatedData]);
    }

    public function searchUpdated() {
        $this->resetPage();
    }

    public function sortByFunction($field) {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortBy = $field;
        $this->resetPage();
    }

    public function dateChanged($start, $end, $fullDate) {
        $this->date_start = $start;
        $this->date_end = $end;
        $this->fullDate = $fullDate;
        $this->resetPage();
    }

    public function stateChanged($stateId) {
        $this->state_id = $stateId;
        $this->resetPage();
    }

    public function branchChanged($branchId) {
        $this->branch_id = $branchId;
        $this->resetPage();
    }
}
