<?php

namespace App\Livewire;

use App\Models\MerchantFulfillment;
use App\Models\MerchantFulfillmentProduct;
use App\Services\FulfillmentService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Livewire\Component;
use Livewire\WithPagination;

class PosFulfillments extends Component
{
    use WithPagination;

    public $branchId;
    public $registeryId;
    public $search = '';
    public $fulfillmentStatus = 'pending'; // Default to pending fulfillments
    public $perPage = 5;
    public $selectedFulfillment = null;
    public $fulfillmentProducts = [];
    public $processingFulfillment = false;
    public $showCancelConfirmation = false;
    public $cancellationReason = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'fulfillmentStatus' => ['except' => 'pending'],
    ];

    protected $listeners = [
        'refreshFulfillmentsList' => '$refresh',
        'processFulfillment' => 'processFulfillment',
        'cancelFulfillment' => 'cancelFulfillment',
        'confirmCancelFulfillment' => 'confirmCancelFulfillment',
        'updatedFulfillmentStatus' => 'updatedFulfillmentStatus'
    ];

    public function mount($branchId, $registeryId)
    {
        $this->branchId = $branchId;
        $this->registeryId = $registeryId;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatedFulfillmentStatus($value){
        $this->fulfillmentStatus = $value;
        $this->resetPage();
    }

    /**
     * Validate and sanitize fulfilled quantity when updated
     *
     * @param mixed $value
     * @param string $key
     * @return void
     */
    public function updatedFulfillmentProducts($value, $key)
    {
        // Extract the product ID from the key (format: "fulfillmentProducts.{id}.fulfilled_quantity")
        $parts = explode('.', $key);
        if (count($parts) === 3 && $parts[2] === 'fulfilled_quantity') {
            $productId = $parts[1];

            // Sanitize the input to ensure it's a valid number
            $value = $this->sanitizeInput($value);

            // Ensure the value is within valid range (0 to requested_quantity)
            if (isset($this->fulfillmentProducts[$productId])) {
                $requestedQuantity = $this->fulfillmentProducts[$productId]['requested_quantity'];

                if ($value < 0) {
                    $value = 0;
                } elseif ($value > $requestedQuantity) {
                    $value = $requestedQuantity;
                    $this->showToast(Lang::get('messages.fulfilled_quantity_exceeds_requested'));
                }

                // Update the value with the sanitized one
                $this->fulfillmentProducts[$productId]['fulfilled_quantity'] = $value;
            }
        }
    }

    /**
     * Sanitize numeric input
     *
     * @param mixed $value
     * @return int
     */
    private function sanitizeInput($value)
    {
        // Remove any non-numeric characters
        $value = preg_replace('/[^0-9]/', '', $value);

        // Convert to integer
        return (int) $value;
    }

    public function render()
    {
        $user = Auth::user();

        // Get fulfillments for the current branch that are branch_transfer type
        $query = MerchantFulfillment::query()
            ->with(['sourceBranch', 'fulfillmentBranch', 'customer', 'invoice', 'products'])
            ->where('merchant_id', $user->merchant_id)
            ->where('fulfillment_type', 'branch_transfer')
            ->where('fulfillment_branch_id', $this->branchId); // Only show fulfillments for this branch

        // Apply search filter
        if (!empty($this->search)) {
            $query->where(function($q) {
                $q->where('pretty_id', 'like', '%' . $this->search . '%')
                  ->orWhereHas('customer', function($q) {
                      $q->where('name', 'like', '%' . $this->search . '%');
                  })
                  ->orWhereHas('invoice', function($q) {
                      $q->where('pretty_id', 'like', '%' . $this->search . '%');
                  });
            });
        }

        // Apply status filter
        if (!empty($this->fulfillmentStatus)) {
            $query->where('status', $this->fulfillmentStatus);
        }

        $fulfillments = $query->orderBy('created_at', 'desc')->paginate($this->perPage);

        return view('livewire.pos-fulfillments', [
            'fulfillments' => $fulfillments
        ]);
    }

    /**
     * View fulfillment details
     *
     * @param int $fulfillmentId
     * @return void
     */
    public function viewFulfillment($fulfillmentId)
    {
        $this->selectedFulfillment = MerchantFulfillment::with([
            'sourceBranch',
            'fulfillmentBranch',
            'customer',
            'invoice',
            'products' => function($query) {
                $query->with(['product', 'variant']);
            }
        ])->find($fulfillmentId);

        if (!$this->selectedFulfillment) {
            $this->showToast(Lang::get('messages.fulfillment_not_found'));
            return;
        }

        // Initialize products array with current values
        $this->fulfillmentProducts = [];
        foreach ($this->selectedFulfillment->products as $product) {
            $this->fulfillmentProducts[$product->id] = [
                'id' => $product->id,
                'fulfilled_quantity' => $product->fulfilled_quantity ?? 0,
                'requested_quantity' => $product->requested_quantity,
                'status' => $product->status,
                'product_name' => $product->product->name . ($product->variant ? ' - ' . $product->variant->name : '')
            ];
        }

        // Open the modal
        $this->dispatch('open-fulfillment-details-modal');
    }

    /**
     * Process the fulfillment
     *
     * @return void
     */
    public function processFulfillment()
    {
        if (!$this->selectedFulfillment) {
            $this->showToast(Lang::get('messages.fulfillment_not_found'));
            return;
        }

        // Validate that we're not exceeding requested quantities and sanitize values
        foreach ($this->fulfillmentProducts as $id => $product) {
            // Sanitize the fulfilled quantity
            $fulfilledQuantity = $this->sanitizeInput($product['fulfilled_quantity']);

            // Ensure it's within valid range
            if ($fulfilledQuantity < 0) {
                $fulfilledQuantity = 0;
            } elseif ($fulfilledQuantity > $product['requested_quantity']) {
                $fulfilledQuantity = $product['requested_quantity'];
                $this->showToast(Lang::get('messages.fulfilled_quantity_exceeds_requested'));
                return;
            }

            // Update with sanitized value
            $this->fulfillmentProducts[$id]['fulfilled_quantity'] = $fulfilledQuantity;
        }

        $this->processingFulfillment = true;

        try {
            // Update fulfillment using service
            $fulfillmentService = app(FulfillmentService::class);
            $fulfillmentService->processFulfillment($this->selectedFulfillment, $this->fulfillmentProducts);

            $this->showToast(Lang::get('messages.fulfillment_processed_successfully'));

            // Close the modal
            $this->dispatch('close-fulfillment-details-modal');

            // Reset selected fulfillment
            $this->selectedFulfillment = null;
            $this->fulfillmentProducts = [];

            // Refresh the list
            $this->dispatch('refreshFulfillmentsList');
        } catch (\Exception $exception) {
            $this->showToast(Lang::get('messages.fulfillment_processing_failed'));
        } finally {
            $this->processingFulfillment = false;
        }
    }

    /**
     * Show the cancellation confirmation modal
     *
     * @return void
     */
    public function cancelFulfillment()
    {
        if (!$this->selectedFulfillment) {
            $this->showToast(Lang::get('messages.fulfillment_not_found'));
            return;
        }

        // Only allow cancellation of pending or partially_fulfilled fulfillments
        if ($this->selectedFulfillment->status === 'fulfilled' || $this->selectedFulfillment->status === 'cancelled') {
            $this->showToast(Lang::get('messages.cannot_cancel_fulfilled_or_cancelled_fulfillment'));
            return;
        }

        $this->showCancelConfirmation = true;
        $this->cancellationReason = '';

        // Dispatch event to show the cancellation modal
        $this->dispatch('open-cancellation-modal');
    }

    /**
     * Confirm and process the cancellation
     *
     * @return void
     */
    public function confirmCancelFulfillment()
    {
        if (!$this->selectedFulfillment) {
            $this->showToast(Lang::get('messages.fulfillment_not_found'));
            return;
        }

        try {
            // Cancel the fulfillment using the service
            $fulfillmentService = app(FulfillmentService::class);
            $fulfillmentService->cancelFulfillment($this->selectedFulfillment, $this->cancellationReason);

            $this->showToast(Lang::get('messages.fulfillment_cancelled_successfully'));

            // Close both modals
            $this->dispatch('close-cancellation-modal');
            $this->dispatch('close-fulfillment-details-modal');

            // Reset selected fulfillment and cancellation data
            $this->selectedFulfillment = null;
            $this->fulfillmentProducts = [];
            $this->showCancelConfirmation = false;
            $this->cancellationReason = '';

            // Refresh the list
            $this->dispatch('refreshFulfillmentsList');
        } catch (\Exception $e) {
            $this->showToast(Lang::get('messages.fulfillment_cancellation_failed') . ': ' . $e->getMessage());
        }
    }

    /**
     * Show a toast message
     *
     * @param string $message
     * @return void
     */
    private function showToast($message)
    {
        $this->dispatch('toast', message: $message);
    }
}
