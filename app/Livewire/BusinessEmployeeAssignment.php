<?php

namespace App\Livewire;

use App\Models\MerchantBranch;
use App\Models\MerchantUserBranch;
use Livewire\Component;
use App\Models\Business;
use App\Models\MerchantUser;
use App\Models\MerchantGroup;
use App\Models\UserBusinessGroup;
use Illuminate\Support\Facades\Auth;

class BusinessEmployeeAssignment extends Component
{
    public $selectedBusinessId = null;
    public $selectedEmployeeId = null;
    public $selectedGroupId = null;

    public $availableGroups = [];

    public $employees = [];
    public $businesses = [];
    public $groups = [];
    public $assignments = [];

    public function mount()
    {
        // Check if user has permission
        $user = getCurrentUser();
        if (!$user->main_account) {
            return redirect()->route('home')->with('error', 'You do not have permission to access this page.');
        }

        $this->loadData();
    }

    private function loadData()
    {
        $user = getCurrentUser();

        // Get all employees except the current user
        $this->employees = MerchantUser::where('merchant_id', $user->merchant_id)
            ->where('id', '!=', $user->id)
            ->get();

        // Get all businesses for this merchant
        $this->businesses = Business::withoutGlobalScopes()
            ->where('merchant_id', $user->merchant_id)
            ->get();

        // Get all current assignments with relationships
        $this->assignments = UserBusinessGroup::where('merchant_id', $user->merchant_id)
            ->with(['user', 'business', 'group'])
            ->get();
    }

    public function updatedSelectedBusinessId($businessId)
    {
        \Log::info('Business selected:', ['business_id' => $businessId]);

        $this->selectedGroupId = null; // Reset group selection
        $this->availableGroups = []; // Reset groups

        if ($businessId) {
            $user = getCurrentUser();

            // Get groups for selected business
            $this->availableGroups = MerchantGroup::where('merchant_id', $user->merchant_id)
                ->where('business_id', $businessId)
                ->get()
                ->toArray();

            \Log::info('Groups loaded:', [
                'count' => count($this->availableGroups),
                'groups' => $this->availableGroups
            ]);
        }
    }

    public function assign()
    {
        $user = getCurrentUser();

        if (!$user->main_account) {
            session()->flash('error', 'You do not have permission to perform this action.');
            return;
        }

        $this->validate([
            'selectedEmployeeId' => 'required|exists:merchant_users,id',
            'selectedBusinessId' => 'required|exists:businesses,id',
            'selectedGroupId' => 'required|exists:merchant_groups,id',
        ]);

        // Check if assignment already exists
        $existingAssignment = UserBusinessGroup::where('user_id', $this->selectedEmployeeId)
            ->where('business_id', $this->selectedBusinessId)
            ->first();

        if ($existingAssignment) {
            session()->flash('error', 'This employee already has access to this business.');
            return;
        }

        //TODO check this while using trait
        // Create new assignment
        UserBusinessGroup::create([
            'merchant_id' => $user->merchant_id,
            'business_id' => $this->selectedBusinessId,
            'user_id' => $this->selectedEmployeeId,
            'group_id' => $this->selectedGroupId,
        ]);

        $branches = MerchantBranch::query()
            ->where('merchant_id', $user->merchant_id)
            ->where('business_id', $this->selectedBusinessId)
            ->select('id')
            ->get();

        foreach($branches as $branch){
            MerchantUserBranch::create([
                'merchant_id' => $user->merchant_id,
                'business_id' => $this->selectedBusinessId,
                'user_id' => $this->selectedEmployeeId,
                'branch_id' => $branch->id,
            ]);
        }

        // Reset form and refresh data
        $this->reset(['selectedEmployeeId', 'selectedBusinessId', 'selectedGroupId']);
        $this->loadData();

        session()->flash('success', __('messages.created_successfully'));
    }

    public function removeAssignment($userId, $businessId)
    {
        $user = getCurrentUser();

        if (!$user->main_account) {
            session()->flash('error', 'You do not have permission to perform this action.');
            return;
        }

        //TODO check this while using trait
        UserBusinessGroup::query()
            ->where('merchant_id', $user->merchant_id)
            ->where('business_id', $businessId)
            ->where('user_id', $userId)
            ->delete();

        $branches = MerchantBranch::query()
            ->where('merchant_id', $user->merchant_id)
            ->where('business_id', $businessId)
            ->pluck('id')
            ->toArray();

        MerchantUserBranch::query()
            ->where('merchant_id', $user->merchant_id)
            ->where('business_id', $businessId)
            ->where('user_id', $userId)
            ->whereIn('branch_id', $branches)
            ->delete();

        // Refresh data
        $this->loadData();

        session()->flash('success', __('messages.deleted_successfully'));
    }

    public function render()
    {
        // Add authorization check in render
        if (!getCurrentUser()->main_account) {
            return view('livewire.unauthorized');
        }

        return view('livewire.business-employee-assignment');
    }
}
