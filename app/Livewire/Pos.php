<?php

namespace App\Livewire;

use App\Models\MerchantBranch;
use App\Models\MerchantRegisterRecord;
use App\Models\MerchantRegisterSetting;
use App\Models\MerchantRegistery;
use Carbon\Carbon;
use Livewire\Component;

class Pos extends Component{
    protected $listeners = [
        'setSelectedInvoiceId',
        'switchToInvoicesTab',
        'switch-to-cashier-tab' => 'switchToCashierTab',
        'setActiveTabFromHistory' => 'handleSetActiveTabFromHistory'
    ];
    public $branchId;
    public $registeryId;
    public $branches;
    public $registeries;
    public $branchesAndRegisteries;
    public $main_account;
    public $permissions;
    public $registerSettings;
    public $user;
    public $permittedBranches = [];
    public $merchantRegistries;
    public $registerOpenTooLong = false;
    public $registerOpenHours = 0;
    public $maxOpenHours = 8;
    public $activeTab = 'cashier'; // Default tab is cashier
    public $selectedInvoiceId = null; // Track the selected invoice ID
    public $pendingFulfillmentsCount = 0; // Track the number of pending fulfillments

    public function render()
    {
        $register = MerchantRegisterRecord::where([
            'branch_id' => $this->branchId,
            'register_id' => $this->registeryId
        ])
            ->latest()
            ->first();

        // Check if register has been open for too long
        $this->checkRegisterOpenDuration($register);

        if($this->registerSettings?->open_close_register){
            if($this->main_account || $this->permissions?->cashier_open){
                // new user would open the register for the first time.
                if($register == null){
                    redirect("pos/{$this->branchId}/{$this->registeryId}/open");
                }
                // this means that the registry has been closed.
                if($register != null and $register->complete_date != null){
                    redirect("pos/{$this->branchId}/{$this->registeryId}/open");
                }
            }
        }

        if(!$this->main_account) {
            if (!in_array($this->branchId, $this->permittedBranches)) {
                redirect('pos');
                return view('livewire.pos-images');
            }
        }

        if($this->registerSettings?->screen === 'sale_default'){
            return view('livewire.pos-default');
        }

        if($this->registerSettings?->screen === 'sale_images'){
            $this->dispatch('setScreenSetting','sale_images');
            return view('livewire.pos-images');
        }
    }

    /**
     * Check if the register has been open for too long
     *
     * @param MerchantRegisterRecord|null $register The register record
     * @return void
     */
    private function checkRegisterOpenDuration($register)
    {
        // If register is not open or doesn't exist, no need to check
        if (!$register || $register->complete_date !== null) {
            $this->registerOpenTooLong = false;
            return;
        }

        // Get the max open hours from settings or use default
        $this->maxOpenHours = $this->registerSettings?->max_open_hours ?? 8;

        // Calculate how long the register has been open
        $openTime = Carbon::parse($register->created_at);
        $now = Carbon::now();
        $diffInHours = $openTime->diffInHours($now);
        // dd($diffInHours);
        $this->registerOpenHours = $diffInHours;

        // Check if it's been open too long
        $this->registerOpenTooLong = $diffInHours >= $this->maxOpenHours;
    }

    /**
     * Switch between tabs
     *
     * @param string $tab Tab to switch to ('cashier', 'invoices', or 'fulfillments')
     * @return void
     */
    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;

        // If switching to invoices tab, refresh the invoices list
        if ($tab === 'invoices') {
            $this->dispatch('refreshInvoicesList');
        }

        // If switching to fulfillments tab, refresh the fulfillments list
        if ($tab === 'fulfillments') {
            $this->dispatch('refreshFulfillmentsList');
        }
    }

    /**
     * Handle the setActiveTabFromHistory event from JavaScript
     *
     * @param string $tabName Tab name to set active
     * @return void
     */
    public function handleSetActiveTabFromHistory($tabName)
    {
        if (in_array($tabName, ['cashier', 'invoices', 'fulfillments'])) {
            $this->setActiveTab($tabName);
        }
    }

    /**
     * Set the selected invoice ID
     *
     * @param int $invoiceId The invoice ID to select
     * @return void
     */
    public function setSelectedInvoiceId($invoiceId)
    {
        $this->selectedInvoiceId = $invoiceId;
    }

    /**
     * Switch to the invoices tab and set the search value
     *
     * @param array $params Parameters containing the search value
     * @return void
     */
    public function switchToInvoicesTab($params)
    {
        // Switch to the invoices tab
        $this->activeTab = 'invoices';

        // Dispatch an event to set the search value in the PosInvoices component
        $this->dispatch('setInvoiceSearch', ['search' => $params['search']]);
    }

    /**
     * Switch to the cashier tab
     *
     * @return void
     */
    public function switchToCashierTab()
    {
        // Switch to the cashier tab
        $this->activeTab = 'cashier';
    }

    public function mount($branchId = null, $registeryId = null){
        $this->user = getCurrentUser();
        $this->main_account = $this->user->main_account;
        $this->permissions = $this->user->group->permissions;

        if(!$this->main_account){
            $this->permittedBranches = $this->user->branches->pluck('branch_id');
        }

        $branches = MerchantBranch::where(['merchant_branches.merchant_id' => $this->user->merchant_id])
            ->when($this->permittedBranches, fn($query) => $query->whereIn('id', $this->permittedBranches))
            ->get();

        $this->merchantRegistries = MerchantRegistery::query()->get();

        $this->branchesAndRegisteries = [];
        foreach($branches as $branch){
            $this->branchesAndRegisteries[$branch->id] = $branch;
            $this->branchesAndRegisteries[$branch->id]['registers'] = $this->merchantRegistries->where('branch_id', $branch->id);
        }

        $this->registerSettings = MerchantRegisterSetting::query()
            ->where('register_id', $this->registeryId)
            ->first();

        if($branchId != null and $registeryId != null){
            $branchExists = $branches->contains('id', $branchId);
            $registeryExists = $this->merchantRegistries->where('branch_id', $branchId)->contains('id', $registeryId);

            if ($branchExists && $registeryExists) {
                $this->branchId = $branchId;
                $this->registeryId = $registeryId;

                // Check if there's a tab parameter in the URL and set the active tab accordingly
                if (request()->has('tab') && in_array(request()->tab, ['cashier', 'invoices', 'fulfillments'])) {
                    $this->activeTab = request()->tab;
                }
            } else {
                abort(404, 'Branch or Registry not found.');
            }
        }else{
            $branch = $branches->first();

            $registery = $this->merchantRegistries->where('branch_id',$branch->id)->first();

            return redirect("pos/{$branch['id']}/{$registery['id']}");
        }
    }
}
