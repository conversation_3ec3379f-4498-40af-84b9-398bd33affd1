<?php

namespace App\Livewire;

use App\Models\MerchantVariant;
use App\Models\MerchantVariantOption;
use Illuminate\Support\Facades\Lang;
use Livewire\Component;

class VariantProductList extends Component
{
    public $variants = [];
    public $generatedProducts = [];
    public $showGeneratedProducts = false;
    public $productName = '';
    public $branches = [];
    public $selectedBranch = null;
    public $branchValues = [];
    public $showAllBranches = false;
    public $oldProducts = [];

    // New properties
    public $availableVariants = [];
    public $availableOptions = [];
    public $newVariantName = '';
    public $newOptionName = '';
    public $selectedVariantId = null;

    protected $listeners = [
        'productNameUpdated' => 'productNameUpdated',
        'variantAdded' => 'refreshVariants',
        'optionAdded' => 'refreshOptions',
        'selectVariant' => 'selectVariantById',
        'selectOption' => 'selectOptionById',
        'refreshComponent' => '$refresh'
    ];

    public function mount($oldProducts = [], $productName = '', $branches = [], $variants = [])
    {
        // dd($variants);
        $this->productName = $productName;
        $this->branches = $branches;

        // Ensure variants is always an array
        $this->variants = is_array($variants) ? $variants : [];

        // Convert oldProducts to array if it's another type
        if (!is_array($oldProducts)) {
            $oldProducts = [];
        }

        $this->oldProducts = $oldProducts;
        $this->showAllBranches = request()->cookie('variant_product_display_mode', false);
        $this->loadAvailableVariants();

        if (!empty($this->branches)) {
            $this->selectedBranch = $this->branches[0]['id'];
        }

        // Process oldProducts if available (for edit mode)
        if (!empty($oldProducts)) {
            $this->initializeFromOldProducts();
        }
    }

    private function initializeFromOldProducts()
    {
        // Set generated products from old products
        $this->generatedProducts = [];

        // Process each old product and ensure it has the correct keys
        foreach ($this->oldProducts as $product) {
            $processedProduct = $product;

            // Make sure 'name' key exists (view expects this)
            if (!isset($processedProduct['name']) && isset($processedProduct['product_name'])) {
                $processedProduct['name'] = $processedProduct['product_name'];
            }

            // Make sure 'product_name' key exists (controller expects this)
            if (!isset($processedProduct['product_name']) && isset($processedProduct['name'])) {
                $processedProduct['product_name'] = $processedProduct['name'];
            }

            if (!isset($processedProduct['cost']) && isset($product['cost'])) {
                $processedProduct['cost'] = (string)$product['cost'];
            }

            // Ensure branch_values exists and is properly initialized
            if (!isset($processedProduct['branch_values']) || !is_array($processedProduct['branch_values']) || empty($processedProduct['branch_values'])) {
                $processedProduct['branch_values'] = $this->initializeBranchValues();
            } else {
                // Ensure all branches have all required fields with proper types
                foreach ($this->branches as $branch) {
                    $branchId = $branch['id'];

                    // If this branch doesn't exist in the product's branch_values, initialize it
                    if (!isset($processedProduct['branch_values'][$branchId]) || !is_array($processedProduct['branch_values'][$branchId])) {
                        $processedProduct['branch_values'][$branchId] = [
                            'cost' => '0',
                            'sell_price' => '0',
                            'wholesale_price' => '0',
                            'quantity' => '0',
                        ];
                    } else {
                        // Ensure all required fields exist and are strings
                        foreach (['cost', 'sell_price', 'wholesale_price', 'quantity'] as $field) {
                            // If field doesn't exist, initialize it to '0'
                            if (!isset($processedProduct['branch_values'][$branchId][$field])) {
                                $processedProduct['branch_values'][$branchId][$field] = '0';
                            } else {
                                // Convert to string for Livewire wire:model compatibility
                                // This is crucial for fields like cost that might be numeric but need to be strings
                                $processedProduct['branch_values'][$branchId][$field] = (string)$processedProduct['branch_values'][$branchId][$field];
                            }
                        }
                    }
                }
            }

            // Make sure variant_id and variant_option_id are properly formatted arrays
            foreach (['variant_id', 'variant_option_id'] as $key) {
                if (isset($processedProduct[$key])) {
                    if (is_string($processedProduct[$key]) && $this->isJson($processedProduct[$key])) {
                        $processedProduct[$key] = json_decode($processedProduct[$key], true);
                    } elseif (!is_array($processedProduct[$key])) {
                        $processedProduct[$key] = [$processedProduct[$key]];
                    }
                }
            }

            $this->generatedProducts[] = $processedProduct;
        }

        if (!empty($this->generatedProducts)) {
            $this->showGeneratedProducts = true;
        }

        // Extract variant IDs and options from old products
        $processedVariants = [];

        foreach ($this->oldProducts as $product) {
            if (isset($product['variant_id']) && isset($product['variant_option_id'])) {
                try {
                    // Process variant IDs (might be a JSON string or already an array)
                    $variantIds = $product['variant_id'];
                    if (is_string($variantIds)) {
                        // Try to decode as JSON
                        $decodedVariantIds = json_decode($variantIds, true);
                        // If successfully decoded and is array, use it
                        if (is_array($decodedVariantIds)) {
                            $variantIds = $decodedVariantIds;
                        } else {
                            // If not JSON, treat as comma-separated string or single value
                            $variantIds = [$variantIds];
                        }
                    } elseif (!is_array($variantIds)) {
                        // If not array or string, convert to array with single value
                        $variantIds = [$variantIds];
                    }

                    // Process option IDs (might be a JSON string or already an array)
                    $optionIds = $product['variant_option_id'];
                    if (is_string($optionIds)) {
                        // Try to decode as JSON
                        $decodedOptionIds = json_decode($optionIds, true);
                        // If successfully decoded and is array, use it
                        if (is_array($decodedOptionIds)) {
                            $optionIds = $decodedOptionIds;
                        } else {
                            // If not JSON, treat as comma-separated string or single value
                            $optionIds = [$optionIds];
                        }
                    } elseif (!is_array($optionIds)) {
                        // If not array or string, convert to array with single value
                        $optionIds = [$optionIds];
                    }

                    if (!empty($variantIds) && !empty($optionIds)) {
                        // Handle simple case of one variant with multiple options
                        if (count($variantIds) === 1 && count($optionIds) === 1) {
                            $variantId = is_array($variantIds) ? $variantIds[0] : $variantIds;
                            $optionId = is_array($optionIds) ? $optionIds[0] : $optionIds;
                            // Ensure variants is an array
                            if (is_string($this->variants)) {
                                $this->variants = json_decode($this->variants, true) ?: [];
                            }

                            // Add variant if not already processed
                            if (!isset($processedVariants[$variantId])) {
                                $variantIndex = count($this->variants);
                                $this->variants[$variantIndex] = [
                                    'variant_id' => $variantId,
                                    'options' => []
                                ];
                                $processedVariants[$variantId] = $variantIndex;

                                // Load options for this variant
                                $this->loadOptionsForVariant($variantId);
                            }

                            // Get the variant index
                            $variantIndex = $processedVariants[$variantId];

                            // Add option to the variant if not already added
                            if (!in_array($optionId, $this->variants[$variantIndex]['options'])) {
                                $this->variants[$variantIndex]['options'][] = $optionId;
                            }
                        }
                        // Handle case of multiple variants/options
                        else {
                            for ($i = 0; $i < count($variantIds); $i++) {
                                $variantId = $variantIds[$i];
                                $optionId = $optionIds[$i] ?? null;

                                if (!$optionId) continue;

                                // Add variant if not already processed
                                if (!isset($processedVariants[$variantId])) {
                                    $variantIndex = count($this->variants);
                                    $this->variants[$variantIndex] = [
                                        'variant_id' => $variantId,
                                        'options' => []
                                    ];
                                    $processedVariants[$variantId] = $variantIndex;

                                    // Load options for this variant
                                    $this->loadOptionsForVariant($variantId);
                                }

                                // Get the variant index
                                $variantIndex = $processedVariants[$variantId];

                                // Add option to the variant if not already added
                                if (!in_array($optionId, $this->variants[$variantIndex]['options'])) {
                                    $this->variants[$variantIndex]['options'][] = $optionId;
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Error processing variants from old products', [
                        'error' => $e->getMessage(),
                        'product' => $product,
                    ]);
                }
            }
        }

        // Clean up variants - remove any with no options
        $this->cleanupVariants();

        // Re-generate products to ensure they're up to date
        if (!empty($this->variants)) {
            // But only if we don't already have generated products from old products
            if (empty($this->generatedProducts)) {
                $this->generateProducts();
            }
        }
    }

    /**
     * Remove variants that don't have proper IDs or options, and merge duplicates
     */
    private function cleanupVariants()
    {
        $validVariants = [];
        $mergedVariants = [];

        // Group variants by variant_id to detect duplicates
        foreach ($this->variants as $index => $variant) {
            // Skip variants without valid IDs
            if (empty($variant['variant_id'])) {
                continue;
            }

            // Check if variant exists in the database
            $variantExists = false;
            foreach ($this->availableVariants as $availableVariant) {
                if ($availableVariant->id == $variant['variant_id']) {
                    $variantExists = true;
                    break;
                }
            }

            if (!$variantExists) {
                continue;
            }

            // Collect options for this variant_id
            $variantId = $variant['variant_id'];
            if (!isset($mergedVariants[$variantId])) {
                $mergedVariants[$variantId] = [
                    'variant_id' => $variantId,
                    'options' => []
                ];
            }

            // Merge in any valid options
            if (!empty($variant['options'])) {
                foreach ($variant['options'] as $optionId) {
                    // Check if option exists
                    $optionExists = false;
                    if (isset($this->availableOptions[$variantId])) {
                        foreach ($this->availableOptions[$variantId] as $option) {
                            if ($option->id == $optionId) {
                                $optionExists = true;
                                break;
                            }
                        }
                    }

                    if ($optionExists && !in_array($optionId, $mergedVariants[$variantId]['options'])) {
                        $mergedVariants[$variantId]['options'][] = $optionId;
                    }
                }
            }
        }

        // Convert merged variants back to indexed array, filtering out empty options
        foreach ($mergedVariants as $variantId => $variant) {
            if (!empty($variant['options'])) {
                $validVariants[] = $variant;
            }
        }

        // Replace variants with validated and merged ones
        $this->variants = $validVariants;

        // Regenerate products to match the updated variants
        if (!empty($this->variants)) {
            $this->generateProducts();
        }
    }

    public function loadAvailableVariants()
    {
        $this->availableVariants = MerchantVariant::all();
    }

    public function loadOptionsForVariant($variantId)
    {
        // Load options from database
        $this->availableOptions[$variantId] = MerchantVariantOption::where('variant_id', $variantId)->get();

        // Return the options so JavaScript can use them
        return $this->availableOptions[$variantId];
    }

    public function addNewVariant($currentIndex = null)
    {
        if (empty(trim($this->newVariantName))) {
            return null;
        }

        // Check if a variant with this name already exists
        $existingVariant = MerchantVariant::where('name', $this->newVariantName)->first();
        if ($existingVariant) {
            // Return the existing variant ID
            $this->loadAvailableVariants();

            // If we have a current index, use it; otherwise add a new variant row
            $variantIndex = $currentIndex !== null ? $currentIndex : count($this->variants);

            if ($currentIndex === null) {
                // Add a new variant row if no current index was provided
                $this->addVariant();
                $variantIndex = count($this->variants) - 1;
            }

            // Set the variant ID for the current or new row
            $this->variants[$variantIndex]['variant_id'] = $existingVariant->id;

            // Load options for this variant
            $this->loadOptionsForVariant($existingVariant->id);

            // Call variantSelected to ensure proper initialization
            $this->variantSelected($variantIndex, $existingVariant->id);

            // Force a refresh of the component
            $this->dispatch('variantAdded', [
                'variantId' => $existingVariant->id,
                'variantIndex' => $variantIndex
            ]);

            return $existingVariant->id;
        }

        // Create a new variant
        $variant = MerchantVariant::create([
            'name' => $this->newVariantName
        ]);

        $this->newVariantName = '';
        $this->loadAvailableVariants();

        // If we have a current index, use it; otherwise add a new variant row
        $variantIndex = $currentIndex !== null ? $currentIndex : count($this->variants);

        if ($currentIndex === null) {
            // Add a new variant row if no current index was provided
            $this->addVariant();
            $variantIndex = count($this->variants) - 1;
        }

        // Set the variant ID for the current or new row
        $this->variants[$variantIndex]['variant_id'] = $variant->id;

        // Load options for this variant
        $this->loadOptionsForVariant($variant->id);

        // Call variantSelected to ensure proper initialization
        $this->variantSelected($variantIndex, $variant->id);

        // Force a refresh of the component
        $this->dispatch('variantAdded', [
            'variantId' => $variant->id,
            'variantIndex' => $variantIndex
        ]);

        $this->dispatch('show-toast', [
            'type' => 'success',
            'message' => __('messages.variant_created_successfully')
        ]);

        return $variant->id;
    }

    public function addNewOption($variantIndex)
    {
        // Now using variant index instead of variant ID
        if (empty(trim($this->newOptionName))) {
            return null;
        }

        // Validate the variant index
        if (!isset($this->variants[$variantIndex]) || !isset($this->variants[$variantIndex]['variant_id'])) {
            return null; // Invalid variant index or no variant ID
        }

        // Get the variant ID from the backend
        $variantId = $this->variants[$variantIndex]['variant_id'];

        // Check if an option with this name already exists for this variant
        $existingOption = MerchantVariantOption::where('variant_id', $variantId)
            ->where('name', $this->newOptionName)
            ->first();

        if ($existingOption) {
            // Return the existing option ID
            $this->loadOptionsForVariant($variantId);

            // Add the option to the variant
            if (!isset($this->variants[$variantIndex]['options'])) {
                $this->variants[$variantIndex]['options'] = [];
            }

            if (!in_array($existingOption->id, $this->variants[$variantIndex]['options'])) {
                $this->variants[$variantIndex]['options'][] = $existingOption->id;
            }

            // Force a refresh to show the existing option
            $this->dispatch('optionAdded', [
                'optionId' => $existingOption->id,
                'variantIndex' => $variantIndex
            ]);

            return $existingOption->id;
        }

        // Create a new option
        $option = MerchantVariantOption::create([
            'variant_id' => $variantId,
            'name' => $this->newOptionName
        ]);

        $this->newOptionName = '';
        $this->loadOptionsForVariant($variantId);

        // Add the option to the variant
        if (!isset($this->variants[$variantIndex]['options'])) {
            $this->variants[$variantIndex]['options'] = [];
        }

        $this->variants[$variantIndex]['options'][] = $option->id;

        // Force a refresh to show the new option
        $this->dispatch('optionAdded', [
            'optionId' => $option->id,
            'variantIndex' => $variantIndex
        ]);

        $this->dispatch('show-toast', [
            'type' => 'success',
            'message' => __('messages.option_created_successfully')
        ]);

        // Generate products with the new option
        $this->generateProducts();

        return $option->id;
    }

    public function addVariant()
    {
        $newVariant = [
            'variant_id' => null,
            'name' => '',
            'options' => [],
        ];
        $this->variants[] = $newVariant;
    }

    public function isVariantSelected($variantId, $currentIndex)
    {
        foreach ($this->variants as $index => $variant) {
            if ($index !== $currentIndex && isset($variant['variant_id']) && $variant['variant_id'] == $variantId) {
                return true;
            }
        }
        return false;
    }

    public function isOptionSelected($variantIndex, $optionId)
    {
        if (isset($this->variants[$variantIndex]['options'])) {
            // Convert optionId to string for comparison
            $optionIdStr = (string)$optionId;

            foreach ($this->variants[$variantIndex]['options'] as $selectedOptionId) {
                // Convert each selected option ID to string for comparison
                if ((string)$selectedOptionId === $optionIdStr) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Get the list of selected options for a variant at the given index
     * This method is used by the JavaScript to refresh the option dropdown
     *
     * @param int $variantIndex
     * @return array
     */
    public function getSelectedOptions($variantIndex)
    {
        if (isset($this->variants[$variantIndex]['options'])) {
            return $this->variants[$variantIndex]['options'];
        }
        return [];
    }

    public function variantSelected($index, $variantId)
    {
        if (empty($variantId)) {
            $this->variants[$index] = [
                'variant_id' => null,
                'options' => [],
                'selected_option' => null
            ];
            $this->generateProducts();
            return;
        }

        // Reset options when variant changes
        $this->variants[$index] = [
            'variant_id' => $variantId,
            'options' => [],
            'selected_option' => null
        ];

        // Load options for this variant and make them available to the frontend
        $options = $this->loadOptionsForVariant($variantId);
        $this->generateProducts();

        // Return the options for JavaScript
        return $options;
    }

    public function optionSelected($variantIndex, $optionId)
    {
        if (empty($optionId)) {
            return;
        }

        if (!isset($this->variants[$variantIndex]['options'])) {
            $this->variants[$variantIndex]['options'] = [];
        }

        if (!in_array($optionId, $this->variants[$variantIndex]['options'])) {
            $this->variants[$variantIndex]['options'][] = $optionId;
            $this->variants[$variantIndex]['selected_option'] = null; // Reset selection
            $this->generateProducts();
            $this->dispatch('option-added', ['variantIndex' => $variantIndex]);
        }
    }

    public function removeVariant($index)
    {
        unset($this->variants[$index]);
        $this->variants = array_values($this->variants);

        // Clear and regenerate products list after removing variant
        $this->generateProducts();
    }

    public function addOption($variantIndex)
    {
        $this->variants[$variantIndex]['options'][] = '';
        // Don't generate products yet, wait for option value to be entered
    }

    public function removeOption($variantIndex, $optionId)
    {

        $options = $this->variants[$variantIndex]['options'];
        $this->variants[$variantIndex]['options'] = array_values(array_filter($options, function($value) use ($optionId) {
            return $value != $optionId;
        }));

        // Get the variant ID for refreshing the options dropdown
        $variantId = $this->variants[$variantIndex]['variant_id'] ?? null;

        // Always regenerate products after removing an option
        $this->generateProducts();

        // Dispatch events for frontend
        $this->dispatch('option-removed', [
            'variantIndex' => $variantIndex,
            'optionId' => $optionId,
            'variantId' => $variantId
        ]);
    }

    /**
     * Unselect all previously selected options for a variant
     *
     * @param int $variantIndex
     */
    public function unselectAllOptions($variantIndex)
    {
        if (isset($this->variants[$variantIndex])) {
            // Get the variant ID for refreshing the options dropdown
            $variantId = $this->variants[$variantIndex]['variant_id'] ?? null;

            // Clear options
            $this->variants[$variantIndex]['options'] = [];
            $this->variants[$variantIndex]['selected_option'] = null;

            // Regenerate products
            $this->generateProducts();

            // Dispatch event with variant ID
            $this->dispatch('options-unselected', [
                'variantIndex' => $variantIndex,
                'variantId' => $variantId
            ]);
        }
    }

    public function updatedVariants($value, $key)
    {
        // Check for duplicate variant names
        if (str_contains($key, 'name')) {
            $index = explode('.', $key)[0];
            $currentName = strtolower(trim($value));
            if (!empty($currentName)) {
                foreach ($this->variants as $i => $variant) {
                    if ($i != $index && strtolower(trim($variant['name'])) === $currentName) {
                        $this->dispatch('show-toast', [
                            'type' => 'error',
                            'message' => Lang::get('messages.duplicate_variant')
                        ]);
                        $this->variants[$index]['name'] = '';
                        return;
                    }
                }
            }
        }

        // Check for duplicate options
        if (str_contains($key, 'options')) {
            [$variantIndex, , $optionIndex] = explode('.', $key);
            $currentOption = strtolower(trim($value));
            if (!empty($currentOption)) {
                foreach ($this->variants[$variantIndex]['options'] as $i => $option) {
                    if ($i != $optionIndex && strtolower(trim($option)) === $currentOption) {
                        $this->dispatch('show-toast', [
                            'type' => 'error',
                            'message' => Lang::get('messages.duplicate_option')
                        ]);
                        $this->variants[$variantIndex]['options'][$optionIndex] = '';
                        return;
                    }
                }

                // When a new option value is entered, generate products
                $this->generateProducts();
            }
        }
    }


    private function updateProductNames()
    {
        // First, sort variants and their options
        $sortedVariants = $this->getVariantOptions();

        // Now update product names based on the current options
        foreach ($this->generatedProducts as &$product) {
            // Skip products without combination keys
            if (!isset($product['combination_keys'])) {
                continue;
            }

            $combinationKeys = $product['combination_keys'];
            $displayParts = [];

            foreach ($combinationKeys as $variantIndex => $optionIndex) {
                if (isset($this->variants[$variantIndex]) &&
                    isset($this->variants[$variantIndex]['options'][$optionIndex])) {
                    $displayParts[] = $this->variants[$variantIndex]['options'][$optionIndex];
                }
            }

            $displayText = implode(' - ', $displayParts);
            $product['variant_part'] = $displayText;
            $product['name'] = $this->productName . ' - ' . $displayText;
        }
    }

    private function getVariantOptions()
    {
        // Return array of non-empty variant options
        $result = [];
        if(isset($this->variants) && !empty($this->variants)){
            foreach ($this->variants as $variantIndex => $variant) {
                $options = [];
                if(isset($variant['options']) && !empty($variant['options'])){
                    foreach ($variant['options'] as $optionIndex => $option) {
                        if (!empty(trim($option))) {
                            $options[$optionIndex] = trim($option);
                        }
                    }
                }
                if (!empty($options)) {
                    $result[$variantIndex] = $options;
                }
            }
        }
        return $result;
    }

    public function updatedSelectedBranch($branchId)
    {
        $this->selectedBranch = $branchId;
        // Ensure branch values exist for all products
        foreach ($this->generatedProducts as &$product) {
            if (!isset($product['branch_values'])) {
                $product['branch_values'] = [];
            }
            if (!isset($product['branch_values'][$branchId])) {
                $product['branch_values'][$branchId] = [
                    'cost' => '0',
                    'sell_price' => '0',
                    'wholesale_price' => '0',
                    'quantity' => '0',
                ];
            }
        }
    }

    public function generateProducts()
    {
        // Save existing product data keyed by variant_id and option_id combinations
        $existingProductData = [];
        foreach ($this->generatedProducts as $product) {
            if (isset($product['variant_id']) && isset($product['variant_option_id']) && isset($product['branch_values'])) {
                // Create a unique key from the variant and option IDs
                $variantIds = is_array($product['variant_id']) ? $product['variant_id'] : [$product['variant_id']];
                $optionIds = is_array($product['variant_option_id']) ? $product['variant_option_id'] : [$product['variant_option_id']];

                $combinationKey = $this->createCombinationKey($variantIds, $optionIds);

                $existingProductData[$combinationKey] = [
                    'barcode' => $product['barcode'] ?? null,
                    'cost' => $product['cost'] ?? '0',
                    'branch_values' => $product['branch_values'] ?? []
                ];
            }
        }

        // Clear existing products
        $this->generatedProducts = [];

        // Prepare arrays for combinations
        $variantArrays = [];
        foreach ($this->variants as $variantIndex => $variant) {
            if (!isset($variant['variant_id']) || empty($variant['options'])) {
                continue;
            }
            $variantArrays[$variant['variant_id']] = $variant['options'];
        }

        $combinations = $this->generateCombinations($variantArrays, 0);

        foreach ($combinations as $combination) {
            $variantNames = [];
            $variantIds = [];
            $optionIds = [];

            foreach ($combination as $variantId => $optionId) {
                $variant = $this->variants[array_search($variantId, array_column($this->variants, 'variant_id'))];
                $option = $this->availableOptions[$variantId]->firstWhere('id', $optionId);

                if ($variant && $option) {
                    // Make sure option name is a string
                    $optionName = '';
                    if (isset($option->name)) {
                        $optionName = is_string($option->name) ? $option->name : (string)$option->name;
                    }
                    $variantNames[] = $optionName;
                    $variantIds[] = $variantId;
                    $optionIds[] = $optionId;
                }
            }

            // Make sure all variant names are strings
            $variantNames = array_map(function($name) {
                return is_string($name) ? $name : (string)$name;
            }, $variantNames);

            // Double-check all names are valid strings
            foreach ($variantNames as $i => $name) {
                $variantNames[$i] = (string)$name;
            }

            $variantString = implode(' - ', $variantNames);
            $productName = '';

            // Make sure this->productName is a string even if it's still an array somehow
            $safeProductName = $this->productName;
            if (is_array($safeProductName)) {
                $safeProductName = implode(' ', array_map('strval', $safeProductName));
            } else {
                $safeProductName = (string)$safeProductName;
            }

            if ($safeProductName) {
                $productName = $safeProductName . ' - ' . $variantString;
            } else {
                $productName = $variantString;
            }

            // Check if we have existing data for this product
            $combinationKey = $this->createCombinationKey($variantIds, $optionIds);

            $barcode = null;
            $cost = null;
            $branchValues = null;

            if (isset($existingProductData[$combinationKey])) {
                $barcode = $existingProductData[$combinationKey]['barcode'];
                $cost = $existingProductData[$combinationKey]['cost'];
                $branchValues = $existingProductData[$combinationKey]['branch_values'];
            }

            if (!$barcode) {
                $barcode = generateBarcode();
            }

            if (!$branchValues) {
                $branchValues = $this->initializeBranchValues();
            }

            // Store the variant IDs and option IDs as arrays for the new junction table approach
            $this->generatedProducts[] = [
                'name' => $productName,
                'product_name' => $productName, // Add product_name for consistency with oldProducts
                'variant_id' => $variantIds,
                'variant_option_id' => $optionIds,
                'barcode' => $barcode,
                'cost' => $cost,
                'branch_values' => $branchValues,
                'combination_key' => $combinationKey // Add combination key for synchronization
            ];
        }
        $this->showGeneratedProducts = true;
    }

    /**
     * Create a unique key for a combination of variant IDs and option IDs
     *
     * @param array $variantIds
     * @param array $optionIds
     * @return string
     */
    private function createCombinationKey($variantIds, $optionIds)
    {
        // Sort both arrays to ensure consistent ordering
        if (!is_array($variantIds)) $variantIds = [$variantIds];
        if (!is_array($optionIds)) $optionIds = [$optionIds];

        // Create pairs of variant_id:option_id
        $pairs = [];
        foreach (array_keys($variantIds) as $index) {
            if (isset($variantIds[$index]) && isset($optionIds[$index])) {
                $pairs[] = $variantIds[$index] . ':' . $optionIds[$index];
            }
        }

        // Sort the pairs for consistency
        sort($pairs);

        // Join with a pipe to create a unique key
        return implode('|', $pairs);
    }

    private function generateVariantCombinations()
    {
        $combinations = [[]];

        foreach ($this->variants as $variant) {
            if (!isset($variant['variant_id']) || empty($variant['options'])) {
                continue;
            }

            $newCombinations = [];
            foreach ($combinations as $combination) {
                foreach ($variant['options'] as $optionId) {
                    $newCombination = $combination;
                    $newCombination[$variant['variant_id']] = $optionId;
                    $newCombinations[] = $newCombination;
                }
            }
            $combinations = $newCombinations;
        }

        return $combinations;
    }

    private function initializeBranchValues()
    {
        $branchValues = [];
        foreach ($this->branches as $branch) {
            $branchValues[$branch['id']] = [
                'cost' => '0',
                'sell_price' => '0',
                'wholesale_price' => '0',
                'quantity' => '0',
            ];
        }

        return $branchValues;
    }

    public function updatedGeneratedProducts($value, $key)
    {
        if (str_contains($key, 'barcode')) {
            $this->updateBarcode($value, $key);
        } elseif (str_contains($key, 'cost')) {
            $this->updateCost($value, $key);
        }
    }

    public function updateBarcode($value, $key)
    {
        // Extract the index from the key (e.g., "0.barcode" -> "0")
        $index = explode('.', $key)[0];

        // Update the barcode for all products with the same combination key
        if (isset($this->generatedProducts[$index]['combination_key'])) {
            $combinationKey = $this->generatedProducts[$index]['combination_key'];
            foreach ($this->generatedProducts as $i => &$product) {
                if ($i != $index && isset($product['combination_key']) && $product['combination_key'] === $combinationKey) {
                    $product['barcode'] = $value;
                }
            }
        }
    }

    public function updateCost($value, $key)
    {
        // Extract the index from the key (e.g., "0.branch_values.1.cost" -> index=0)
        $index = explode('.', $key)[0];

        // Update the cost for all products with the same combination key
        if (isset($this->generatedProducts[$index]['combination_key'])) {
            $combinationKey = $this->generatedProducts[$index]['combination_key'];
            foreach ($this->generatedProducts as $i => &$product) {
                if (isset($product['combination_key']) && $product['combination_key'] === $combinationKey) {
                    // Update cost for all branches of this variant
                    if (isset($product['branch_values']) && is_array($product['branch_values'])) {
                        foreach ($product['branch_values'] as $currentBranchId => &$branchData) {
                            $branchData['cost'] = $value;
                        }
                    }
                }
            }
        }
    }

    private function generateCombinations($arrays, $i = 0)
    {
        if (!isset(array_keys($arrays)[$i])) {
            return array();
        }

        if ($i == count($arrays) - 1) {
            $result = array();
            $variantIndex = array_keys($arrays)[$i];

            foreach ($arrays[$variantIndex] as $optionValue) {
                $result[] = array($variantIndex => $optionValue);
            }

            return $result;
        }

        $variantIndex = array_keys($arrays)[$i];
        $combinations = $this->generateCombinations($arrays, $i + 1);
        $result = array();

        foreach ($arrays[$variantIndex] as $optionValue) {
            foreach ($combinations as $combination) {
                $result[] = array($variantIndex => $optionValue) + $combination;
            }
        }

        return $result;
    }

    public function copyFromFirst($index)
    {
        if (isset($this->generatedProducts[0])) {
            // dump($this->generatedProducts);
            $firstProduct = $this->generatedProducts[0];

            if (!empty($this->branches) && $this->selectedBranch) {
                // Copy branch-specific values from first product to current product
                if (isset($firstProduct['branch_values'][$this->selectedBranch])) {
                    if (!isset($this->generatedProducts[$index]['branch_values'])) {
                        $this->generatedProducts[$index]['branch_values'] = [];
                    }
                    $this->generatedProducts[$index]['branch_values'][$this->selectedBranch] = [
                        'cost' => $firstProduct['branch_values'][$this->selectedBranch]['cost'],
                        'sell_price' => $firstProduct['branch_values'][$this->selectedBranch]['sell_price'],
                        'wholesale_price' => $firstProduct['branch_values'][$this->selectedBranch]['wholesale_price'],
                        'quantity' => $firstProduct['branch_values'][$this->selectedBranch]['quantity'],
                    ];
                }
            }
        }
    }

    public function toggleView()
    {
        $this->showAllBranches = !$this->showAllBranches;

        // Save preference to cookie for 30 days
       cookie()->queue('variant_product_display_mode', $this->showAllBranches, 60 * 24 * 30);
    }

    public function refreshVariants($params = [])
    {
        $this->loadAvailableVariants();

        // If a variantId was provided, select it in the next render cycle
        if (is_array($params) && isset($params['variantId'])) {
            // Force a re-render of the component
            $this->dispatch('selectNewVariant', ['variantId' => $params['variantId']]);
        }
    }

    public function refreshOptions($params = [])
    {
        // If a variantId was provided, reload the options for that variant
        if (is_array($params) && isset($params['variantId'])) {
            $this->loadOptionsForVariant($params['variantId']);
        }

        // If a variantId and optionId were provided, select it in the next render cycle
        if (is_array($params) && isset($params['variantId']) && isset($params['optionId'])) {
            // Find the variant index
            $variantIndex = $this->getVariantIndexById($params['variantId']);

            if ($variantIndex !== null) {
                $this->dispatch('selectNewOption', [
                    'variantIndex' => $variantIndex,
                    'optionId' => $params['optionId']
                ]);
            } else {
                // Fallback to old method for backward compatibility
                $this->dispatch('selectNewOption', [
                    'variantId' => $params['variantId'],
                    'optionId' => $params['optionId']
                ]);
            }
        }
    }

    /**
     * Get the variant index by variant ID
     *
     * @param int $variantId
     * @return int|null
     */
    public function getVariantIndexById($variantId)
    {
        foreach ($this->variants as $index => $variant) {
            if (isset($variant['variant_id']) && $variant['variant_id'] == $variantId) {
                return $index;
            }
        }

        return null;
    }

    public function selectVariantById($params)
    {
        $variantId = $params['variantId'];
        $variantIndex = $params['variantIndex'] ?? 0;

        // Make sure the variant exists in the available variants
        $variantExists = collect($this->availableVariants)->contains('id', $variantId);

        if ($variantExists) {
            // If the variant index doesn't exist, add a new variant
            if (!isset($this->variants[$variantIndex])) {
                $this->addVariant();
            }

            // Select the variant
            $this->variantSelected($variantIndex, $variantId);
        }
    }

    public function selectOptionById($params)
    {
        $optionId = $params['optionId'];
        $variantIndex = $params['variantIndex'] ?? 0;

        // Make sure the variant exists and has options loaded
        if (isset($this->variants[$variantIndex]) &&
            isset($this->variants[$variantIndex]['variant_id']) &&
            isset($this->availableOptions[$this->variants[$variantIndex]['variant_id']])) {

            // Check if the option exists in the available options
            $optionExists = collect($this->availableOptions[$this->variants[$variantIndex]['variant_id']])->contains('id', $optionId);

            if ($optionExists) {
                // Select the option
                $this->optionSelected($variantIndex, $optionId);
            }
        }
    }

    public function productNameUpdated($name)
    {
        // Ensure we handle both strings and arrays
        if (is_array($name)) {
            // If it's an array, try to convert it to a string in a safe way
            $this->productName = implode(' ', array_map('strval', $name));
        } else {
            // Otherwise, just ensure it's a string
            $this->productName = (string)$name;
        }

        $this->generateProducts();
    }

    public function render()
    {
        return view('livewire.variant-product-list');
    }

    // Helper function to check if a string is valid JSON
    private function isJson($string) {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Remove all variants and reset the product list
     */
    public function removeAllVariants()
    {
        $this->variants = [];
        $this->generatedProducts = [];
        $this->showGeneratedProducts = false;
    }
}
