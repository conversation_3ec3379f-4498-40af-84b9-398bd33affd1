<?php

namespace App\Livewire;

use App\Models\MerchantsProduct;
use App\Models\MerchantTag;
use App\Services\BusinessContextService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use App\Enums\Application\ProductTypeEnum;

class Products extends Component
{
    use WithPagination;
    public $status;
    public $search;
    public $categoryId;
    public $selectedTag;
    public $selectedCategory;
    public $selectedType;
    protected $listeners = ['updatedStatus', 'updatedTag', 'updatedCategory', 'updatedType'];
    protected $queryString = [
        'search',
        'status',
        'selectedTag',
        'selectedCategory',
        'selectedType',
    ];

    public function mount($categoryId = null){
        if(!is_null($categoryId)){
            $this->categoryId = $categoryId;
        }
    }

    public function render()
    {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $businessId = BusinessContextService::getBusinessId();

        // Build the base query for all scenarios using subqueries instead of joins
        $merchantProducts = MerchantsProduct::select(['*'])
            ->selectRaw('
                (SELECT SUM(quantity) FROM merchant_product_branch_quantities
                 WHERE merchant_id = ?
                 AND product_id = merchants_products.id
                 ' . ($businessId ? 'AND business_id = ?' : '') .
                 ($main_account ? '' : ' AND branch_id IN (' . implode(',', $user->branches->pluck('branch_id')->toArray() ?: [0]) . ')') .
                 ') AS totalQuantities,
                (SELECT MAX(sell_price) FROM merchant_product_branch_quantities
                 WHERE merchant_id = ?
                 AND product_id = merchants_products.id
                 ' . ($businessId ? 'AND business_id = ?' : '') .
                 ($main_account ? '' : ' AND branch_id IN (' . implode(',', $user->branches->pluck('branch_id')->toArray() ?: [0]) . ')') .
                 ') AS max_price,
                (SELECT MIN(sell_price) FROM merchant_product_branch_quantities
                 WHERE merchant_id = ?
                 AND product_id = merchants_products.id
                 ' . ($businessId ? 'AND business_id = ?' : '') .
                 ($main_account ? '' : ' AND branch_id IN (' . implode(',', $user->branches->pluck('branch_id')->toArray() ?: [0]) . ')') .
                 ') AS min_price
            ', $businessId
               ? array_merge([$user->merchant_id, $businessId], [$user->merchant_id, $businessId], [$user->merchant_id, $businessId])
               : [$user->merchant_id, $user->merchant_id, $user->merchant_id])
            ->when($this->categoryId, function($query) {
                return $query->where('category_id', $this->categoryId);
            })
            ->orderBy('created_at', 'desc');

        // Apply search if provided
        if (!empty($this->search)) {
            $merchantProducts->where(function($query) use ($user) {
                $query->where('name', 'LIKE', '%' . $this->search . '%')
                      ->orWhere('barcode', 'LIKE', '%' . $this->search . '%');
            });
        }

        // Apply status filter if provided
        if (!empty($this->status) || $this->status != '') {
            $merchantProducts->where('status', $this->status);
        }

        // Apply tag filter if provided
        if (!empty($this->selectedTag) && $this->selectedTag !== null) {
            $merchantProducts->whereHas('tags', function($query) {
                $query->where('tag_id', $this->selectedTag);
            });
        }

        // Apply category filter if provided (and not already filtered by categoryId from route)
        if (!empty($this->selectedCategory) && $this->selectedCategory !== null && empty($this->categoryId)) {
            $merchantProducts->where('category_id', $this->selectedCategory);
        }

        // Apply product type filter if provided
        if (!empty($this->selectedType) && $this->selectedType !== null) {
            $merchantProducts->where('type', $this->selectedType);
        }

        $merchantProducts = $merchantProducts->paginate(10);

        // Load composite components for composite products
        $merchantProducts->each(function ($product) use ($businessId) {
            if ($product->type === ProductTypeEnum::COMPOSITE->value) {
                // Load components with business filtering
                if ($businessId) {
                    $product->load(['compositeComponents.componentProduct' => function($query) use ($businessId) {
                        $query->where('business_id', $businessId);
                    }, 'compositeComponents.componentProduct.branchQuantities' => function($query) use ($businessId) {
                        $query->where('business_id', $businessId);
                    }]);
                } else {
                    $product->load(['compositeComponents.componentProduct.branchQuantities']);
                }

                // Calculate max possible quantity based on component availability
                $maxQuantities = [];
                foreach ($product->compositeComponents as $component) {
                    if ($component->componentProduct && $component->componentProduct->type !== ProductTypeEnum::SERVICE->value) {
                        $componentProduct = $component->componentProduct;
                        $componentQuantity = $componentProduct->branchQuantities->sum('quantity');

                        if ($componentProduct->type === ProductTypeEnum::WEIGHTED->value) {
                            if ($component->quantity > 0) {
                                $maxQuantities[] = floor($componentQuantity / $component->quantity);
                            } else {
                                $maxQuantities[] = 0;
                            }
                        } else {
                            if ($component->quantity > 0) {
                                $maxQuantities[] = floor($componentQuantity / $component->quantity);
                            } else {
                                $maxQuantities[] = 0;
                            }
                        }
                    }
                }
                $product->totalQuantities = empty($maxQuantities) ? 0 : min($maxQuantities);
            }
        });

        // Get all tags for the filter dropdown
        $tags = MerchantTag::query()->get();

        // Get all categories for the filter dropdown
        $categories = \App\Models\MerchantCategory::query()->get();

        // Get all product types for the filter dropdown
        $productTypes = [
            ProductTypeEnum::SIMPLE->value => __('messages.simple'),
            ProductTypeEnum::WEIGHTED->value => __('messages.weighted'),
            ProductTypeEnum::SERVICE->value => __('messages.service'),
            ProductTypeEnum::COMPOSITE->value => __('messages.composite'),
            ProductTypeEnum::VARIANT->value => __('messages.variant'),
        ];

        return view('livewire.products', [
            'products' => $merchantProducts,
            'tags' => $tags,
            'categories' => $categories,
            'productTypes' => $productTypes
        ]);
    }

    public function searchUpdated(){
        $this->resetPage();
    }

    public function updatedStatus($value): void{
        $this->status = $value;
        $this->resetPage();
    }

    public function updatedSelectedTag($value): void{
        // Handle empty values properly
        $this->selectedTag = empty($value) ? null : $value;
        $this->resetPage();
    }

    public function updatedSelectedCategory($value): void{
        // Handle empty values properly
        $this->selectedCategory = empty($value) ? null : $value;
        $this->resetPage();
    }

    public function updatedTag($value): void{
        // Handle empty values properly
        $this->selectedTag = empty($value) ? null : $value;
        $this->resetPage();
    }

    public function updatedCategory($value): void{
        // Handle empty values properly
        $this->selectedCategory = empty($value) ? null : $value;
        $this->resetPage();
    }

    public function updatedType($value): void{
        // Handle empty values properly
        $this->selectedType = empty($value) ? null : $value;
        $this->resetPage();
    }
}
