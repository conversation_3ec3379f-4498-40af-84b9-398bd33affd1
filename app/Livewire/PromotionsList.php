<?php

namespace App\Livewire;

use App\Models\MerchantPromotion;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Url;

class PromotionsList extends Component
{
    use WithPagination;

    #[Url(history: true)]
    public $search = '';

    public $perPage = 10;

    public function mount()
    {
        // Get search from URL if exists
        $this->search = request()->query('search', '');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        $user = getCurrentUser();
        $query = MerchantPromotion::query()
            ->orderBy('created_at', 'desc');

        if (!$user->main_account) {
            $permittedBranches = $user->branches->pluck('branch_id');
            $query->whereHas('branches', function($q) use ($permittedBranches) {
                $q->whereIn('merchant_branches.id', $permittedBranches);
            });
        }

        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('type', 'like', '%' . $this->search . '%')
                  ->orWhere('status', 'like', '%' . $this->search . '%');
            });
        }

        $promotions = $query->with(['branches', 'products.product', 'categories', 'tags'])
            ->paginate(10);

        return view('livewire.promotions-list', [
            'promotions' => $promotions
        ]);
    }

    public function delete($id)
    {
        $promotion = MerchantPromotion::findOrFail($id);
        $promotion->delete();
        session()->flash('success', __('messages.deleted_successfully'));
    }
}
