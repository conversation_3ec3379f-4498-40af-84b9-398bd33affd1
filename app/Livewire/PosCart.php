<?php

namespace App\Livewire;

use App\Actions\Application\Web\ZatcaInvoices\ZatcaInvoiceAction;
use App\Enums\Application\InvoiceTypeEnum;
use App\Enums\Application\InvoiceTypePerformedEnum;
use App\Enums\Application\ProductTypeEnum;
use App\Enums\Application\PromotionDiscountTypeEnum;
use App\Enums\Application\PromotionTypeEnum;
use App\Enums\Application\Zatca\ZatcaInvoiceName;
use App\Events\POSCartUpdateEvent;
use App\Helpers\Helper;
use App\Models\ActiveCustomerDisplay;
use App\Models\MerchantBranch;
use App\Models\MerchantInvoice;
use App\Models\MerchantPaymentMethod;
use App\Models\MerchantPaymentMethodRegister;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantRegisterSetting;
use App\Models\MerchantSaleInvoiceBeforeRounding;
use App\Models\MerchantSaleInvoiceDiscount;
use App\Models\MerchantSaleInvoicePaymentMethod;
use App\Models\MerchantSaleInvoiceProductsBeforePromotion;
use App\Models\MerchantSaleInvoicePromotionGroup;
use App\Models\MerchantsSetting;
use App\Models\MerchantTempInvoice;
use App\Models\Address;
use BCMathExtended\BC;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;
use Livewire\Component;
use Salla\ZATCA\GenerateQrCode;
use Salla\ZATCA\Tags\InvoiceDate;
use Salla\ZATCA\Tags\InvoiceTaxAmount;
use Salla\ZATCA\Tags\InvoiceTotalAmount;
use Salla\ZATCA\Tags\Seller;
use Salla\ZATCA\Tags\TaxNumber;
use App\Models\MerchantsProduct;


class PosCart extends Component{

    protected $listeners = ["CartAdded",'setScreenSetting',"setTaxRate",'percentageSliderUpdated',"IncreaseQuantity","DecreaseQuantity","calculatingNumbers","clear",'CustomerAdded','wholesaleStatus','PauseOrder','discountInputChanged','updateInvoiceNotes'];
    public $products;
    public $cartProductSorted = [];
    public $paymentMethods;
    public $showSideBarProducts;
    public $subtotal = 0;
    public $tax = 0;
    public $total = 0;
    protected $cat;
    public $chosenPaymentMethod = [];
    public $paymentMethod;
    public $invoiceId = 0;
    public $cost = 0;
    public $cost_tax_exclusive = 0;
    public $branchId;
    public $registeryId;
    public $customer = null;
    public $wholesale = 0;
    public $pausedOrders;
    public $pausedClicked = false;
    public $loadingPausedOrders = false;
    public $multipay = false;
    public $multipayArray = [];
    public $multipayAmounts = [];
    public $totalMultiPayAmounts = 0;
    public $discountInput =0;
    public $discountValue =[];
    public $discountToggle = 0;
    public $taxRate = null;
    public $totalAfterDiscountRounded = null;
    public $totalTaxAfterDiscount = null;
    public $taxOfDiscount = null;
    public $screen = null;
    public $user;
    public $plan;
    public $showNeedsSubscription = false;
    public $permissions;
    public $main_account;
    public $roundingApplied = 0;
    public $valuesBeforeRounding = [];
    public $discountPercentage;
    public $currency;
    public $inputValue;
    public $invoiceNotes;
    public $hasUnfulfilledProducts = false;
    public $appliedPromotions = [];
    public $promotionGroups = [];


    public function render()
    {
        // Sort products by rank if available
        $this->cartProductSorted = collect($this->products ?? [])->sortBy(function ($product) {
            return $product['rank'] ?? 0;
        })->toArray();

        $this->dispatch('contentChanged');
        return view('livewire.pos-cart');
    }

    public function mount($branchId = null,$registeryId = null){
        $this->user = getCurrentUser();
        $this->plan = $this->user->plan;
        $this->permissions = $this->user->group->permissions;
        $this->main_account = $this->user->main_account;

        $permittedPaymentMethods = MerchantPaymentMethodRegister::query()
            ->where('register_id', $registeryId)
            ->pluck('payment_id');

        $this->paymentMethods = MerchantPaymentMethod::query()
            ->where(function ($query) use ($permittedPaymentMethods){
               $query
                   ->whereIn('id',$permittedPaymentMethods)
                   ->orWhere('is_postpay', true);
            })
            ->get();

        $this->chosenPaymentMethod['id'] = $this->paymentMethods[0]['id'];
        $this->chosenPaymentMethod['is_cash'] = $this->paymentMethods[0]['is_cash']?1:0;
        $this->chosenPaymentMethod['is_postpay'] = $this->paymentMethods[0]['is_postpay']?1:0;
        $this->chosenPaymentMethod['is_rounding_activiated'] = $this->paymentMethods[0]['is_rounding_activiated']?1:0;

        if($branchId != null and $registeryId != null){
            $this->branchId = $branchId;
            $this->registeryId = $registeryId;
        }

        $setting = MerchantRegisterSetting::query()
            ->where('register_id', $registeryId)
            ->first();

        $this->screen = $setting?->screen;

        // Get register settings with currency relationship
        $branch = MerchantBranch::with('currency')
            ->where('id', $branchId)
            ->first();


        // Set the currency from register settings
        $this->currency = $branch?->currency;

        // Get invoice notes from merchant settings
        $settings = MerchantsSetting::query()->first();
        $this->invoiceNotes = $settings?->invoice_notes;
    }

    public function updateInvoiceNotes($notes)
    {
        $this->invoiceNotes = $notes;
    }

    public function CartAdded ($cart, $appliedPromotions = [], $promotionGroups = []){
        $this->products = $cart;
        $this->checkForUnfulfilledProducts();
        $this->updateTaxRateBasedOnProducts();
        $this->appliedPromotions = $appliedPromotions;
        $this->promotionGroups = $promotionGroups;

        // Update cartProductSorted immediately to reflect changes
        $this->cartProductSorted = collect($this->products ?? [])->sortBy(function ($product) {
            return $product['rank'] ?? 0;
        })->toArray();
    }

    /**
     * Check if there are any unfulfilled products in the cart
     *
     * @return void
     */
    private function checkForUnfulfilledProducts()
    {
        $this->hasUnfulfilledProducts = false;

        if (!$this->products) {
            return;
        }

        foreach ($this->products as $product) {
            // Skip products that don't need fulfillment (already available)
            if (isset($product['fulfillment_status']) && $product['fulfillment_status'] === 'Available') {
                continue;
            }

            // Check if the product has fulfillment arranged
            $hasFulfillment = isset($product['has_fulfillment']) && $product['has_fulfillment'] === true;
            $hasBranchFulfillment = isset($product['fulfillment_from_branch']);
            $hasDeliveryFulfillment = isset($product['fulfillment_by_delivery']);

            // If the product is not available and has no fulfillment arranged, mark as unfulfilled
            if (!$hasFulfillment && !$hasBranchFulfillment && !$hasDeliveryFulfillment) {
                $this->hasUnfulfilledProducts = true;
                break;
            }
        }
    }

    public function calculatingNumbers(){
        $roundingPoints = 2;
        $subtotal = [];
        $tax = [];
        $cost = [];
        $cost_tax_exclusive = [];

        // Calculate for each product in cart
        if (!empty($this->products)) {
            foreach ($this->products as $item) {
                // Convert array to collection
                $product = collect($item);

                $quantity = $product->get('cartQuantity');
                $costField = $product->get('cost');
                $costExclusiveField = ($this->discountToggle == 1)
                    ? $product->get('cost_tax_exclusive')
                    : $product->get('cost_tax_exclusive_rounded');

                if ($product->get('type') === ProductTypeEnum::WEIGHTED->value) {
                    $roundingPoints = getRoundingPoints($product->get('type'));
                }

                // Determine price fields based on whether we're using discount or not
                $priceField = $this->wholesale
                    ? $product->get('wholesale_price')
                    : $product->get('sell_price');

                $priceTaxExclusiveField = ($this->discountToggle == 1)
                    ? ($this->wholesale ? $product->get('wholesale_price_tax_exclusive') : $product->get('sell_price_tax_exclusive'))
                    : ($this->wholesale ? $product->get('wholesale_price_tax_exclusive_rounded') : $product->get('sell_price_tax_exclusive_rounded'));

                // Convert to base unit for weighted products
                if ($product->get('type') === ProductTypeEnum::WEIGHTED->value) {
                    $priceField = Helper::setCostOrPriceInBaseUnit($priceField);
                    $priceTaxExclusiveField = Helper::setCostOrPriceInBaseUnit($priceTaxExclusiveField);
                    $costField = Helper::setCostOrPriceInBaseUnit($costField);
                    $costExclusiveField = Helper::setCostOrPriceInBaseUnit($costExclusiveField);
                    $quantity = Helper::setQuantityInBaseUnit($quantity);
                }

                $freeQuantity = 0;
                if (isset($product['has_promotion']) && $product['has_promotion'] &&
                    isset($product['promotion_type']) && $product['promotion_type'] === PromotionTypeEnum::BUY_X_GET_Y->value &&
                    isset($product['promotion_get_y_type']) && $product['promotion_get_y_type'] === PromotionDiscountTypeEnum::FREE->value &&
                    isset($product['promotion_free_quantity'])) {
                    $freeQuantity = $product['promotion_free_quantity'];
                }

                // Calculate paid quantity (total quantity minus free quantity)
                // For return products, preserve negative quantities; for regular products, ensure non-negative
                $isReturnProduct = isset($product['is_return']) && $product['is_return'];
                if ($isReturnProduct) {
                    // For return products, use the actual quantity (which should be negative)
                    $paidQuantity = $quantity - $freeQuantity;
                } else {
                    // For regular products, ensure quantity is not negative
                    $paidQuantity = max(0, $quantity - $freeQuantity);
                }

                // Use cart_id if available, otherwise fall back to id
                $productKey = $product["cart_id"] ?? $product["id"];

                // Calculate subtotal based on tax rate and price field
                $subtotal[$productKey] = BC::mul($priceTaxExclusiveField, $paidQuantity, 10);

                // Calculate tax
                $tax[$productKey] = isset($product->get('tax')['percentage']) && $product->get('tax')['percentage'] == 0
                    ? 0
                    : BC::sub(
                        BC::mul($priceField, $paidQuantity, 10),
                        BC::mul($priceTaxExclusiveField, $paidQuantity, 10),
                        10
                    );

                $cost[$productKey] = BC::mul($costField, $paidQuantity, 10);
                $cost_tax_exclusive[$productKey] = BC::mul($costExclusiveField, $paidQuantity, 10);
            }
        }

        // Process totals based on discount toggle
        if ($this->discountToggle == 1 && $this->discountInput >= 0 && !is_null($this->discountInput)) {
            // With discount
            $this->subtotal = isset($subtotal) ? BC::round(Helper::bcsum($subtotal), $roundingPoints) : 0;
            $this->tax = isset($tax) ? BC::round(Helper::bcsum($tax), $roundingPoints) : 0;
            $this->total = BC::round(BC::add($this->subtotal, $this->tax, 10), $roundingPoints);
            $this->cost = isset($cost) ? BC::round(Helper::bcsum($cost), $roundingPoints) : 0;
            $this->cost_tax_exclusive = isset($cost_tax_exclusive) ? BC::round(Helper::bcsum($cost_tax_exclusive), 3) : 0;

            $this->discountInputChanged();
        } else {
            // Without discount (apply rounding if needed)
            if (isset($this->chosenPaymentMethod['is_cash']) && $this->chosenPaymentMethod['is_rounding_activiated']) {
                // Apply rounding
                $this->subtotal = isset($subtotal) ? Helper::bcsum($subtotal) : 0;
                $this->tax = isset($tax) ? Helper::bcsum($tax) : 0;
                $this->total = BC::add($this->subtotal, $this->tax, 10);

                $this->valuesBeforeRounding['subtotal'] = $this->subtotal;
                $this->valuesBeforeRounding['tax'] = $this->tax;
                $this->valuesBeforeRounding['total'] = $this->total;

                $this->roundingApplied = 1;

                // Apply rounding to totals
                $this->total = BC::round($this->total, $roundingPoints);
                $this->subtotal = ($this->taxRate == 0)
                    ? 0
                    : BC::round(Helper::excludeTax($this->total, $this->taxRate), $roundingPoints);
                $this->tax = BC::sub($this->total, $this->subtotal, 10);
                $this->cost = isset($cost) ? Helper::bcsum($cost) : 0;
                $this->cost_tax_exclusive = isset($cost_tax_exclusive) ? Helper::bcsum($cost_tax_exclusive) : 0;
            } else {
                // No rounding
                $this->subtotal = isset($subtotal) ? Helper::bcsum($subtotal) : 0;
                $this->tax = isset($tax) ? Helper::bcsum($tax) : 0;
                $this->total = BC::add($this->subtotal, $this->tax, 10);
                $this->cost = isset($cost) ? Helper::bcsum($cost) : 0;
                $this->cost_tax_exclusive = isset($cost_tax_exclusive) ? Helper::bcsum($cost_tax_exclusive) : 0;
                $this->roundingApplied = 0;
                $this->valuesBeforeRounding = [];
            }
        }

        // Update cartProductSorted to reflect changes
        $this->cartProductSorted = collect($this->products ?? [])->sortBy(function ($product) {
            return $product['rank'] ?? 0;
        })->toArray();

        // Broadcast cart data to customer display
        $this->broadcastCartData();
    }

    public function IncreaseQuantity($cartId){
        $this->dispatch("ProdClicked",$cartId,"increase");

        $this->calculatingNumbers();

        // Update cartProductSorted after quantity change
        if (isset($this->products) && !empty($this->products)) {
            $this->cartProductSorted = collect($this->products)->sortBy(function ($product) {
                return $product['rank'] ?? 0;
            })->toArray();
        }
    }

    public function DecreaseQuantity($cartId){
        $this->dispatch("ProdClicked",$cartId,"decrease");
        $this->calculatingNumbers();

        // Update cartProductSorted after quantity change
        if (isset($this->products) && !empty($this->products)) {
            $this->cartProductSorted = collect($this->products)->sortBy(function ($product) {
                return $product['rank'] ?? 0;
            })->toArray();
        }
    }

    Public function clear(){
        $this->products = [];
        $this->cartProductSorted = [];
        $this->subtotal = 0;
        $this->tax = 0;
        $this->total = 0;
        $this->taxRate = 0;
        $this->appliedPromotions = [];
        $this->promotionGroups = [];

        $this->multipayArray = [];
        $this->multipayAmounts= [];
        $this->totalMultiPayAmounts = 0;
        $this->multipay = false;

        $this->chosenPaymentMethod['id'] = $this->paymentMethods[0]['id'];
        $this->chosenPaymentMethod['is_cash'] = $this->paymentMethods[0]['is_cash']?1:0;
        $this->chosenPaymentMethod['is_postpay'] = $this->paymentMethods[0]['is_postpay']?1:0;
        $this->chosenPaymentMethod['is_rounding_activiated'] = $this->paymentMethods[0]['is_rounding_activiated']?1:0;

        // Reset all discount-related properties
        $this->discountValue = ['discountValue' => 0, 'discountValueRounded' => 0];
        $this->totalTaxAfterDiscount = 0;
        $this->totalAfterDiscountRounded = 0;
        $this->discountInput = 0;
        $this->discountPercentage = [0]; // Reset the discount percentage slider value
        $this->taxOfDiscount = 0;
        $this->discountToggle = 0; // Explicitly set to 0 instead of toggling

        // Recalculate numbers with reset discount values
        $this->calculatingNumbers();

        $this->roundingApplied = 0;
        $this->valuesBeforeRounding = [];
        $this->hasUnfulfilledProducts = false;

        $this->dispatch('clearFrontEnd');
        $this->setPaymentMethod($this->paymentMethods[0]['id'],0,0);
        $this->dispatch("ProdClicked",0,"reset");
        $this->dispatch("ProdClicked2",status: "reset");
        $this->dispatch('clear2');
        $this->dispatch("customerRemove");
        $this->dispatch('searchInputFocus');

        // Clear promotion groups in PosProductsList to prevent accumulation
        $this->dispatch('clearPromotionGroups');

        // Broadcast empty cart to customer display
        $this->broadcastCartData();
    }

    public function pay(){
        $products = $this->products;

        $user = getCurrentUser();

        if(empty($products)){
            return $this->clear();
        }

        // Check if we have return products in the cart
        $hasReturnProducts = false;
        $hasSaleProducts = false;

        foreach($products as $product) {
            if (isset($product['is_return']) && $product['is_return']) {
                $hasReturnProducts = true;
            } else {
                $hasSaleProducts = true;
            }
        }

        // If we only have return products, process them
        if ($hasReturnProducts && !$hasSaleProducts) {
            return $this->processReturnProducts();
        }

        // If we have both return and sale products, show an error
        if ($hasReturnProducts && $hasSaleProducts) {
            $this->dispatch('toast', message: __('messages.cannot_mix_sale_and_return'));
            return;
        }

        // If total is negative for a sale invoice, show an error
        if ($this->total < 0 && !$hasReturnProducts) {
            return $this->clear();
        }

        $roundingPoints = 2;
        $insertProducts = [];

        // Process all products regardless of wholesale status
        foreach($products as $product){
            $unit = null;

            if ($product['type'] === ProductTypeEnum::WEIGHTED->value) {
                $roundingPoints = getRoundingPoints($product['type']);
                $unit = getWeightedProductSetting()->unit;
            }

            // For products without promotions, use regular prices
            $priceInclusive = $this->wholesale ? $product["wholesale_price"] : $product["sell_price"];
            $priceExclusive = $this->wholesale ? $product["wholesale_price_tax_exclusive"] : $product["sell_price_tax_exclusive"];
            $priceExclusiveRounded = $this->wholesale ? $product["wholesale_price_tax_exclusive_rounded"] : $product["sell_price_tax_exclusive_rounded"];
            $quantity = $product["cartQuantity"];

            // Calculate subtotal using original price and quantity
            $subtotal = BC::mul($priceExclusive, $quantity, 10);
            $subtotalRounded = BC::round($subtotal, $roundingPoints);
            $discount = 0;
            $discountRounded = 0;

            // Set price fields based on wholesale status
            // Check if this product has a promotion
            $hasPromotion = isset($product['has_promotion']) && $product['has_promotion'];

            // Apply promotion discount if applicable
            if ($hasPromotion) {

                $priceInclusive = $this->wholesale ? $product["original_wholesale_price"] : $product["original_sell_price"];

                $priceExclusive = $this->wholesale ? $product["original_wholesale_price_tax_exclusive"] : $product["original_sell_price_tax_exclusive"];

                $priceExclusiveRounded = $this->wholesale ? $product["original_wholesale_price_tax_exclusive_rounded"] : $product["original_sell_price_tax_exclusive_rounded"];

                $subtotal = BC::mul($priceExclusive, $quantity, 10);
                $subtotalRounded = BC::round($subtotal, $roundingPoints);

                // Check if this is a free product in buy X get Y promotion
                if ($product['promotion_type'] === PromotionTypeEnum::BUY_X_GET_Y->value){
                    if ($product['promotion_get_y_type'] === PromotionDiscountTypeEnum::FREE->value) {
                        $discount = $subtotal;
                        $discountRounded = BC::round($discount, $roundingPoints);
                    }

                    if ($product['promotion_get_y_type'] === PromotionDiscountTypeEnum::DISCOUNT->value &&
                        isset($product['promotion_discount_percentage'])){
                        $discountPercentage = $product['promotion_discount_percentage'];
                        $discount = BC::mul($subtotal, BC::div($discountPercentage, 100, 10), 10);
                        $discountRounded = BC::round($discount, $roundingPoints);
                    }
                }
                // Check if this is a regular discount promotion
                elseif ($product['promotion_type'] === PromotionTypeEnum::DISCOUNT->value &&
                    isset($product['promotion_discount_percentage'])) {
                    // Calculate discount amount based on original price and discount percentage
                    $discountPercentage = $product['promotion_discount_percentage'];
                    $discount = BC::mul($subtotal, BC::div($discountPercentage, 100, 10), 10);
                    $discountRounded = BC::round($discount, $roundingPoints);
                }
            }

            $subtotalAfterDiscount = BC::sub($subtotal, $discount, 10);
            $tax = BC::mul($subtotalAfterDiscount, BC::div($product['tax']['percentage'], 100, 10), 10);
            $total = BC::add($subtotalAfterDiscount, $tax, 10);

            // Calculate discount-related values if discount is applied
            $discountTaxExclusive = 0;
            $discountTax = 0;

            // Apply cart-level discount if enabled
            if ($this->discountToggle == 1 && $this->discountInput > 0) {
                // Calculate discount proportion for this product
                $discountProportion = BC::div($total, $this->total, 10);

                // Calculate discount amount for this product
                $discountAmount = BC::mul($discountProportion, $this->discountInput, 10);

                // Calculate tax-exclusive discount
                $discountTaxExclusive = Helper::excludeTax($discountAmount, $product['tax']['percentage']);

                // Calculate tax portion of discount
                $discountTax = BC::sub($discountAmount, $discountTaxExclusive, 10);
            }

            // Get promotion ID if available
            $promotionId = isset($product['promotion_id']) ? $product['promotion_id'] : null;

            // Use the actual product ID for database storage, but track cart_id for reference
            $productData = [
                "product_id" => $product["id"],
                "product_name_ar" => isset($product['is_variant']) ? $product["variant_name"] : $product["name"],
                "product_name_en" => isset($product['is_variant']) ? $product["variant_name"] : $product["name_en"],
                "barcode" => $product["barcode"] ?? '',
                "price_tax_inclusive" => $priceInclusive,
                "price_tax_exclusive" => $priceExclusive,
                "price_tax_exclusive_rounded" => $priceExclusiveRounded,
                "cost_tax_exclusive" => $product["cost_tax_exclusive_rounded"],
                "cost_tax_inclusive" => $product["cost"],
                "type" => $product["type"],
                "cart_quantity" => $quantity,
                "unit_id" => $unit?->id,
                "unit_name" => $unit?->name,
                "tax_type" => $product["tax_id"],
                "tax_code" => $product['tax']['code'],
                "tax_schema" => $product['tax_schema'],
                "created_at" => date("Y-m-d H:i:s"),
                "updated_at" => date("Y-m-d H:i:s"),
                "invoice_type" => InvoiceTypeEnum::SELL->value,
                "invoice_type_performed" => InvoiceTypePerformedEnum::SALE->value,
                'branch_id' => $this->branchId,
                'has_promotion' => $hasPromotion ? 1 : 0,
                'promotion_id' => $promotionId,
                'registery_id' => $this->registeryId,
                'customer_id' => isset($this->customer['id']) ? $this->customer['id'] : 0,
                'wholesale_status' => $this->wholesale ? 1 : 0,
                'returnable' => $product["returnable"],
                'is_variant' => $product['is_variant'] ?? false,
                'product_variant_id' => $product['variant_id'] ?? null,
                'cart_id' => $product['cart_id'] ?? $product['id'],
                'tax_percentage' => $product['tax']['percentage'],
                'subtotal' => $subtotal,
                'subtotal_rounded' => $subtotalRounded,
                'discount' => $discount,
                'discount_rounded' => $discountRounded,
                'subtotal_after_discount' => $subtotalAfterDiscount,
                'subtotal_after_discount_rounded' => BC::round($subtotalAfterDiscount, $roundingPoints),
                'vat' => $tax,
                'vat_rounded' => BC::round($tax, $roundingPoints),
                'total' => $total,
                'total_rounded' => BC::round($total, $roundingPoints),
                'row_invoice_discount' => $discountTaxExclusive,
                'row_invoice_discount_rounded' => BC::round($discountTaxExclusive, $roundingPoints),
                'row_invoice_discount_vat' => $discountTax,
                'row_invoice_discount_vat_rounded' => BC::round($discountTax, $roundingPoints),

                // Add promotion group information if present
                'promotion_group_id' => $product['promotion_group_id'] ?? null,
                'promotion_product_type' => $product['promotion_product_type'] ?? null,
                'promotion_group_number' => $product['promotion_group_number'] ?? null,
                'promotion_buy_x_quantity' => $product['promotion_buy_x_quantity'] ?? null,
                'promotion_get_y_quantity' => $product['promotion_get_y_quantity'] ?? null,
                'promotion_name' => $product['promotion_name'] ?? null,
                'promotion_get_y_type' => $product['promotion_get_y_type'] ?? null,
            ];

            $insertProducts[] = $productData;
        }

        // Prepare data for invoice creation
        $settings = MerchantsSetting::query()->first();
        $branch = MerchantBranch::find($this->branchId);
        $address = Address::where('addressable_id', $this->branchId)
            ->where('addressable_type', MerchantBranch::class)
            ->first();

        $zatca_date = date("Y-m-d\TH:i:s\Z");
        $qr_zatca = GenerateQrCode::fromArray([
            new Seller($settings?->name),
            new TaxNumber($settings?->VAT),
            new InvoiceDate($zatca_date),
            new InvoiceTotalAmount($this->total),
            new InvoiceTaxAmount($this->tax)
        ])->toBase64();

        $qr_zatca_image = GenerateQrCode::fromArray([
            new Seller($settings?->name),
            new TaxNumber($settings?->VAT),
            new InvoiceDate($zatca_date),
            new InvoiceTotalAmount($this->total),
            new InvoiceTaxAmount($this->tax)
        ])->render();

        // Determine if invoice has postpay
        $has_postpay = $this->determinePostpayStatus();

        // Prepare common invoice data
        $invoiceData = [
            "user_id" => $user->id,
            "zatca_date" => $zatca_date,
            "created_at" => date("Y-m-d H:i:s"),
            "updated_at" => date("Y-m-d H:i:s"),
            "qr_zatca" => $qr_zatca,
            "qr_zatca_image" => $qr_zatca_image,
            "pretty_id" => "S-". strtoupper(Str::random(8)),
            "branch_id" => $this->branchId,
            "registery_id" => $this->registeryId,
            "customer_id" => isset($this->customer['id']) ? $this->customer['id'] : 0,
            'wholesale_status' => $this->wholesale ? 1 : 0,
            'is_multipay' => $this->multipay,
            'has_postpay' => $has_postpay,
            'invoice_notes' => $this->invoiceNotes,
            'tax_code' => $insertProducts[0]['tax_code'],
            "tax_schema" => $insertProducts[0]["tax_schema"],
            'zatca_uuid' => Str::uuid(),
            'zatca_invoice_name' => isset($this->customer['vat_number']) ? ZatcaInvoiceName::STANDARD : ZatcaInvoiceName::SIMPLIFIED,
            'merchant_name' => $settings->name,
            'merchant_vat' => $settings->VAT,
            'merchant_cr' => $settings->CR,
            'merchant_address' => $address ? "{$address->street}, {$address->district}" : null,
            'city_id' => $address? $address->city_id : null,
            'state_id' => $address? $address->state_id : null,
            'merchant_phone' => $settings->phone,
            'merchant_logo_path' => $settings->logo,
            'branch_name' => $branch->name,
            'customer_name' => $this->customer ? $this->customer['name'] : null,
            'customer_vat' => $this->customer ? $this->customer['vat_number'] : null,
            'customer_address' => $this->customer && isset($this->customer['address']) ? "{$this->customer['address']->street}, {$this->customer['address']->district}" : null,
            'customer_phone' => $this->customer ? $this->customer['phone_number'] : null,
            "invoice_type" => 'sell',
            "invoice_type_performed" => InvoiceTypePerformedEnum::SALE->value,
            "total_cost" => $this->cost,
            "total_cost_tax_exclusive" => $this->cost_tax_exclusive,
        ];

        // Add discount-specific data if applicable
        if($this->discountToggle == 1 && $this->discountInput > 0 && !is_null($this->discountInput)){
            if($this->totalAfterDiscountRounded < 0){
                return;
            }
            $invoiceData = array_merge($invoiceData, [
                "total_tax_inclusive" => $this->totalAfterDiscountRounded,
                "total_tax_exclusive" => BC::sub($this->totalAfterDiscountRounded, $this->totalTaxAfterDiscount, 10),
                "total_tax" => $this->totalTaxAfterDiscount,
                'is_discounted' => 1,
            ]);
        } else {
            $invoiceData = array_merge($invoiceData, [
                "total_tax_inclusive" => $this->total,
                "total_tax_exclusive" => BC::sub($this->total, $this->tax, 10),
                "total_tax" => $this->tax,
                'is_discounted' => 0,
                'is_rounded' => $this->roundingApplied,
            ]);
        }

        try {
            DB::transaction(function () use ($insertProducts, $invoiceData, $user, $roundingPoints) {
                $invoice = MerchantInvoice::create($invoiceData);
                $invoice->refresh();

                // Handle rounding data if applicable
                if($this->roundingApplied){
                    MerchantSaleInvoiceBeforeRounding::create([
                        "invoice_id" => $invoice->id,
                        "total_tax_inclusive" => $this->valuesBeforeRounding['total'],
                        "total_tax_exclusive" => BC::sub($this->valuesBeforeRounding['total'], $this->valuesBeforeRounding['tax'], 10),
                        "total_tax" => $this->valuesBeforeRounding['tax'],
                    ]);
                }

                // Handle discount data if applicable
                if($invoice['is_discounted']){
                    MerchantSaleInvoiceDiscount::create([
                        'invoice_id' => $invoice->id,
                        'type' => 'sell',
                        'total_after_discount' => $this->totalAfterDiscountRounded,
                        'total_discount_tax_exclusive' => $this->discountValue['discountValue'],
                        'total_discount_tax_exclusive_rounded' => $this->discountValue['discountValueRounded'],
                        'total_discount_tax' => $this->taxOfDiscount,
                        'total_discount_tax_rounded' => BC::round($this->taxOfDiscount, $roundingPoints),
                    ]);
                }

                // Process inventory changes for products
                $this->processInventoryChanges($insertProducts);

                // Add products to invoice
                $invoice->salesProducts()->createMany($insertProducts);

                // Save products before promotion only if there are promotions applied
                $hasPromotions = false;
                foreach ($this->products as $product) {
                    if (isset($product['has_promotion']) && $product['has_promotion']) {
                        $hasPromotions = true;
                        break;
                    }
                }

                if ($hasPromotions) {
                    $this->saveProductsBeforePromotion($invoice, $this->products);
                    // Save promotion groups using data from PosProductsList
                    $this->savePromotionGroupsFromProductsList($invoice, $insertProducts);
                }

                // Handle payment methods
                $this->processPaymentMethods($invoice);

                // Process Zatca if enabled
                if ($user->isZatcaEnabled) {
                    app(ZatcaInvoiceAction::class)($invoice);
                }

                // Create fulfillment orders if needed
                $this->createFulfillmentOrders($invoice, $insertProducts);

                $this->invoiceId = $invoice->id;

            });
        } catch (\Exception $exception) {
            $this->invoiceId = 0;
            if ($user->isZatcaEnabled) {
                return $this->dispatch('toast', message: __('messages.zatca_unavailable'));
            } else {
                Log::debug($exception);
                return $this->dispatch('toast', message: __('messages.invoice_error'));
            }
        }

        $this->clear();

        // Clear promotion groups to prevent accumulation across invoices
        $this->promotionGroups = [];
    }

    /**
     * Determine if the invoice has postpay payment method
     */
    private function determinePostpayStatus()
    {
        if ($this->multipay == false) {
            return $this->chosenPaymentMethod['is_postpay'] ? 1 : 0;
        }

        foreach ($this->multipayAmounts as $id => $amount) {
            $payment = MerchantPaymentMethod::query()->where('id', $id)->first();
            $this->multipayArray[$id]['is_postpay'] = $payment['is_postpay'];

            if ($payment['is_postpay']) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * Process inventory changes for non-service products
     *
     * Note: Products marked for delivery fulfillment or branch fulfillment will not have their inventory deducted
     * from the current branch as they are handled separately.
     */
    private function processInventoryChanges($insertProducts)
    {
        foreach($insertProducts as $product) {
            // Skip quantity reduction for service products
            if ($product['type'] === ProductTypeEnum::SERVICE->value) {
                continue;
            }

            // Get the cart_id to check if this product is marked for fulfillment
            $cartId = $product['cart_id'] ?? $product['product_id'];

            // Skip inventory deduction for products marked for delivery fulfillment
            if (isset($this->products[$cartId]) && isset($this->products[$cartId]['fulfillment_by_delivery'])) {
                \Illuminate\Support\Facades\Log::debug('Skipping inventory deduction for delivery product: ' . $cartId);
                continue;
            }

            // Skip inventory deduction for products marked for fulfillment from another branch
            if (isset($this->products[$cartId]) && isset($this->products[$cartId]['fulfillment_from_branch'])) {
                \Illuminate\Support\Facades\Log::debug('Skipping inventory deduction for branch fulfillment product: ' . $cartId);
                continue;
            }

            // For variant products, this is handled during initial product processing
            if ($product['type'] === ProductTypeEnum::VARIANT->value) {
                $variantOptionId = $product['product_variant_id'] ?? null;

                if ($variantOptionId) {
                    $variantBranchQuantity = MerchantProductBranchQuantity::where('product_variant_id', $variantOptionId)
                        ->where('branch_id', $product['branch_id'])
                        ->first();

                    if ($variantBranchQuantity) {
                        $variantBranchQuantity->quantity -= $product['cart_quantity'];
                        $variantBranchQuantity->save();
                    }
                }
            } elseif ($product['type'] === ProductTypeEnum::COMPOSITE->value) {
                // Handle composite product - reduce component quantities
                $compositeProduct = MerchantsProduct::find($product['product_id']);
                $components = $compositeProduct->compositeComponents;

                foreach ($components as $component) {
                    $componentProduct = $component->componentProduct;
                    $quantityToReduce = $component->quantity * $product['cart_quantity'];

                    $branchQuantity = MerchantProductBranchQuantity::where('product_id', $componentProduct->id)
                        ->where('branch_id', $product['branch_id'])
                        ->first();

                    if ($branchQuantity) {
                        $branchQuantity->quantity -= $quantityToReduce;
                        $branchQuantity->save();
                    }
                }
            } else {
                // Handle regular product
                $branchQuantity = MerchantProductBranchQuantity::where('product_id', $product['product_id'])
                    ->where('branch_id', $product['branch_id'])
                    ->first();

                if ($branchQuantity) {
                    $branchQuantity->quantity -= $product['cart_quantity'];
                    $branchQuantity->save();
                }
            }
        }
    }

    /**
     * Save products before promotion to track original prices
     */
    private function saveProductsBeforePromotion($invoice, $products)
    {
        $productsBeforePromotion = [];

        // Process all products to save their original state before promotions
        foreach ($products as $product) {
            // Determine the original price (before promotion)
            $originalSellPrice = isset($product['original_sell_price']) ? $product['original_sell_price'] : $product['sell_price'];
            $originalSellPriceTaxExclusive = isset($product['original_sell_price_tax_exclusive']) ? $product['original_sell_price_tax_exclusive'] : $product['sell_price_tax_exclusive'];

            // Get unit information if this is a weighted product
            $unit = null;
            if (isset($product['type']) && $product['type'] === ProductTypeEnum::WEIGHTED->value) {
                $unit = getWeightedProductSetting();
            }

            // Prepare product data without promotion effects - matching the database schema
            $productData = [
                'merchant_id' => $this->user->merchant_id,
                'business_id' => $this->user->business_id,
                'invoice_id' => $invoice->id,
                'product_id' => $product['id'],
                'product_name_ar' => isset($product['is_variant']) ? ($product['variant_name'] ?? $product['name']) : $product['name'],
                'product_name_en' => isset($product['is_variant']) ? ($product['variant_name'] ?? $product['name_en']) : ($product['name_en'] ?? $product['name']),
                'barcode' => $product['barcode'] ?? '',
                'price_tax_inclusive' => $originalSellPrice,
                'price_tax_exclusive' => $originalSellPriceTaxExclusive,
                'cost_tax_exclusive' => $product['cost_tax_exclusive_rounded'] ?? $product['cost_tax_exclusive'],
                'cost_tax_inclusive' => $product['cost'],
                'type' => $product['type'] ?? null,
                'cart_quantity' => $product['cartQuantity'],
                'unit_id' => $unit?->id,
                'unit_name' => $unit?->name,
                'tax_type' => $product['tax_id'] ?? null,
                'tax_code' => isset($product['tax']) ? $product['tax']['code'] : ($product['tax_code'] ?? null),
                'tax_schema' => $product['tax_schema'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'invoice_type' => InvoiceTypeEnum::SELL->value,
                'branch_id' => $this->branchId,
                'has_promotion' => isset($product['has_promotion']) && $product['has_promotion'] ? 1 : 0,
                'promotion_id' => $product['promotion_id'] ?? null,
                'registery_id' => $this->registeryId,
                'customer_id' => isset($this->customer['id']) ? $this->customer['id'] : 0,
                'wholesale_status' => $this->wholesale ? 1 : 0,
                'returnable' => $product['returnable'] ?? 1,
                'is_variant' => isset($product['is_variant']) && $product['is_variant'] ? 1 : 0,
                'product_variant_id' => $product['variant_id'] ?? null,
            ];

            $productsBeforePromotion[] = $productData;
        }

        // Save all products before promotion in a single query
        if (!empty($productsBeforePromotion)) {
            MerchantSaleInvoiceProductsBeforePromotion::insert($productsBeforePromotion);
        }
    }

    /**
     * Process payment methods for the invoice
     */
    private function processPaymentMethods($invoice)
    {
        if ($this->multipay) {
            $totalAmount = $invoice['is_discounted'] ? $this->totalAfterDiscountRounded : $this->total;
            $totalPaymentAmount = $this->totalMultiPayAmounts;

            if ($totalAmount == $totalPaymentAmount) {
                foreach ($this->multipayAmounts as $id => $amount) {
                    MerchantSaleInvoicePaymentMethod::create([
                        'invoice_id' => $invoice->id,
                        'payment_id' => $id,
                        'invoice_type' => 'sell',
                        'invoice_type_performed' => InvoiceTypePerformedEnum::SALE->value,
                        'is_postpay' => $this->multipayArray[$id]['is_postpay'] ? 1 : 0,
                        'amount' => $this->multipayArray[$id]['is_postpay'] ? (-$amount) : $amount,
                    ]);
                }
            }
        } else {
            $amount = $invoice['is_discounted'] ? $this->totalAfterDiscountRounded : $this->total;

            MerchantSaleInvoicePaymentMethod::create([
                'invoice_id' => $invoice->id,
                'payment_id' => $this->chosenPaymentMethod['id'],
                'is_postpay' => $this->chosenPaymentMethod['is_postpay'],
                'postpay_status' => $this->chosenPaymentMethod['is_postpay'] == 1 ? 'unpaid' : '',
                'invoice_type' => 'sell',
                'invoice_type_performed' => InvoiceTypePerformedEnum::SALE->value,
                'amount' => $this->chosenPaymentMethod['is_postpay'] ? -$amount : $amount,
            ]);
        }
    }

    public function PauseOrder(){
        $user = Auth::user();
        $userId = $user->id;
        $pausedProducts = [];

        if($this->products){
            // Pause the current cart
            foreach($this->products as $key => $product){
                $pausedProducts[$key]['name'] = $product['name'];
                $pausedProducts[$key]['name_en'] = $product['name_en'];
                $pausedProducts[$key]['quantity'] = $product['cartQuantity'];

                // Save variant information if this is a variant product
                if (isset($product['is_variant']) && $product['is_variant']) {
                    $pausedProducts[$key]['is_variant'] = true;
                    $pausedProducts[$key]['variant_id'] = $product['variant_id'];
                    $pausedProducts[$key]['variant_name'] = $product['variant_name'] ?? $product['name'];
                }
            }

            MerchantTempInvoice::create([
                'user_id' => $userId,
                'registry_id' => $this->registeryId,
                'branch_id' => $this->branchId,
                'customer_id' =>  isset($this->customer['id'])?$this->customer['id']:0,
                'products' => json_encode($pausedProducts),
                'total' => $this->total,
            ]);

            $this->clear();

        } else {
            // Show paused carts
            $this->loadingPausedOrders = true;
            $this->pausedClicked = true;

            try {
                $this->pausedOrders = MerchantTempInvoice::query()
                    ->where('user_id', $userId)
                    ->orderBy('created_at','desc')
                    ->take(5)
                    ->get();
            } catch (\Exception $e) {
                $this->dispatch('toast', message: __('messages.error_loading_paused_carts'));
            } finally {
                $this->loadingPausedOrders = false;
            }
        }
    }

    public function CustomerAdded($customer){
        $this->customer = $customer;
    }

    public function setPaymentMethod($id,$is_cash=null,$is_postpay=null,$is_rounding_activiated=null){
        if($this->multipay == false){
            $this->chosenPaymentMethod['id'] = $id;
            $this->chosenPaymentMethod['is_cash'] = $is_cash?1:0;
            $this->chosenPaymentMethod['is_postpay'] = $is_postpay?1:0;
            $this->chosenPaymentMethod['is_rounding_activiated'] = $is_rounding_activiated?1:0;
            $this->calculatingNumbers();

        }else{
            if(!is_null($this->multipayArray) && array_key_exists($id,$this->multipayArray)){
                unset($this->multipayArray[$id]);
                unset($this->multipayAmounts[$id]);
            }else{
                $this->multipayArray[$id]['id'] = $id;
                $this->multipayArray[$id]['is_postpay'] = $is_postpay;
            }
        }

    }

    public function multipayClicked(){
        if($this->user->plan->is_pos_multipay_supported){
            if($this->multipay){
                unset($this->multipayArray);
                unset($this->chosenPaymentMethod);
            }else{
                unset($this->multipayArray);
                $this->multipayAmounts = [];
                $this->totalMultiPayAmounts = 0;
                $this->chosenPaymentMethod['id'] = $this->paymentMethods[0]['id'];
                $this->chosenPaymentMethod['is_cash'] = $this->paymentMethods[0]['is_cash']?1:0;
                $this->chosenPaymentMethod['is_postpay'] = $this->paymentMethods[0]['is_postpay']?1:0;
                $this->chosenPaymentMethod['is_rounding_activiated'] = $this->paymentMethods[0]['is_rounding_activiated']?1:0;

            }
        }else{
            $this->multipay = 0;
            $this->totalMultiPayAmounts = 0;

        }
    }

    public function multipayAmountsChanged(){
        $this->totalMultiPayAmounts = 0;
            foreach($this->multipayAmounts as $amount){
                $amount = $this->sanitizeInput($amount);
                if(is_numeric($amount)){
                    $this->totalMultiPayAmounts = BC::add($this->totalMultiPayAmounts,$amount,10);
                }
            }
    }

    public function wholesaleStatus($status){
        $this->wholesale = $status;
        $this->calculatingNumbers();
    }

    public function ContinueOrder($id){
        //TODO user this->user and test
        $user = Auth::user();
        $cart = MerchantTempInvoice::query()->where('id', $id)->first();
        $products = json_decode($cart->products);
        foreach($products as $key => $product){
            // Check if this is a variant product
            $isVariant = isset($product->is_variant) && $product->is_variant;
            $productId = $isVariant && isset($product->variant_id) ? $product->variant_id : $key;

            // Pass the isVariant parameter to properly handle variant products
            $this->dispatch('ContinueCart', productId: $productId, quantity: $product->quantity, isVariant: $isVariant);
        }
        $cart->delete();
    }

    public function percentageSliderUpdated($value){
            if(!$this->main_account && $this->permissions['cashier_discount_max_percentage'] < $value[0]){
                $value[0] = $this->permissions['cashier_discount_max_percentage'];
            }

            if($value[0] == 0){
                if($this->discountToggle){
                    $this->discountValue['discountValue'] = 0;
                    $this->discountValue['discountValueRounded'] = 0;
                    $this->discountInput = 0;
                    $this->calculatingNumbers();
                    return;
                }else{
                    return;
                }
            }

        $roundingPoints = 2;

        if ($this->products){
            foreach ($this->products as $product) {
                if ($product['type'] === ProductTypeEnum::WEIGHTED->value) {
                    $roundingPoints = getRoundingPoints($product['type']);
                    break;
                }
            }
        }

        // so we can take the percentage from the total
        $percentageValue = BC::div($value[0],100,10);
        $this->discountInput = BC::round(BC::mul($this->total,$percentageValue,10),$roundingPoints);
        $this->discountPercentage = $value;
        $this->calculatingNumbers();
    }


    public function discountToggleAction(){
        if($this->discountToggle == 1){
            $this->taxOfDiscount = 0;
            $this->discountToggle = 0;
        }else{
            $this->discountToggle = 1;
        }

        $this->calculatingNumbers();
    }

    /**
     * Broadcast cart data to the customer display
     */
    private function broadcastCartData()
    {
        // Only broadcast if we have a register ID
        if (!$this->registeryId) {
            return;
        }

        // Check if there's an active customer display for this register
        if (!ActiveCustomerDisplay::isActive($this->registeryId)) {
            // No active display, so don't broadcast
            return;
        }

        // Get merchant settings for the name
        $settings = MerchantsSetting::where('merchant_id', $this->user->merchant_id)->first();

        // Prepare cart data for broadcasting
        $cartData = [
            'products' => $this->products,
            'subtotal' => $this->subtotal,
            'tax' => $this->tax,
            'total' => $this->total,
            'discount' => $this->discountToggle ? $this->discountInput : 0,
            'currency' => $this->currency ? $this->currency->symbol : 'SAR',
            'merchantName' => $settings ? $settings->name : null,
            'timestamp' => now()->timestamp
        ];

        // Broadcast the event
        event(new POSCartUpdateEvent($this->registeryId, $cartData));
    }

    public function discountInputChanged(){
        $this->discountInput = $this->sanitizeInput($this->discountInput);

        if(!$this->main_account && $this->discountInput > $this->permissions['cashier_discount_max_number']){
            $message = Lang::get('messages.max_discount_amount').' '.$this->permissions['cashier_discount_max_number'];
            $this->discountInput = $this->permissions['cashier_discount_max_number'];
            $this->dispatch('toast', message: $message);
        }

        if($this->taxRate < 0){
            return;
        }

        // Reset discount-related values
        $this->discountValue['discountValueRounded'] = 0;
        $this->totalTaxAfterDiscount = 0;
        $this->totalAfterDiscountRounded = 0;

        if($this->taxRate >= 0 && isset($this->products) && count($this->products) > 0){
            // Determine rounding points based on product type
            $roundingPoints = 2;
            foreach ($this->products as $product) {
                if ($product['type'] === ProductTypeEnum::WEIGHTED->value) {
                    $roundingPoints = getRoundingPoints($product['type']);
                    break;
                }
            }

            // Calculate discount distribution proportionally across products
            $totalDiscountTaxExclusive = '0';
            $totalDiscountTax = '0';

            foreach ($this->products as $product) {
                // Calculate this product's proportion of the total
                $productTotal = BC::mul($product['cartQuantity'],
                    $this->wholesale ? $product['wholesale_price'] : $product['sell_price'], 10);
                $proportion = BC::div($productTotal, $this->total, 10);

                // Calculate discount amount for this product
                $productDiscountAmount = BC::mul($proportion, $this->discountInput, 10);



                // If product has tax, split the discount into tax-exclusive and tax portions
                if (isset($product['tax']['percentage']) && $product['tax']['percentage'] > 0) {
                    $productDiscountTaxExclusive = Helper::excludeTax($productDiscountAmount, $product['tax']['percentage']);
                    $productDiscountTax = BC::sub($productDiscountAmount, $productDiscountTaxExclusive, 10);
                } else {
                    // No tax on this product, entire discount is tax-exclusive
                    $productDiscountTaxExclusive = $productDiscountAmount;
                    $productDiscountTax = '0';
                }

                $totalDiscountTaxExclusive = BC::add($totalDiscountTaxExclusive, $productDiscountTaxExclusive, 10);
                $totalDiscountTax = BC::add($totalDiscountTax, $productDiscountTax, 10);
            }

            $this->discountValue['discountValue'] = $totalDiscountTaxExclusive;
            $this->discountValue['discountValueRounded'] = BC::round($totalDiscountTaxExclusive, $roundingPoints);

            // Calculate final totals after discount
            $this->totalTaxAfterDiscount = BC::round(
                BC::sub($this->tax, $totalDiscountTax, 10),
                $roundingPoints
            );
            $this->totalAfterDiscountRounded = BC::round(
                BC::add(
                    BC::sub($this->subtotal, $totalDiscountTaxExclusive, 10),
                    $this->totalTaxAfterDiscount, 10
                ),
                $roundingPoints
            );

            $this->taxOfDiscount = $totalDiscountTax;
        }
    }

    private function convertArabicToEnglish($input) {
        $arabicToEnglishMap = [
            '٠' => '0', '١' => '1', '٢' => '2', '٣' => '3', '٤' => '4',
            '٥' => '5', '٦' => '6', '٧' => '7', '٨' => '8', '٩' => '9'
        ];

        return strtr($input, $arabicToEnglishMap);
    }

    private function sanitizeInput($input) {
        // Remove any non-numeric and non-decimal characters
        $numericValue = preg_replace('/[^0-9.٠-٩]/u', '', $input);

        // Convert Arabic numerals to English numerals
        $numericValue = $this->convertArabicToEnglish($numericValue);

        return $numericValue;
    }

    public function updatedDiscountInput()
    {
        if (empty($this->discountInput)) {
            $this->discountInputChanged();
        }
    }

    public function setTaxRate($rate){
        $this->taxRate = $rate;
        $this->updateTaxRateBasedOnProducts();
    }

    /**
     * Update the tax rate based on products in the cart
     * If any product has a percentage of 15 and code 'S', set tax rate to 15
     * Otherwise, set tax rate to 0
     */
    private function updateTaxRateBasedOnProducts()
    {
        if (empty($this->products)) {
            $this->taxRate = 0;
            return;
        }

        $hasTaxableProduct = false;

        foreach ($this->products as $product) {
            if (isset($product['tax']) &&
                isset($product['tax']['percentage']) &&
                $product['tax']['percentage'] == 15 &&
                isset($product['tax']['code']) &&
                $product['tax']['code'] == 'S') {
                $hasTaxableProduct = true;
                break;
            }
        }

        $this->taxRate = $hasTaxableProduct ? 15 : 0;

        // Recalculate numbers with the new tax rate
        $this->calculatingNumbers();
    }

    /**
     * Create fulfillment orders for products that need fulfillment
     *
     * @param MerchantInvoice $invoice The created invoice
     * @param array $insertProducts The products that were added to the invoice
     * @return void
     */
    /**
     * Process return products in the cart
     *
     * @return void
     */
    private function processReturnProducts()
    {
        // Add debug logging

        $user = getCurrentUser();

        // Get all return products from the cart
        $returnProducts = [];
        $originalInvoiceId = null;
        $customerId = null;
        foreach ($this->products as $cartId => $product) {
            if (isset($product['is_return']) && $product['is_return']) {
                // Extract the original invoice ID if available
                if (isset($product['original_invoice_id']) && !$originalInvoiceId) {
                    $originalInvoiceId = $product['original_invoice_id'];
                }

                // Extract the customer ID if available
                if (isset($product['customer_id']) && !$customerId) {
                    $customerId = $product['customer_id'];
                }

                // Add to return products array
                $returnProducts[$cartId] = $product;
            }
        }

        // If no original invoice ID is found, show an error
        if (!$originalInvoiceId) {
            $this->dispatch('toast', message: __('messages.original_invoice_not_found'));
            return;
        }

        // Prepare data for the invoice return service
        $returnData = [
            'branch' => $this->branchId,
            'payment' => $this->chosenPaymentMethod['id'],
            'backto' => 'pos',
            'branchId' => $this->branchId,
            'registeryId' => $this->registeryId,
        ];

        // Use the customer ID from the original invoice if available
        // Otherwise, use the customer ID from the cart if available
        if ($customerId) {
            $returnData['customer_id'] = $customerId;
        } elseif (isset($this->customer['id']) && $this->customer['id'] > 0) {
            $returnData['customer_id'] = $this->customer['id'];
        }
        // Add return products data
        foreach ($returnProducts as $cartId => $product) {
            $productId = $product['id'];
            $returnQty = abs($product['cartQuantity']); // Convert to positive for the service

            // For variant products, include the variant ID
            if (isset($product['is_variant']) && $product['is_variant'] && isset($product['variant_id'])) {
                $returnData['products'][$productId]['variant_id'] = $product['variant_id'];
            }

            // Add the return quantity
            $returnData['products'][$productId]['quantity'] = $returnQty;
        }

        try {
            // Use the InvoiceReturnService to process the return with a normal array
            $invoiceReturnService = new \App\Services\InvoiceReturnService();
            $result = $invoiceReturnService->processReturn($originalInvoiceId, $returnData, $user);
            // Check for errors
            if (isset($result['error'])) {
                $this->dispatch('toast', message: $result['error']);
                return;
            }

            // Store the returned invoice ID for printing
            if (isset($result['returnedInvoiceId'])) {
                $this->invoiceId = $result['returnedInvoiceId'];
            }

            // Clear the cart
            $this->clear();

            // Show success message
            $this->dispatch('toast-success', message: __('messages.return_successful'));

        } catch (\Exception $exception) {
            \Illuminate\Support\Facades\Log::debug($exception);
            $this->dispatch('toast', message: __('messages.return_error') . ' - ' . $exception->getMessage());
        }
    }

    private function createFulfillmentOrders($invoice, $insertProducts)
    {
        // Add debug logging
        \Illuminate\Support\Facades\Log::debug('createFulfillmentOrders called for invoice: ' . $invoice->id);
        \Illuminate\Support\Facades\Log::debug('Products in cart: ' . json_encode($this->products));

        // Check if we have any products that need fulfillment
        $needsFulfillment = false;

        // Update the products array with invoice_product_id values
        $invoiceProducts = $invoice->salesProducts()->get();
        \Illuminate\Support\Facades\Log::debug('Invoice products: ' . json_encode($invoiceProducts));

        foreach ($this->products as $cartId => $product) {
            // Find the corresponding invoice product
            $invoiceProduct = $invoiceProducts->first(function ($item) use ($product, $cartId) {
                return ($item->cart_id == $cartId) ||
                       ($item->product_id == $product['id'] &&
                        $item->product_variant_id == ($product['variant_id'] ?? null));
            });

            if ($invoiceProduct) {
                $this->products[$cartId]['invoice_product_id'] = $invoiceProduct->id;
                \Illuminate\Support\Facades\Log::debug('Added invoice_product_id ' . $invoiceProduct->id . ' to product ' . $cartId);

                // Check if this product needs fulfillment
                if (isset($product['fulfillment_from_branch']) || isset($product['fulfillment_by_delivery'])) {
                    $needsFulfillment = true;
                    \Illuminate\Support\Facades\Log::debug('Product needs fulfillment: ' . json_encode($product));
                }
            } else {
                \Illuminate\Support\Facades\Log::debug('Could not find invoice product for cart item: ' . $cartId);
            }
        }

        // If no products need fulfillment, return
        if (!$needsFulfillment) {
            \Illuminate\Support\Facades\Log::debug('No products need fulfillment, returning');
            return;
        }

        \Illuminate\Support\Facades\Log::debug('Dispatching createFulfillmentsFromCart event');

        // Call the PosProductsList component to create fulfillments
        // Pass the updated products array with invoice_product_id values
        $this->dispatch('createFulfillmentsFromCart', invoice: $invoice, products: $this->products);
    }

    private function formatValueBasedOnProductType(string $productType, $value)
    {
        if ($productType === ProductTypeEnum::WEIGHTED->value) {
            return Helper::getCostOrPriceInCurrentUnit($value);
        }

        return $value;
    }

    /**
     * Save promotion groups using data from PosProductsList
     */
    private function savePromotionGroupsFromProductsList($invoice, $insertProducts)
    {
        // Use promotion groups data directly from PosProductsList instead of extracting from products
        if (!empty($this->promotionGroups)) {
            foreach ($this->promotionGroups as $groupData) {
                MerchantSaleInvoicePromotionGroup::create([
                    'invoice_id' => $invoice->id,
                    'group_id' => $groupData['group_id'],
                    'original_promotion_id' => $groupData['original_promotion_id'],
                    'promotion_snapshot' => $groupData['promotion_snapshot'],
                    'buy_x_quantity_required' => $groupData['buy_x_quantity_required'],
                    'get_y_quantity_given' => $groupData['get_y_quantity_given'],
                    'promotion_type' => $groupData['promotion_type']
                ]);
            }
        } else {
            // Fallback: Extract promotion groups data from products if promotionGroups is empty
            $promotionGroupsData = $this->extractPromotionGroupsFromProducts($insertProducts);

            if (!empty($promotionGroupsData)) {
                foreach ($promotionGroupsData as $groupData) {
                    MerchantSaleInvoicePromotionGroup::create([
                        'invoice_id' => $invoice->id,
                        'group_id' => $groupData['group_id'],
                        'original_promotion_id' => $groupData['original_promotion_id'],
                        'promotion_snapshot' => $groupData['promotion_snapshot'],
                        'buy_x_quantity_required' => $groupData['buy_x_quantity_required'],
                        'get_y_quantity_given' => $groupData['get_y_quantity_given'],
                        'promotion_type' => $groupData['promotion_type']
                    ]);
                }
            }
        }

        // Update products with group information
        $this->updateProductsWithGroupInfo($insertProducts);
    }

    /**
     * Extract promotion groups data from cart products
     */
    private function extractPromotionGroupsFromProducts($insertProducts)
    {
        $groups = [];

        foreach ($insertProducts as $product) {
            if (isset($product['promotion_group_id']) && !empty($product['promotion_group_id'])) {
                $groupId = $product['promotion_group_id'];

                if (!isset($groups[$groupId])) {
                    $groups[$groupId] = [
                        'group_id' => $groupId,
                        'original_promotion_id' => $product['promotion_id'] ?? null,
                        'promotion_snapshot' => [
                            'original_promotion_id' => $product['promotion_id'] ?? null,
                            'promotion_name' => $product['promotion_name'] ?? 'Buy X Get Y Promotion',
                            'promotion_type' => PromotionTypeEnum::BUY_X_GET_Y->value,
                            'buy_x_quantity' => $product['promotion_buy_x_quantity'] ?? 2,
                            'get_y_quantity' => $product['promotion_get_y_quantity'] ?? 1,
                            'get_y_type' => $product['promotion_get_y_type'] ?? 'free',
                            'get_y_discount_percentage' => $product['promotion_discount_percentage'] ?? null,
                            'created_at' => now()->toISOString(),
                        ],
                        'buy_x_quantity_required' => $product['promotion_buy_x_quantity'] ?? 2,
                        'get_y_quantity_given' => $product['promotion_get_y_quantity'] ?? 1,
                        'promotion_type' => PromotionTypeEnum::BUY_X_GET_Y->value
                    ];
                }
            }
        }

        return array_values($groups);
    }

    /**
     * Update invoice products with promotion group information from cart
     */
    private function updateProductsWithGroupInfo($insertProducts)
    {
        // Products already have group information from PosProductsList
        // Just ensure the data is properly formatted for database insertion
        foreach ($insertProducts as $index => &$product) {
            // Group information is already set in cart products
            // promotion_group_id, promotion_product_type are already assigned
            if (isset($product['promotion_group_id'])) {
                // Ensure the fields are properly set for database insertion
                $product['promotion_group_id'] = $product['promotion_group_id'];
                $product['promotion_product_type'] = $product['promotion_product_type'] ?? null;
            }
        }
    }
}
