<?php

namespace App\Livewire;

use App\Enums\Application\ProductStatus;
use App\Enums\Application\ProductTypeEnum;
use App\Enums\Application\PromotionCategoryTypeEnum;
use App\Enums\Application\PromotionDiscountTypeEnum;
use App\Enums\Application\PromotionProductTypeEnum;
use App\Enums\Application\PromotionTagTypeEnum;
use App\Enums\Application\PromotionTypeEnum;
use App\Enums\Application\TaxCodeEnum;
use App\Enums\Application\InvoiceTypePerformedEnum;
use App\Models\MerchantCustomer;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantProductVariant;
use App\Models\MerchantCategory;
use App\Models\MerchantPromotion;
use App\Models\MerchantTag;
use App\Models\MerchantsProduct;
use App\Models\MerchantBranch;
use App\Services\BusinessContextService;
use BCMathExtended\BC;
use App\Models\MerchantInvoice;
use App\Services\FulfillmentService;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Lang;
use Livewire\Component;
use Livewire\Attributes\Computed;

class PosProductsList extends Component
{
    public $inputValue;
    public $showSideBarProducts = false;
    public $branchId;
    public $registeryId;
    public $cartProducts = [];
    public $products = [];
    public $test = [];
    public $searchResults = [];
    public $searchCustomerResults = [];
    public $changedQuantity = [];
    public $showList = false;
    public $showCustomerList = false;
    public $rank = 0;
    public $customers = [];
    public $chosenCustomer = ['id' => 0, 'name' => ''];
    public $inputCustomerValue;
    public $wholesale = false;
    public $productDetailId = 0;
    public $productDetailKey = 0;
    public $productBranchQuantities = [];
    public $taxRate = null;
    public $user;
    public $permissions;
    public $plan;
    public $selectedFulfillmentProduct = null;
    public $branchesForFulfillment = [];
    public $selectedFulfillmentBranch = '';
    public $fulfillmentQuantity = 1;
    public $maxFulfillmentQuantity = 0;
    public $deliveryAddress = '';
    public $deliveryDate = '';
    public $deliveryTime = '';
    public $deliveryNotes = '';
    public $pendingAction = null; // Track pending actions that require a customer
    public $pendingActionData = null; // Store data for the pending action
    public $appliedPromotions = [];
    public $activePromotions = [];
    public $promotionGroups = []; // Store promotion group data for invoice creation

    protected $listeners = [
        "ProdClicked2", "CartAdded", "clear2", 'RemoveProduct',
        'EditQuantityInCart', 'increaseRank', 'customerAdded',
        'customerRemove', 'chooseProductDetail', 'ContinueCart',
        'handleEnterKeyPress', 'customerCreated', 'createFulfillmentsFromCart',
        'addReturnProductsFromInvoice', 'clearPromotionGroups'
    ];

    public function mount($branchId = null, $registeryId = null)
    {
        $user = Auth::user();
        $this->user = $user;
        $this->plan = $user->plan;
        $this->permissions = $user->group->permissions;
        $this->branchId = $branchId;
        $this->registeryId = $registeryId;
    }

    public function render()
    {
        return view('livewire.pos-products-list');
    }

    public function ProdClicked2($status = "", $productId = 0, $isVariant = false, $isReturn = false)
    {
        // Add debug logging
        \Illuminate\Support\Facades\Log::debug('ProdClicked2 called with: status=' . $status . ', productId=' . $productId . ', isVariant=' . ($isVariant ? 'true' : 'false') . ', isReturn=' . ($isReturn ? 'true' : 'false'));

        if ($status == "reset") {
            $this->resetCart();
            return;
        }

        $inputValue = $this->inputValue;

        // Special case for return products or when product ID is provided
        if ($productId > 0) {
            \Illuminate\Support\Facades\Log::debug('Product ID provided, proceeding with search');
            // Continue with the product search
        }
        // Handle short input for quantity
        else if (strlen($inputValue) <= 2 && is_numeric($inputValue) && $productId == 0) {
            $this->handleShortInput($inputValue, $productId);
            return;
        }
        // No input and no product ID
        else if (strlen($inputValue) <= 0 && $productId == 0) {
            \Illuminate\Support\Facades\Log::debug('No input value or product ID provided');
            return;
        }

        // Handle variant products differently
        if ($isVariant) {
            \Illuminate\Support\Facades\Log::debug('Processing variant product with ID: ' . $productId);
            $variantProduct = $this->getVariantProductById($productId);
            if (!$variantProduct) {
                $this->showToast(Lang::get('messages.doesnt_exist_product'));
                $this->hideProductListAndClearSearch();
                return;
            }

            if (!$this->validateProduct($variantProduct)) {
                $this->hideProductListAndClearSearch();
                return;
            }

            \Illuminate\Support\Facades\Log::debug('Adding variant product to cart with isReturn=' . ($isReturn ? 'true' : 'false'));
            $this->addToCart($variantProduct, $isReturn);
            $this->hideProductListAndClearSearch();
            return;
        }

        // Handle regular products
        if($productId > 0){
            \Illuminate\Support\Facades\Log::debug('Getting product by ID: ' . $productId);
            $product = $this->getProduct($productId,'id');
        }else{
            \Illuminate\Support\Facades\Log::debug('Getting product by barcode: ' . $inputValue);
            $product = $this->getProduct($inputValue,'barcode');
        }

        if ($product->isEmpty()) {
            \Illuminate\Support\Facades\Log::debug('Product not found');
            $this->showToast(Lang::get('messages.doesnt_exist_product'));
            $this->hideProductListAndClearSearch();
            return;
        }

        $productFirst = $product->first();
        // dd($productFirst);
        \Illuminate\Support\Facades\Log::debug('Found product: ' . json_encode($productFirst));

        if (!$this->validateProduct($productFirst)) {
            \Illuminate\Support\Facades\Log::debug('Product validation failed');
            $this->hideProductListAndClearSearch();
            return;
        }

        \Illuminate\Support\Facades\Log::debug('Adding regular product to cart with isReturn=' . ($isReturn ? 'true' : 'false'));
        $this->addToCart($productFirst, $isReturn);

        $this->hideProductListAndClearSearch();
    }

    public function handleEnterKeyPress()
    {
        if (!empty($this->inputValue)) {
            $this->ProdClicked2('', 0, false, false);
        }
    }

    private function hideProductListAndClearSearch($inputValue = false)
    {
        $this->showList = false;
        if(!$inputValue){
            $this->inputValue = '';
        }
    }

    private function resetCart()
    {
        $this->cartProducts = [];
        $this->dispatch("CartAdded", cart: $this->cartProducts);
    }

    private function handleShortInput($inputValue, $productId)
    {
        $this->hideProductListAndClearSearch();
        if ($productId == 0 && is_numeric($inputValue) && !empty($this->cartProducts)) {
            // Get the first product in the cart
            $firstProduct = reset($this->products);
            $cartId = $firstProduct['cart_id'] ?? $firstProduct['id'];

            // Set the quantity for this product
            $this->changedQuantity[$cartId] = $inputValue;
            $this->EditQuantityInCart(cartId: $cartId);
            $this->showList = false;
            $this->inputValue = '';
        }
    }

    #[Computed]
    private function getProduct($identifier, $type, $includeDeleted = false)
    {
        // First, check if this is a variant product by barcode
        if ($type == 'barcode') {
            $variantProduct = $this->getVariantProductByBarcode($identifier);
            if ($variantProduct) {
                return collect([$variantProduct]);
            }
        }

        // If not a variant product or searching by ID, proceed with regular product search
        // Exclude main variant products
        $query = MerchantsProduct::query();

        // Include soft deleted products if requested (for returns)
        if ($includeDeleted) {
            $query->withTrashed();
        }

        $query->where('type', '!=', ProductTypeEnum::VARIANT->value) // Exclude main variant products
            ->with([
                'tax' => function ($query) {
                    $query->select('id', 'percentage', 'code');
                },
                'branchQuantities' => function ($query) {
                    $query->where('branch_id', $this->branchId)
                        ->select('id', 'product_id', 'branch_id', 'quantity','sell_price','wholesale_price','sell_price_tax_exclusive','sell_price_tax_exclusive_rounded','wholesale_price_tax_exclusive','wholesale_price_tax_exclusive_rounded');
                },
                'compositeComponents.componentProduct.branchQuantities' => function($query) {
                    $query->where('branch_id', $this->branchId);
                }
            ])
            ->select(
                'id',
                'merchant_id',
                'business_id',
                'name',
                'name_en',
                'cost',
                'barcode',
                'type',
                'status',
                'tax_id',
                'tax_schema',
                'returnable',
                'thumbnail',
                'cost_tax_exclusive',
                'sell_price_tax_exclusive',
                'wholesale_price_tax_exclusive',
                'cost_tax_exclusive_rounded',
                'sell_price_tax_exclusive_rounded',
                'wholesale_price_tax_exclusive_rounded',
                'sellable',
            );

        if ($type == 'id') {
            $query->where('id', $identifier);
        } else {
            $query->where('barcode', $identifier);
        }

        $result = $query->get();

        $get = $result->map(function ($item) {
            // Add cart_id for regular products (same as id for non-variants)
            $item->cart_id = $item->id;

            $branchQuantity = $item->branchQuantities->first();
            $item->percentage = $item->tax->percentage;
            $item->sell_price = $branchQuantity?->sell_price ?? 0;
            $item->sell_price_tax_exclusive = $branchQuantity?->sell_price_tax_exclusive ?? 0;
            $item->sell_price_tax_exclusive_rounded = $branchQuantity?->sell_price_tax_exclusive_rounded ?? 0;
            $item->wholesale_price = $branchQuantity?->wholesale_price ?? 0;
            $item->wholesale_price_tax_exclusive = $branchQuantity?->wholesale_price_tax_exclusive ?? 0;
            $item->wholesale_price_tax_exclusive_rounded = $branchQuantity?->wholesale_price_tax_exclusive_rounded ?? 0;
            $item->branch_id = $branchQuantity?->branch_id;

            // For composite products, calculate the maximum possible quantity based on components
            if ($item->type === ProductTypeEnum::COMPOSITE->value) {
                $maxPossibleQuantity = null;

                foreach ($item->compositeComponents as $component) {
                    $componentProduct = $component->componentProduct;
                    $componentBranchQuantity = $componentProduct->branchQuantities->first()?->quantity ?? 0;

                    // Set the component quantity for reference
                    $componentProduct->quantity = $componentBranchQuantity;

                    // Skip service components when calculating composite quantity
                    if ($componentProduct->type === ProductTypeEnum::SERVICE->value) {
                        continue;
                    }

                    // Calculate how many composite products can be made with this component
                    $possibleQuantity = $component->quantity > 0 ? floor($componentBranchQuantity / $component->quantity) : 0;

                    if ($maxPossibleQuantity === null || $possibleQuantity < $maxPossibleQuantity) {
                        $maxPossibleQuantity = $possibleQuantity;
                    }
                }

                // Set the composite product quantity to the maximum possible quantity
                $item->quantity = $maxPossibleQuantity ?? 0;
            } else {
                // For regular products, use the branch quantity directly
                $item->quantity = $branchQuantity?->quantity ?? 0;
            }

            return $item;
        });

        return $get;
    }

    /**
     * Get a variant product by its barcode
     *
     * @param string $barcode The barcode to search for
     * @return object|null The variant product or null if not found
     */
    private function getVariantProductByBarcode($barcode)
    {
        $variantProduct = MerchantProductVariant::where([
            'barcode' => $barcode
        ])
        ->with([
            'product.tax' => function ($query) {
                $query->select('id', 'percentage', 'code');
            },
            'branchQuantities' => function($query) {
                $query->where('branch_id', $this->branchId);
            }
        ])
        ->first();

        if (!$variantProduct) {
            return null;
        }

        return $this->prepareVariantProduct($variantProduct);
    }

    /**
     * Get a variant product by its ID
     *
     * @param int $id The variant product ID
     * @param bool $includeDeleted Whether to include soft deleted parent products
     * @return object|null The variant product or null if not found
     */
    private function getVariantProductById($id, $includeDeleted = false)
    {
        $variantProduct = MerchantProductVariant::where([
            'id' => $id
        ])
        ->with([
            'product.tax' => function ($query) use ($includeDeleted) {
                $query->select('id', 'percentage', 'code');
                if ($includeDeleted) {
                    $query->withTrashed();
                }
            },
            'branchQuantities' => function($query) {
                $query->where('branch_id', $this->branchId);
            }
        ])
        ->first();

        // If variant product not found, return null
        if (!$variantProduct) {
            return null;
        }

        // If we need to include deleted products, make sure to load the parent product with trashed
        if ($includeDeleted && !$variantProduct->product) {
            $variantProduct->load(['product' => function($query) {
                $query->withTrashed()->with(['tax' => function($taxQuery) {
                    $taxQuery->select('id', 'percentage', 'code');
                }]);
            }]);
        }

        return $this->prepareVariantProduct($variantProduct);
    }

    /**
     * Prepare a variant product object with all necessary data
     *
     * @param MerchantProductVariant $variantProduct The variant product record
     * @return object The prepared product object
     */
    private function prepareVariantProduct($variantProduct)
    {
        // Create a CLONE of the product object to avoid overriding shared data
        $product = clone $variantProduct->product;

        // Get option names without using joins for display
        $optionNames = [];
        $attributes = $variantProduct->variantAttributes()
            ->with(['variantOption', 'variant'])
            ->get();

        foreach ($attributes as $attribute) {
            if ($attribute->variantOption) {
                $optionNames[] = $attribute->variantOption->name;
            }
        }

        $variantPart = implode(' - ', $optionNames);
        $fullName = trim("{$product->name} - {$variantPart}", ' -');

        // Add the variant-specific data to the product
        $product->variant_name = $fullName;
        $product->is_variant = true;
        $product->variant_id = $variantProduct->id;

        // Create a unique ID for this variant product to avoid overriding
        // We'll use the product ID and variant ID combined
        $product->unique_id = $product->id . '-' . $variantProduct->id;

        // Add cart_id for consistent identification in the cart system
        $product->cart_id = $product->id . '-' . $variantProduct->id;

        // Get the branch quantity for this variant
        $branchQuantity = $variantProduct->branchQuantities
            ->where('branch_id', $this->branchId)
            ->first();

        $product->quantity = $branchQuantity ? $branchQuantity->quantity : 0;
        $product->sell_price = $branchQuantity ? $branchQuantity->sell_price : 0;
        $product->sell_price_tax_exclusive = $branchQuantity ? $branchQuantity->sell_price_tax_exclusive : 0;
        $product->sell_price_tax_exclusive_rounded = $branchQuantity ? $branchQuantity->sell_price_tax_exclusive_rounded : 0;
        $product->wholesale_price = $branchQuantity ? $branchQuantity->wholesale_price : 0;
        $product->wholesale_price_tax_exclusive = $branchQuantity ? $branchQuantity->wholesale_price_tax_exclusive : 0;
        $product->wholesale_price_tax_exclusive_rounded = $branchQuantity ? $branchQuantity->wholesale_price_tax_exclusive_rounded : 0;
        $product->barcode = $variantProduct->barcode; // Use the variant barcode
        $product->display_name = $fullName; // Use combined name for display

        // Ensure tax property is properly mapped
        if ($product->relationLoaded('tax')) {
            $product->tax = $product->tax;
            $product->percentage = $product->tax->percentage;
        }

        return $product;
    }

    private function validateCompositeProductQuantity($product, $requestedQuantity)
    {
        // Load composite components if not already loaded
        if (!$product->relationLoaded('compositeComponents')) {
            $product->load(['compositeComponents.componentProduct.branchQuantities' => function($query) {
                $query->where('branch_id', $this->branchId);
            }]);
        }

        $maxPossibleQuantity = null;

        foreach ($product->compositeComponents as $component) {
            $componentProduct = $component->componentProduct;
            $branchQuantity = $componentProduct->branchQuantities->first()?->quantity ?? 0;
            $requiredQuantity = $component->quantity * $requestedQuantity;

            if ($branchQuantity < $requiredQuantity) {
                $possibleQuantity = floor($branchQuantity / $component->quantity);
                if ($maxPossibleQuantity === null || $possibleQuantity < $maxPossibleQuantity) {
                    $maxPossibleQuantity = $possibleQuantity;
                }
            }
        }

        if ($maxPossibleQuantity !== null) {
            if ($maxPossibleQuantity <= 0) {
                $this->showToast(Lang::get('messages.no_quantity_product'));
            } else {
                $this->showToast(Lang::get('messages.max_composite_to_sell') . ' ' . $maxPossibleQuantity);
            }
            return $maxPossibleQuantity;
        }

        return $requestedQuantity;
    }

    private function validateProduct($product)
    {
        $price = $this->wholesale ? $product->wholesale_price : $product->sell_price;

        if ($product->status == ProductStatus::DISABLED->value) {
            $this->showToast(Lang::get('messages.disabled_product'));
            return false;
        }
        elseif (!$product->sellable) {
            $this->showToast(Lang::get('messages.non_sellable_product_error'));
            return false;
        }
        // Commented out quantity validation - allowing products with zero quantity
        /*
        elseif ($product->type !== ProductTypeEnum::SERVICE->value) {
            if ($product->type === ProductTypeEnum::COMPOSITE->value) {
                // Check if any quantity is available
                $maxQuantity = $this->validateCompositeProductQuantity($product, 1);
                if ($maxQuantity <= 0) {
                    return false;
                }
            } elseif ($product->quantity <= 0) {
                $this->showToast(Lang::get('messages.no_quantity_product'));
                return false;
            }
        }
        */
        elseif ($price <= $product->cost) {
            $this->showToast(Lang::get('messages.sell_less_cost'));
            return false;
        }
        elseif ($this->user->isZatcaEnabled && ($product->tax->percentage == 0 &&  $product->tax->code == TaxCodeEnum::NONE->value)) {
            $this->showToast(Lang::get('messages.cannot_add_non_taxable_product'));
            return false;
        }
        return true;
    }
    private function addToCart($product, $isReturn = false)
    {
        // Use cart_id as the key for all products (regular and variants)
        $productKey = $product->cart_id;

        // For returned products, create a unique key by adding a suffix
        if ($isReturn) {
            $productKey = $productKey . '-return';
        }

        if (isset($this->cartProducts[$productKey])) {
            $this->updateExistingCartItem($product, $productKey);
        } else {
            $this->addNewCartItem($product, $productKey, $isReturn);
        }

        // Now that the product is in the cart, update the changedQuantity array
        // This ensures the productKey exists in cartProducts before we reference it
        if (isset($this->cartProducts[$productKey])) {
            $this->changedQuantity[$productKey] = $this->cartProducts[$productKey]['cartQuantity'];
        }

        // Apply promotions to cart products
        $this->processPromotions();

        // Dispatch events first to ensure the product is properly added to the cart
        $this->dispatch("CartAdded", cart: $this->cartProducts, appliedPromotions: $this->appliedPromotions, promotionGroups: $this->promotionGroups);
        $this->dispatch("calculatingNumbers", cart: $this->cartProducts);
        $this->dispatch('productUpdated');
    }
    private function updateExistingCartItem($product, $productKey)
    {
        // For service products, allow unlimited quantity
        if ($product->type === ProductTypeEnum::SERVICE->value) {
            $this->cartProducts[$productKey]["cartQuantity"]++;
            $this->changedQuantity[$productKey] = $this->cartProducts[$productKey]["cartQuantity"];
            return;
        }

        // Commented out composite product quantity validation
        /*
        // For composite products, check component quantities
        if ($product->type === ProductTypeEnum::COMPOSITE->value) {
            $newCartQuantity = $this->cartProducts[$productKey]["cartQuantity"] + 1;
            $maxQuantity = $this->validateCompositeProductQuantity($product, $newCartQuantity);

            $this->cartProducts[$productKey]["cartQuantity"] = $maxQuantity;
            $this->changedQuantity[$productKey] = $maxQuantity;
            return;
        }

        // For regular products, maintain the existing quantity check
        if ($this->cartProducts[$productKey]["cartQuantity"] >= $product->quantity) {
            $this->cartProducts[$productKey]["cartQuantity"] = $product->quantity;
            $this->changedQuantity[$productKey] = $product->quantity;
            $this->showToast(Lang::get('messages.max_to_sell'). ' ' . $product->quantity);
            return;
        }
        */

        // Simply increment the quantity for all product types
        $this->cartProducts[$productKey]["cartQuantity"]++;
        $this->changedQuantity[$productKey] = $this->cartProducts[$productKey]["cartQuantity"];

        // Update fulfillment status after quantity change
        $this->updateFulfillmentStatus($productKey);
    }

    private function addNewCartItem($product, $productKey, $isReturn = false)
    {

        // Convert the product to array and ensure all required fields are present
        $productArray = $product->toArray();

        // Ensure price fields are set to "0" string if null to avoid BC math errors
        $priceFields = [
            'sell_price',
            'wholesale_price',
            'sell_price_tax_exclusive',
            'wholesale_price_tax_exclusive',
            'sell_price_tax_exclusive_rounded',
            'wholesale_price_tax_exclusive_rounded',
            'cost',
            'cost_tax_exclusive',
            'cost_tax_exclusive_rounded'
        ];

        foreach ($priceFields as $field) {
            $productArray[$field] = (string)($productArray[$field] ?? '0');
        }

        $this->cartProducts[$productKey] = $productArray;
        $this->cartProducts[$productKey]["cartQuantity"] = 1;
        $this->cartProducts[$productKey]["rank"] = $this->rank;

        // Mark as return item if applicable
        if ($isReturn) {
            $this->cartProducts[$productKey]["is_return"] = true;
            // Store the original cart_id without the -return suffix for reference
            $this->cartProducts[$productKey]["original_cart_id"] = str_replace('-return', '', $productKey);
            $this->cartProducts[$productKey]["invoice_type_performed"] = InvoiceTypePerformedEnum::RETURN->value;
        } else {
            $this->cartProducts[$productKey]["invoice_type_performed"] = InvoiceTypePerformedEnum::SALE->value;
        }

        // Add fulfillment status based on inventory
        $this->updateFulfillmentStatus($productKey);

        $this->changedQuantity[$productKey] = 1;
        $this->increaseRank();
    }

    public function ContinueCart($productId, $quantity, $isVariant = false, $isReturn = false)
    {
        // Handle variant products differently
        if ($isVariant) {
            $product = $this->getVariantProductById($productId);
            if (!$product || !$this->validateProduct($product)) {
                return;
            }
        } else {
            $product = $this->getProduct($productId,'id')->first();
            if (!$product || !$this->validateProduct($product)) {
                return;
            }
        }

        // For composite products, validate component quantities
        if ($product->type === ProductTypeEnum::COMPOSITE->value) {
            $quantity = $this->validateCompositeProductQuantity($product, $quantity);
            if ($quantity <= 0) {
                return;
            }
        }

        // Convert the product to array and ensure all required fields are present
        $productArray = $product->toArray();

        // For variant products, ensure we preserve the variant-specific properties
        if ($isVariant || (isset($product->is_variant) && $product->is_variant)) {
            $productArray['is_variant'] = true;
            $productArray['variant_id'] = $product->variant_id;
            $productArray['variant_name'] = $product->variant_name;
            $productArray['unique_id'] = $product->unique_id ?? ($product->id . '-' . $product->variant_id);
            $productArray['cart_id'] = $product->cart_id ?? ($product->id . '-' . $product->variant_id);
        } else {
            // For regular products
            $productArray['cart_id'] = $product->cart_id ?? $product->id;
        }

        // Determine the product key to use
        $productKey = $productArray['cart_id'];

        // For returned products, create a unique key by adding a suffix
        if ($isReturn) {
            $productKey = $productKey . '-return';
            $productArray['is_return'] = true;
            // Store the original cart_id without the -return suffix for reference
            $productArray['original_cart_id'] = str_replace('-return', '', $productKey);
            $productArray['invoice_type_performed'] = InvoiceTypePerformedEnum::RETURN->value;
        } else {
            $productArray['invoice_type_performed'] = InvoiceTypePerformedEnum::SALE->value;
        }

        // Ensure price fields are set to "0" string if null to avoid BC math errors
        $priceFields = [
            'sell_price',
            'wholesale_price',
            'sell_price_tax_exclusive',
            'wholesale_price_tax_exclusive',
            'sell_price_tax_exclusive_rounded',
            'wholesale_price_tax_exclusive_rounded',
            'cost',
            'cost_tax_exclusive',
            'cost_tax_exclusive_rounded'
        ];

        foreach ($priceFields as $field) {
            $productArray[$field] = (string)($productArray[$field] ?? '0');
        }

        $this->cartProducts[$productKey] = $productArray;
        $this->cartProducts[$productKey]["cartQuantity"] = $quantity;
        $this->cartProducts[$productKey]["rank"] = $this->rank;
        $this->increaseRank();

        // Dispatch setTaxRate event to update the cart tax rate based on all products
        $this->dispatch('setTaxRate', rate: 0);

        // Apply promotions to cart products
        $this->processPromotions();

        // Dispatch events first to ensure the product is properly added to the cart
        $this->dispatch("CartAdded", cart: $this->cartProducts, appliedPromotions: $this->appliedPromotions, promotionGroups: $this->promotionGroups);
        $this->dispatch("calculatingNumbers", cart: $this->cartProducts);

        // Now that the product is in the cart, update the changedQuantity array
        // This ensures the productKey exists in cartProducts before we reference it
        if (isset($this->cartProducts[$productKey])) {
            $this->changedQuantity[$productKey] = $quantity;
        }
    }

    public function CartAdded($cart)
    {
        usort($cart, function ($a, $b) {
            return $b['rank'] - $a['rank'];
        });

        $this->products = $cart;
    }

    public function increaseRank()
    {
        $this->rank++;
    }

    #[Computed]
    public function FindProducts()
    {
        // Check if the input matches the invoice pattern (S- followed by 8 characters)
        if (preg_match('/^S-[a-zA-Z0-9]{8}$/', $this->inputValue)) {
            // Switch to invoices tab and search for the invoice
            $this->dispatch('switchToInvoicesTab', ['search' => $this->inputValue]);
            $this->inputValue = ''; // Clear the search input
            $this->showList = false;
            $this->searchResults = [];
            return;
        }

        // First, search regular products (excluding main variant products)
        $regularProducts = MerchantsProduct::query()
            ->where('type', '!=', ProductTypeEnum::VARIANT->value) // Exclude main variant products
            ->where(function ($query) {
                $query->where('name', 'LIKE', '%' . $this->inputValue . '%')
                    ->orWhere('name_en', 'LIKE', '%' . $this->inputValue . '%')
                    ->orWhere('barcode', 'LIKE', '%' . $this->inputValue . '%');
            })
            ->with([
                'branchQuantities' => function ($query) {
                    $query->where('branch_id', $this->branchId);
                },
                'compositeComponents.componentProduct.branchQuantities' => function($query) {
                    $query->where('branch_id', $this->branchId);
                }
            ])
            ->take(5)
            ->get()
            ->map(function ($item) {
                // Add cart_id for regular products (same as id for non-variants)
                $item->cart_id = $item->id;

                $branchQuantity = $item->branchQuantities->first();
                $item->percentage = $item->tax->percentage ?? 0;

                // For composite products, calculate the maximum possible quantity based on components
                if ($item->type === ProductTypeEnum::COMPOSITE->value) {
                    $maxPossibleQuantity = null;

                    foreach ($item->compositeComponents as $component) {
                        $componentProduct = $component->componentProduct;
                        $componentBranchQuantity = $componentProduct->branchQuantities->first()?->quantity ?? 0;

                        // Skip service components when calculating composite quantity
                        if ($componentProduct->type === ProductTypeEnum::SERVICE->value) {
                            continue;
                        }

                        // Calculate how many composite products can be made with this component
                        $possibleQuantity = $component->quantity > 0 ? floor($componentBranchQuantity / $component->quantity) : 0;

                        if ($maxPossibleQuantity === null || $possibleQuantity < $maxPossibleQuantity) {
                            $maxPossibleQuantity = $possibleQuantity;
                        }
                    }

                    // Set the composite product quantity to the maximum possible quantity
                    $item->quantity = $maxPossibleQuantity ?? 0;
                } else {
                    // For regular products, use the branch quantity directly
                    $item->quantity = $branchQuantity?->quantity ?? 0;
                }

                return $item;
            });

        // Then, search variant products
        $variantProducts = MerchantProductVariant::query()
            ->whereHas('branchQuantities', function ($query) {
                $query->where('branch_id', $this->branchId);
            })
            ->where(function ($query) {
                // Search by barcode
                $query->where('barcode', 'LIKE', '%' . $this->inputValue . '%');

                // Search by associated product name
                $query->orWhereHas('product', function($q) {
                    $q->where('name', 'LIKE', '%' . $this->inputValue . '%')
                      ->orWhere('name_en', 'LIKE', '%' . $this->inputValue . '%');
                });

                // Search by variant options - this allows searching for option name parts
                $query->orWhereHas('variantAttributes.variantOption', function($q) {
                    $q->where('name', 'LIKE', '%' . $this->inputValue . '%')
                      ->orWhere('name_en', 'LIKE', '%' . $this->inputValue . '%');
                });
            })
            ->with([
                'product.tax' => function ($query) {
                    $query->select('id', 'percentage', 'code');
                },
                'variantAttributes.variantOption',
                'branchQuantities' => function($q) {
                    $q->where('branch_id', $this->branchId);
                }
            ])
            ->take(5)
            ->get()
            ->map(function ($variantProduct) {
                return $this->prepareVariantProduct($variantProduct);
            });

        // Combine the results, prioritizing exact barcode matches
        $exactBarcodeMatches = collect();
        $otherMatches = collect();

        // Process regular products
        foreach ($regularProducts as $product) {
            if ($product->barcode === $this->inputValue) {
                $exactBarcodeMatches->push($product);
            } else {
                $otherMatches->push($product);
            }
        }

        // Process variant products
        foreach ($variantProducts as $product) {
            if ($product->barcode === $this->inputValue) {
                $exactBarcodeMatches->push($product);
            } else {
                $otherMatches->push($product);
            }
        }

        // Combine exact matches first, then others, limited to 5 total
        $this->searchResults = $exactBarcodeMatches->concat($otherMatches)->take(5);

        $this->showList = count($this->searchResults) > 0 && strlen($this->inputValue) > 0;
        if($this->showList == false){
            $this->hideProductListAndClearSearch(true);
        }
    }

    public function EditQuantityInCart($cartId)
    {
        // Check if this is a variant product
        $isVariant = isset($this->cartProducts[$cartId]['is_variant']) && $this->cartProducts[$cartId]['is_variant'];

        if ($isVariant) {
            // For variant products, get the product using the variant ID
            $variantId = $this->cartProducts[$cartId]['variant_id'] ?? null;
            if (!$variantId) {
                return;
            }

            $product = $this->getVariantProductById($variantId);
            // Ensure the product has the same cart_id as the cart item
            $product->cart_id = $cartId;
        } else {
            // For regular products, proceed as before
            $productId = $this->cartProducts[$cartId]['id'] ?? $cartId;
            $product = $this->getProduct($productId,'id')->first();
            if ($product) {
                $product->cart_id = $cartId;
            }
        }

        if (!$product || !$this->validateProduct($product)) {
            return;
        }

        // Use cart_id as the key for changedQuantity
        $newQuantity = $this->changedQuantity[$cartId] ?? 0;
        $newQuantity = $this->sanitizeInput($newQuantity);

        // Check if this is a return product
        $isReturn = isset($this->cartProducts[$cartId]['is_return']) && $this->cartProducts[$cartId]['is_return'];

        if ($isReturn) {
            // Get the remaining returnable quantity if available, otherwise use original invoice quantity
            $remainingQuantity = $this->cartProducts[$cartId]['remaining_qty'] ?? 0;
            $originalInvoiceQuantity = $this->cartProducts[$cartId]['original_invoice_quantity'] ?? 0;

            // Use remaining quantity if available, otherwise fall back to original invoice quantity
            $maxReturnableQuantity = $remainingQuantity > 0 ? $remainingQuantity : $originalInvoiceQuantity;

            // For return products, we work with positive quantities in the UI but store as negative
            // So we need to validate the positive value against the maximum returnable quantity
            $positiveNewQuantity = abs($newQuantity);

            // Limit the return quantity to the maximum returnable quantity
            if ($maxReturnableQuantity > 0 && $positiveNewQuantity > $maxReturnableQuantity) {
                $positiveNewQuantity = $maxReturnableQuantity;
                $this->changedQuantity[$cartId] = $positiveNewQuantity;
                $this->showToast(Lang::get('messages.max_return_quantity') . ' ' . $maxReturnableQuantity);
            }

            // Store the absolute value for reference
            $this->cartProducts[$cartId]["absolute_return_qty"] = $positiveNewQuantity;

            // Convert back to negative for storage
            $newQuantity = -$positiveNewQuantity;

            // Ensure the invoice_type_performed is set to RETURN
            $this->cartProducts[$cartId]["invoice_type_performed"] = InvoiceTypePerformedEnum::RETURN->value;
        }

        // Check if this product has fulfillment from another branch
        if (isset($this->cartProducts[$cartId]['fulfillment_from_branch'])) {
            // Get the current branch quantity
            $currentBranchQuantity = $this->cartProducts[$cartId]['quantity'] ?? 0;

            // Get the fulfillment branch
            $fulfillmentBranchId = $this->cartProducts[$cartId]['fulfillment_from_branch']['branch_id'];

            // Find the maximum available quantity in the fulfillment branch
            $maxFulfillmentQuantity = 0;
            $branchData = MerchantBranch::with(['branchQuantities' => function($query) use ($product) {
                $query->where('product_id', $product->id);
            }])->find($fulfillmentBranchId);

            if ($branchData && $branchData->branchQuantities->isNotEmpty()) {
                $maxFulfillmentQuantity = $branchData->branchQuantities->first()->quantity ?? 0;
            }

            // Calculate the maximum allowed quantity (current branch + max fulfillment)
            $maxAllowedQuantity = $currentBranchQuantity + $maxFulfillmentQuantity;

            // If the new quantity exceeds the maximum allowed, limit it
            if ($newQuantity > $maxAllowedQuantity) {
                $newQuantity = $maxAllowedQuantity;
                $this->changedQuantity[$cartId] = $newQuantity;
                $this->showToast(Lang::get('messages.max_quantity_with_fulfillment') . ' ' . $maxAllowedQuantity);
            }

            // Update the fulfillment quantity based on the new cart quantity
            $newFulfillmentQuantity = max(0, $newQuantity - $currentBranchQuantity);
            if ($newFulfillmentQuantity > $maxFulfillmentQuantity) {
                $newFulfillmentQuantity = $maxFulfillmentQuantity;
            }

            // Update the fulfillment quantity in the cart
            $this->cartProducts[$cartId]['fulfillment_from_branch']['quantity'] = $newFulfillmentQuantity;
        }

        // For return products, we allow negative quantities
        // For regular products, ensure quantity is positive
        if ($isReturn || $newQuantity > 0) {
            // Update the quantity in the cart
            $this->cartProducts[$cartId]["cartQuantity"] = round($newQuantity, 0, PHP_ROUND_HALF_DOWN);

            // Check if this is a promoted product
            $isPromotedProduct = isset($this->cartProducts[$cartId]['has_promotion']) && $this->cartProducts[$cartId]['has_promotion'];

            // If it's a promoted product, we need to check if we need to update the non-promoted version
            if ($isPromotedProduct && isset($this->cartProducts[$cartId]['promotion_id'])) {
                $promotionId = $this->cartProducts[$cartId]['promotion_id'];

                // Find the original product ID (remove the promotion ID suffix)
                $originalCartId = str_replace('-' . $promotionId, '', $cartId);

                // If the original product exists, update its changedQuantity
                if (isset($this->cartProducts[$originalCartId])) {
                    $this->changedQuantity[$originalCartId] = $this->cartProducts[$originalCartId]['cartQuantity'];
                }
            }

            // Update fulfillment status immediately but skip re-render
            $this->updateFulfillmentStatusSilently($cartId);

            $this->dispatch("CartAdded", cart: $this->cartProducts);
            $this->dispatch("calculatingNumbers", cart: $this->cartProducts);
        } else {
            // If quantity is zero or negative, remove the product from cart and changedQuantity array
            unset($this->cartProducts[$cartId]);
            unset($this->changedQuantity[$cartId]);
        }

        // Process promotions before dispatching events - do this regardless of quantity
        $this->processPromotions();

        // Now dispatch events with the updated cart that includes promotion changes
        $this->dispatch("CartAdded", cart: $this->cartProducts, appliedPromotions: $this->appliedPromotions, promotionGroups: $this->promotionGroups);
        $this->dispatch("calculatingNumbers", cart: $this->cartProducts);

        // Commented out quantity validation
        /*
        // Skip quantity validation for service products
        if ($product->type === ProductTypeEnum::SERVICE->value) {
            if ($newQuantity > 0) {
                $this->cartProducts[$cartId]["cartQuantity"] = round($newQuantity, 0, PHP_ROUND_HALF_DOWN);
                $this->dispatch("CartAdded", cart: $this->cartProducts);
                $this->dispatch("calculatingNumbers", cart: $this->cartProducts);
            }
            return;
        }

        // Handle composite products
        if ($product->type === ProductTypeEnum::COMPOSITE->value) {
            $newQuantity = $this->validateCompositeProductQuantity($product, $newQuantity);
            $this->changedQuantity[$cartId] = $newQuantity;
        } else {
            // Regular product quantity validation
            if ($newQuantity > $product->quantity) {
                $newQuantity = $product->quantity;
                $this->changedQuantity[$cartId] = $newQuantity;
                $this->showToast(Lang::get('messages.max_to_sell').' '.$product->quantity);
            }
        }

        if ($newQuantity > 0) {
            $this->cartProducts[$cartId]["cartQuantity"] = round($newQuantity, 0, PHP_ROUND_HALF_DOWN);

            // Apply promotions to cart products
            $this->processPromotions();

            $this->dispatch("CartAdded", cart: $this->cartProducts, appliedPromotions: $this->appliedPromotions);
            $this->dispatch("calculatingNumbers", cart: $this->cartProducts);
        }
        */
    }

    public function RemoveProduct($id)
    {
        // Also remove from changedQuantity array to prevent issues with variants
        unset($this->changedQuantity[$id]);
        unset($this->products[$id]);
        unset($this->cartProducts[$id]);

        if (count($this->cartProducts) == 0) {
            $this->taxRate = null;
            $this->dispatch('setTaxRate', rate: 0);
        } else {
            // Trigger tax rate recalculation based on remaining products
            $this->dispatch('setTaxRate', rate: 0);
        }

        // Apply promotions to cart products
        $this->processPromotions();

        $this->dispatch("CartAdded", cart: $this->cartProducts, appliedPromotions: $this->appliedPromotions, promotionGroups: $this->promotionGroups);
        $this->dispatch("calculatingNumbers", cart: $this->cartProducts);
    }

    public function findCustomer()
    {
        $this->findCustomerQuery();

        $this->showCustomerList = count($this->searchCustomerResults) > 0 && strlen($this->inputCustomerValue) > 0;

    }

    #[Computed]
    private function findCustomerQuery()
    {
        return $this->searchCustomerResults = MerchantCustomer::query()
            ->where('merchant_id', $this->user->merchant_id)
            ->where('name', 'LIKE', '%' . $this->inputCustomerValue . '%')
            ->take(5)
            ->get();
    }

    public function getCustomers()
    {
        if (!isset($this->chosenCustomer['id']) || $this->chosenCustomer['id'] <= 0) {
            $this->customers = $this->getCustomerQuery();
        } else {
            $this->customers = [];
        }
    }

    private function getCustomerQuery(){
        return MerchantCustomer::query()
            ->where('merchant_id', $this->user->merchant_id)
            ->orderBy('created_at', 'desc') // Show most recent customers first
            ->limit(5)
            ->get();
    }



    public function customerAdded(MerchantCustomer $customer, $name)
    {
        if ($this->plan->is_pos_adding_customer_supported) {

            if ($this->user->isZatcaEnabled && !is_null($customer?->vat_number)) {

                $address = $customer->address()->first();

                if (
                    !preg_match('/^3.*3$/', $customer?->vat_number) ||
                    !$address ||
                    is_null($address?->state_id) ||
                    is_null($address?->city_id) ||
                    is_null($address?->street) ||
                    is_null($address?->postal_code) ||
                    is_null($address?->building_number) ||
                    is_null($address?->district)
                )
                {
                    $this->showToast(Lang::get('messages.cannot_add_customer'));
                    return false;
                }
            }

            // Reset any existing fulfillment arrangements if customer is changing
            if (isset($this->chosenCustomer['id']) && $this->chosenCustomer['id'] > 0 && $this->chosenCustomer['id'] != $customer->id) {
                // Reset fulfillment arrangements for all products in the cart
                foreach (array_keys($this->cartProducts) as $cartId) {
                    // Remove branch fulfillment
                    if (isset($this->cartProducts[$cartId]['fulfillment_from_branch'])) {
                        unset($this->cartProducts[$cartId]['fulfillment_from_branch']);
                    }

                    // Remove delivery fulfillment
                    if (isset($this->cartProducts[$cartId]['fulfillment_by_delivery'])) {
                        unset($this->cartProducts[$cartId]['fulfillment_by_delivery']);
                    }

                    // Reset fulfillment flag
                    if (isset($this->cartProducts[$cartId]['has_fulfillment'])) {
                        unset($this->cartProducts[$cartId]['has_fulfillment']);
                    }
                }

                // Update cart to reflect changes
                $this->dispatch("CartAdded", cart: $this->cartProducts);

                // Show toast message
                $this->showToast(Lang::get('messages.fulfillment_reset_customer_changed'));
            }

            $this->dispatch("CustomerAdded", customer: $customer);
            $this->chosenCustomer['id'] = $customer->id;
            $this->chosenCustomer['name'] = $name;
            $this->searchCustomerResults = [];
            $this->inputCustomerValue = '';

            // Check if there's a pending action that requires a customer
            if ($this->pendingAction && $this->pendingActionData) {
                $cartId = $this->pendingActionData;
                $pendingAction = $this->pendingAction;

                // Clear the pending action
                $this->pendingAction = null;
                $this->pendingActionData = null;

                // Continue with the appropriate fulfillment action
                if ($pendingAction === 'delivery_fulfillment') {
                    $this->openDeliveryFulfillmentModal($cartId);
                } elseif ($pendingAction === 'branch_fulfillment') {
                    $this->openBranchFulfillmentModal($cartId);
                }
            }
        }
    }

    /**
     * Handle a newly created customer from the PosCustomerCreate component
     */
    public function customerCreated($customer)
    {
        // Find the customer by ID to get a proper MerchantCustomer object
        $customerModel = MerchantCustomer::find($customer['id']);

        if ($customerModel) {
            // Use the existing customerAdded method to handle the new customer
            $this->customerAdded($customerModel, $customerModel->name);
        }
    }

    public function customerRemove()
    {
        $this->dispatch("CustomerAdded", customer: 0);
        $this->chosenCustomer['id'] = 0;
        $this->chosenCustomer['name'] = '';

        // Reset fulfillment arrangements for all products in the cart
        foreach (array_keys($this->cartProducts) as $cartId) {
            // Remove branch fulfillment
            if (isset($this->cartProducts[$cartId]['fulfillment_from_branch'])) {
                unset($this->cartProducts[$cartId]['fulfillment_from_branch']);
            }

            // Remove delivery fulfillment
            if (isset($this->cartProducts[$cartId]['fulfillment_by_delivery'])) {
                unset($this->cartProducts[$cartId]['fulfillment_by_delivery']);
            }

            // Reset fulfillment flag
            if (isset($this->cartProducts[$cartId]['has_fulfillment'])) {
                unset($this->cartProducts[$cartId]['has_fulfillment']);
            }
        }

        // Update cart to reflect changes
        $this->dispatch("CartAdded", cart: $this->cartProducts);

        // Show toast message if there were fulfillment arrangements
        if (count($this->cartProducts) > 0) {
            $this->showToast(Lang::get('messages.fulfillment_reset_customer_removed'));
        }
    }

    public function clear2()
    {
        if (empty($this->products) && strlen($this->inputValue) == 0) return;
        $this->products = [];
        $this->cartProducts = [];
        $this->appliedPromotions = [];
        $this->activePromotions = [];
        $this->taxRate = null;
        $this->hideProductListAndClearSearch();
    }

    public function wholesaleAction()
    {
        if ($this->plan->is_pos_wholesale_price_supported) {
            $this->dispatch('wholesaleStatus', status: $this->wholesale);
        } else {
            $this->wholesale = false;
        }
    }

    public function chooseProductDetail($key)
    {
        $this->productDetailKey = $key[0];
        $this->productBranchQuantities = $this->chooseProductDetailQuery($key);
    }

    #[Computed]
    private function chooseProductDetailQuery($key)
    {
        // Check if this is a variant product
        $isVariant = isset($this->products[$key[0]]['is_variant']) && $this->products[$key[0]]['is_variant'];

        if ($isVariant) {
            // For variant products, get the specific variant data
            $variantId = $this->products[$key[0]]['variant_id'] ?? null;
            if ($variantId) {
                $variantProduct = MerchantProductVariant::where('id', $variantId)
                    ->with([
                        'product',
                        'branchQuantities' => function($query) {
                            $query->with('merchantBranch');
                        }
                    ])
                    ->first();

                if ($variantProduct) {
                    // Transform the variant's branch quantities to match the expected format
                    $branchQuantities = $variantProduct->branchQuantities->map(function($branchQuantity) {
                        $branchQuantity->branch = $branchQuantity->merchantBranch;
                        return $branchQuantity;
                    });

                    return $branchQuantities;
                }
            }
        }

        // For regular products, proceed as before
        $branchQuantities = MerchantProductBranchQuantity::query()
            ->where('product_id', $key[1])
            ->with([
                'merchantBranch',
                'product.compositeComponents.componentProduct.branchQuantities' => function($query) {
                    // Don't filter by branch_id here, we need quantities for all branches
                    $query->whereIn('branch_id', function($subquery) {
                        $subquery->select('branch_id')
                            ->from('merchant_product_branch_quantities');
                    });
                }
            ])
            ->get();

        // Calculate max possible quantities for composite products
        $branchQuantities->each(function ($branchQuantity) {
            if ($branchQuantity->product->type === ProductTypeEnum::COMPOSITE->value) {
                $maxPossibleQuantity = null;

                foreach ($branchQuantity->product->compositeComponents as $component) {
                    $componentProduct = $component->componentProduct;
                    // Find the branch quantity for this specific branch
                    $componentBranchQuantity = $componentProduct->branchQuantities
                        ->firstWhere('branch_id', $branchQuantity->branch_id)?->quantity ?? 0;

                    $possibleQuantity = floor($componentBranchQuantity / $component->quantity);

                    if ($maxPossibleQuantity === null || $possibleQuantity < $maxPossibleQuantity) {
                        $maxPossibleQuantity = $possibleQuantity;
                    }
                }

                // Add the calculated max possible quantity to the branch quantity object
                $branchQuantity->max_possible_quantity = $maxPossibleQuantity ?? 0;
            } else {
                $branchQuantity->max_possible_quantity = $branchQuantity->quantity;
            }
        });

        return $branchQuantities;
    }

    private function showToast($message, $type = 'error')
    {
        Log::info("Showing toast: " . $message . " (type: " . $type . ")");

        if ($type === 'success') {
            $this->dispatch('toast-success', message: $message);
        } else {
            $this->dispatch('toast', message: $message);
        }
    }

    private function convertArabicToEnglish($input) {
        $arabicToEnglishMap = [
            '٠' => '0', '١' => '1', '٢' => '2', '٣' => '3', '٤' => '4',
            '٥' => '5', '٦' => '6', '٧' => '7', '٨' => '8', '٩' => '9'
        ];

        return strtr($input, $arabicToEnglishMap);
    }

    private function sanitizeInput($input) {
        // Remove any non-numeric and non-decimal characters
        $numericValue = preg_replace('/[^0-9.٠-٩]/u', '', $input);

        // Convert Arabic numerals to English numerals
        $numericValue = $this->convertArabicToEnglish($numericValue);

        return $numericValue;
    }

    /**
     * Update fulfillment status without triggering component re-render
     * This prevents interference with active typing
     *
     * @param string $cartId The cart ID of the product
     */
    public function updateFulfillmentStatusSilently($cartId)
    {
        // Update the fulfillment status in the background
        $this->updateFulfillmentStatus($cartId);

        // Skip the render to prevent input interference
        $this->skipRender();
    }

    /**
     * Update the fulfillment status of a product in the cart
     *
     * @param string $cartId The cart ID of the product
     */
    public function updateFulfillmentStatus($cartId)
    {
        // Skip for products that don't exist in cart
        if (!isset($this->cartProducts[$cartId])) {
            return;
        }

        // Skip for service products
        if (isset($this->cartProducts[$cartId]['type']) && $this->cartProducts[$cartId]['type'] === ProductTypeEnum::SERVICE->value) {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Available';
            return;
        }

        // Handle composite products
        if (isset($this->cartProducts[$cartId]['type']) && $this->cartProducts[$cartId]['type'] === ProductTypeEnum::COMPOSITE->value) {
            $this->updateCompositeProductFulfillmentStatus($cartId);
            return;
        }

        // For regular products, compare requested quantity with available quantity
        $cartQuantity = $this->cartProducts[$cartId]['cartQuantity'] ?? 0;
        $availableQuantity = $this->cartProducts[$cartId]['quantity'] ?? 0;

        if ($availableQuantity <= 0) {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Not Available';
        } elseif ($availableQuantity < $cartQuantity) {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Partially Fulfillable';
            // Calculate the partial quantity that can be fulfilled
            $this->cartProducts[$cartId]['partial_quantity'] = $availableQuantity;
        } else {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Available';
        }
    }

    /**
     * Updates the fulfillment status for composite products by checking component availability
     *
     * @param string $cartId The cart item identifier
     * @return void
     */
    private function updateCompositeProductFulfillmentStatus($cartId)
    {
        $productId = $this->cartProducts[$cartId]['id'] ?? 0;
        if (!$productId) return;

        // Get the product with its components
        $product = MerchantsProduct::with(['compositeComponents.componentProduct.branchQuantities' => function($query) {
            $query->where('branch_id', $this->branchId);
        }])->find($productId);

        if (!$product || !$product->compositeComponents) return;

        $cartQuantity = $this->cartProducts[$cartId]['cartQuantity'] ?? 0;
        $canFulfillFully = true;
        $canFulfillPartially = false;
        $maxPossibleQuantity = null;

        foreach ($product->compositeComponents as $component) {
            $componentProduct = $component->componentProduct;
            $branchQuantity = $componentProduct->branchQuantities->first()?->quantity ?? 0;
            $requiredQuantity = $component->quantity * $cartQuantity;

            // Skip service components when calculating composite quantity
            if ($componentProduct->type === ProductTypeEnum::SERVICE->value) {
                continue;
            }

            // Calculate how many composite products can be made with this component
            $possibleQuantity = $component->quantity > 0 ? floor($branchQuantity / $component->quantity) : 0;

            if ($maxPossibleQuantity === null || $possibleQuantity < $maxPossibleQuantity) {
                $maxPossibleQuantity = $possibleQuantity;
            }

            if ($branchQuantity <= 0) {
                $canFulfillFully = false;
                $canFulfillPartially = false;
                break;
            } elseif ($branchQuantity < $requiredQuantity) {
                $canFulfillFully = false;
                $canFulfillPartially = true;
            }
        }

        // Update the cart product's quantity to reflect the maximum possible quantity
        $this->cartProducts[$cartId]['quantity'] = $maxPossibleQuantity ?? 0;

        if (!$canFulfillFully && !$canFulfillPartially) {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Not Available';
        } elseif (!$canFulfillFully && $canFulfillPartially) {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Partially Fulfillable';
            // Calculate the partial quantity that can be fulfilled
            $this->cartProducts[$cartId]['partial_quantity'] = $maxPossibleQuantity ?? 0;
        } else {
            $this->cartProducts[$cartId]['fulfillment_status'] = 'Available';
        }
    }

    /**
     * Open the branch fulfillment modal for a product
     *
     * @param string $cartId The cart ID of the product
     * @return void
     */
    public function openBranchFulfillmentModal($cartId)
    {
        // Set the selected product for fulfillment
        $this->selectedFulfillmentProduct = $this->cartProducts[$cartId] ?? null;

        if (!$this->selectedFulfillmentProduct) {
            return;
        }

        // Check if a customer is selected
        if (!isset($this->chosenCustomer['id']) || $this->chosenCustomer['id'] <= 0) {
            // Set pending action to continue after customer selection
            $this->pendingAction = 'branch_fulfillment';
            $this->pendingActionData = $cartId;

            // Load customers and open the customer modal
            $this->getCustomers();
            $this->dispatch('open-customer-modal');
            return;
        }

        // Get available branches with quantity information
        $productId = $this->selectedFulfillmentProduct['id'];
        $isComposite = isset($this->selectedFulfillmentProduct['type']) &&
                      $this->selectedFulfillmentProduct['type'] === ProductTypeEnum::COMPOSITE->value;

        if ($isComposite) {
            // For composite products, calculate availability based on components
            $product = MerchantsProduct::with(['compositeComponents.componentProduct.branchQuantities'])
                ->find($productId);

            $this->branchesForFulfillment = MerchantBranch::all()->map(function($branch) use ($product) {
                $maxPossibleQuantity = null;

                if ($product && $product->compositeComponents) {
                    foreach ($product->compositeComponents as $component) {
                        $componentProduct = $component->componentProduct;

                        // Skip service components when calculating composite quantity
                        if ($componentProduct->type === ProductTypeEnum::SERVICE->value) {
                            continue;
                        }

                        $componentBranchQuantity = $componentProduct->branchQuantities
                            ->firstWhere('branch_id', $branch->id)?->quantity ?? 0;

                        // Calculate how many composite products can be made with this component
                        $possibleQuantity = $component->quantity > 0 ? floor($componentBranchQuantity / $component->quantity) : 0;

                        if ($maxPossibleQuantity === null || $possibleQuantity < $maxPossibleQuantity) {
                            $maxPossibleQuantity = $possibleQuantity;
                        }
                    }
                }

                return [
                    'id' => $branch->id,
                    'name' => $branch->branch_name,
                    'quantity' => $maxPossibleQuantity ?? 0
                ];
            })->toArray();
        } else {
            // For regular products, use the existing logic
            $this->branchesForFulfillment = MerchantBranch::with(['branchQuantities' => function($query) use ($productId) {
                $query->where('product_id', $productId);
            }])->get()->map(function($branch) {
                return [
                    'id' => $branch->id,
                    'name' => $branch->branch_name,
                    'quantity' => $branch->branchQuantities->first()?->quantity ?? 0
                ];
            })->toArray();
        }

        // Filter out branches with zero quantity and the current branch
        $this->branchesForFulfillment = array_filter($this->branchesForFulfillment, function($branch) {
            return $branch['quantity'] > 0 && $branch['id'] != $this->branchId;
        });

        // Check if there are any branches available with stock
        if (empty($this->branchesForFulfillment)) {
            $this->showToast(Lang::get('messages.no_branches_with_stock'));
            return;
        }

        // Reset fulfillment form
        $this->selectedFulfillmentBranch = '';
        $this->fulfillmentQuantity = 1;
        $this->maxFulfillmentQuantity = 0;

        // Open the modal
        $this->dispatch('open-branch-fulfillment-modal');
    }

    /**
     * Handle branch selection change in fulfillment modal
     */
    public function updatedSelectedFulfillmentBranch()
    {
        // Find selected branch
        $selectedBranch = collect($this->branchesForFulfillment)
            ->firstWhere('id', $this->selectedFulfillmentBranch);

        // Update maximum available quantity
        $this->maxFulfillmentQuantity = $selectedBranch ? $selectedBranch['quantity'] : 0;

        // Set default fulfillment quantity
        $missingQuantity = $this->selectedFulfillmentProduct['cartQuantity'] - ($this->selectedFulfillmentProduct['quantity'] ?? 0);
        $this->fulfillmentQuantity = min($missingQuantity, $this->maxFulfillmentQuantity);

        // Update the cart quantity
        if ($this->selectedFulfillmentProduct && $this->selectedFulfillmentBranch) {
            $cartId = $this->selectedFulfillmentProduct['cart_id'] ?? $this->selectedFulfillmentProduct['id'];

            if (isset($this->cartProducts[$cartId])) {
                // Get the current quantity in the current branch
                $currentBranchQuantity = $this->selectedFulfillmentProduct['quantity'] ?? 0;

                // Calculate the new cart quantity: current branch quantity + fulfillment quantity
                $newCartQuantity = $currentBranchQuantity + $this->fulfillmentQuantity;

                // Update the cart quantity
                $this->cartProducts[$cartId]['cartQuantity'] = $newCartQuantity;

                // Update the changedQuantity array to reflect the new cart quantity
                $this->changedQuantity[$cartId] = $newCartQuantity;

                // Update the fulfillment information
                $this->cartProducts[$cartId]['fulfillment_from_branch'] = [
                    'branch_id' => $this->selectedFulfillmentBranch,
                    'quantity' => $this->fulfillmentQuantity,
                ];

                // Mark that fulfillment has been arranged
                $this->cartProducts[$cartId]['has_fulfillment'] = true;

                // Update the cart
                $this->dispatch("CartAdded", cart: $this->cartProducts);
            }
        }
    }

    /**
     * Handle changes to the fulfillment quantity input
     */
    public function updatedFulfillmentQuantity()
    {
        // Sanitize the input to ensure it's a valid number
        $this->fulfillmentQuantity = $this->sanitizeInput($this->fulfillmentQuantity);

        // Ensure the quantity is at least 1
        if ($this->fulfillmentQuantity < 1) {
            $this->fulfillmentQuantity = 1;
        }

        // Limit the quantity to the maximum available
        if ($this->fulfillmentQuantity > $this->maxFulfillmentQuantity) {
            $this->fulfillmentQuantity = $this->maxFulfillmentQuantity;
        }

        // If we have a selected product, update the cart quantity
        if ($this->selectedFulfillmentProduct) {
            $cartId = $this->selectedFulfillmentProduct['cart_id'] ?? $this->selectedFulfillmentProduct['id'];

            // Update the cart quantity to match the fulfillment quantity
            if (isset($this->cartProducts[$cartId])) {
                // Get the current quantity in the current branch
                $currentBranchQuantity = $this->selectedFulfillmentProduct['quantity'] ?? 0;

                // Calculate the new cart quantity: current branch quantity + fulfillment quantity
                $newCartQuantity = $currentBranchQuantity + $this->fulfillmentQuantity;

                // Update the cart quantity
                $this->cartProducts[$cartId]['cartQuantity'] = $newCartQuantity;

                // Update the changedQuantity array to reflect the new cart quantity
                $this->changedQuantity[$cartId] = $newCartQuantity;

                // Update the fulfillment information
                $this->cartProducts[$cartId]['fulfillment_from_branch'] = [
                    'branch_id' => $this->selectedFulfillmentBranch,
                    'quantity' => $this->fulfillmentQuantity,
                ];

                // Mark that fulfillment has been arranged
                $this->cartProducts[$cartId]['has_fulfillment'] = true;

                // Update the cart
                $this->dispatch("CartAdded", cart: $this->cartProducts);
            }
        }
    }

    /**
     * Confirm branch fulfillment
     */
    public function confirmBranchFulfillment()
    {
        // Validate input
        if (!$this->selectedFulfillmentProduct || !$this->selectedFulfillmentBranch || $this->fulfillmentQuantity <= 0) {
            $this->showToast(Lang::get('messages.please_complete_all_fields'));
            return;
        }

        // Ensure a customer is selected
        if (!isset($this->chosenCustomer['id']) || $this->chosenCustomer['id'] <= 0) {
            // Set pending action to continue after customer selection
            $cartId = $this->selectedFulfillmentProduct['cart_id'] ?? $this->selectedFulfillmentProduct['id'];
            $this->pendingAction = 'branch_fulfillment';
            $this->pendingActionData = $cartId;

            // Load customers and open the customer modal
            $this->getCustomers();
            $this->dispatch('open-customer-modal');
            return;
        }

        // Ensure the fulfillment quantity is valid
        if ($this->fulfillmentQuantity > $this->maxFulfillmentQuantity) {
            $this->fulfillmentQuantity = $this->maxFulfillmentQuantity;
        }

        $cartId = $this->selectedFulfillmentProduct['cart_id'] ?? $this->selectedFulfillmentProduct['id'];

        // Get the current quantity in the current branch
        $currentBranchQuantity = $this->selectedFulfillmentProduct['quantity'] ?? 0;

        // Calculate the new cart quantity: current branch quantity + fulfillment quantity
        $newCartQuantity = $currentBranchQuantity + $this->fulfillmentQuantity;

        // Update the cart quantity
        $this->cartProducts[$cartId]['cartQuantity'] = $newCartQuantity;

        // Update the changedQuantity array to reflect the new cart quantity
        $this->changedQuantity[$cartId] = $newCartQuantity;

        // Update product fulfillment status
        $this->cartProducts[$cartId]['fulfillment_from_branch'] = [
            'branch_id' => $this->selectedFulfillmentBranch,
            'quantity' => $this->fulfillmentQuantity,
        ];

        // Mark that fulfillment has been arranged, but don't change the status
        $this->cartProducts[$cartId]['has_fulfillment'] = true;

        // Close modal
        $this->dispatch('close-branch-fulfillment-modal');

        // Update cart
        $this->dispatch("CartAdded", cart: $this->cartProducts);
        $this->showToast(Lang::get('messages.fulfillment_confirmed'), 'success');
    }

    /**
     * Open delivery fulfillment modal for a product
     *
     * @param string $cartId The cart ID of the product
     * @return void
     */
    public function openDeliveryFulfillmentModal($cartId)
    {
        // Set the selected product for fulfillment
        $this->selectedFulfillmentProduct = $this->cartProducts[$cartId] ?? null;

        if (!$this->selectedFulfillmentProduct) {
            return;
        }

        // Ensure a customer is selected
        if (!isset($this->chosenCustomer['id']) || $this->chosenCustomer['id'] <= 0) {
            // Set pending action to continue after customer selection
            $this->pendingAction = 'delivery_fulfillment';
            $this->pendingActionData = $cartId;

            // Load customers and open the customer modal
            $this->getCustomers();
            $this->dispatch('open-customer-modal');
            return;
        }

        // Get customer address from profile
        $customer = MerchantCustomer::with('address')->find($this->chosenCustomer['id']);

        if (!$customer || !$customer->address) {
            $this->showToast(Lang::get('messages.customer_has_no_address'));
            return;
        }

        // Get formatted address from customer profile
        $address = $customer->address;
        $formattedAddress = '';

        if ($address->building_number) {
            $formattedAddress .= $address->building_number . ', ';
        }

        if ($address->street) {
            $formattedAddress .= $address->street . ', ';
        }

        if ($address->district) {
            $formattedAddress .= $address->district . ', ';
        }

        $cityState = [];
        if ($address->city_id) {
            $city = \App\Models\City::find($address->city_id);
            if ($city) {
                $cityState[] = $city->name;
            }
        }

        if ($address->state_id) {
            $state = \App\Models\State::find($address->state_id);
            if ($state) {
                $cityState[] = $state->name;
            }
        }

        if (!empty($cityState)) {
            $formattedAddress .= implode(', ', $cityState) . ', ';
        }

        if ($address->postal_code) {
            $formattedAddress .= $address->postal_code;
        }

        // Remove trailing comma and space if present
        $formattedAddress = rtrim($formattedAddress, ', ');

        // Set delivery address from customer profile
        $this->deliveryAddress = $formattedAddress;

        // Reset delivery notes
        $this->deliveryNotes = '';

        // Open the modal
        $this->dispatch('open-delivery-fulfillment-modal');
    }

    /**
     * Confirm delivery fulfillment
     */
    public function confirmDeliveryFulfillment()
    {
        // Validate input
        if (!$this->selectedFulfillmentProduct) {
            $this->showToast(Lang::get('messages.please_complete_all_fields'));
            return;
        }

        // Ensure a customer is selected
        if (!isset($this->chosenCustomer['id']) || $this->chosenCustomer['id'] <= 0) {
            // Set pending action to continue after customer selection
            $cartId = $this->selectedFulfillmentProduct['cart_id'] ?? $this->selectedFulfillmentProduct['id'];
            $this->pendingAction = 'delivery_fulfillment';
            $this->pendingActionData = $cartId;

            // Load customers and open the customer modal
            $this->getCustomers();
            $this->dispatch('open-customer-modal');
            return;
        }

        $cartId = $this->selectedFulfillmentProduct['cart_id'] ?? $this->selectedFulfillmentProduct['id'];

        // Update product fulfillment status
        $this->cartProducts[$cartId]['fulfillment_by_delivery'] = [
            'customer_id' => $this->chosenCustomer['id'],
            'address' => $this->deliveryAddress,
            'notes' => $this->deliveryNotes
        ];

        // Mark that fulfillment has been arranged, but don't change the status
        $this->cartProducts[$cartId]['has_fulfillment'] = true;

        // Close modal
        $this->dispatch('close-delivery-fulfillment-modal');

        // Update cart
        $this->dispatch("CartAdded", cart: $this->cartProducts);
        $this->showToast(Lang::get('messages.delivery_scheduled'), 'success');
    }

    /**
     * Create fulfillment orders from cart data after invoice creation
     *
     * @param MerchantInvoice $invoice The created invoice
     * @param array|null $products Products array with invoice_product_id values
     * @return array Array of created fulfillment orders
     */
    /**
     * Add return products from invoice to the cart
     *
     * @param array $products Array of products from the invoice
     * @return void
     */
    public function addReturnProductsFromInvoice($products)
    {
        if (empty($products) || !is_array($products)) {
            return;
        }

        // Reset the cart first to ensure we're starting fresh
        $this->resetCart();

        // Get the original invoice ID if available
        $originalInvoiceId = null;
        if (isset($products[0]['invoice_id'])) {
            $originalInvoiceId = $products[0]['invoice_id'];
        }

        // Get the customer ID if available
        $customerId = null;
        if (isset($products[0]['customer_id'])) {
            $customerId = $products[0]['customer_id'];
        }

        // Process each product
        foreach ($products as $product) {
            // Skip invalid products
            if (!isset($product['product_id']) || !isset($product['return_qty'])) {
                continue;
            }

            // Get the original quantity from the invoice
            $originalQuantity = isset($product['cart_quantity']) ? (int)$product['cart_quantity'] : 0;

            // Get the remaining quantity that can be returned (if provided)
            $remainingQuantity = isset($product['remaining_qty']) ? (int)$product['remaining_qty'] : $originalQuantity;

            // Limit return quantity to the remaining quantity
            $returnQty = (int)$product['return_qty'];
            if ($remainingQuantity > 0 && $returnQty > $remainingQuantity) {
                $returnQty = $remainingQuantity;
                $product['return_qty'] = $returnQty;
            }

            // Skip if return quantity is zero or negative
            if ($returnQty <= 0) {
                continue;
            }

            // For variant products, handle differently
            if (isset($product['is_variant']) && $product['is_variant']) {
                $variantId = $product['variant_id'] ?? null;
                if ($variantId) {
                    \Illuminate\Support\Facades\Log::debug('Processing variant product with ID: ' . $variantId);
                    $variantProduct = $this->getVariantProductById($variantId, true);

                    if ($variantProduct && $this->validateProduct($variantProduct)) {
                        \Illuminate\Support\Facades\Log::debug('Adding variant product to cart directly');

                        // Create a unique key for the return product
                        $productKey = $variantProduct->cart_id . '-return';

                        // Convert the product to array and ensure all required fields are present
                        $productArray = $variantProduct->toArray();

                        // Override with original invoice pricing data to preserve promotional prices
                        if (isset($product['price_tax_inclusive'])) {
                            $productArray['sell_price'] = (string)$product['price_tax_inclusive'];
                        }
                        if (isset($product['price_tax_exclusive'])) {
                            $productArray['sell_price_tax_exclusive'] = (string)$product['price_tax_exclusive'];
                        }
                        if (isset($product['price_tax_exclusive_rounded'])) {
                            $productArray['sell_price_tax_exclusive_rounded'] = (string)$product['price_tax_exclusive_rounded'];
                        }
                        if (isset($product['cost_tax_inclusive'])) {
                            $productArray['cost'] = (string)$product['cost_tax_inclusive'];
                        }
                        if (isset($product['cost_tax_exclusive'])) {
                            $productArray['cost_tax_exclusive'] = (string)$product['cost_tax_exclusive'];
                            $productArray['cost_tax_exclusive_rounded'] = (string)$product['cost_tax_exclusive'];
                        }

                        // Ensure price fields are set to "0" string if null to avoid BC math errors
                        $priceFields = [
                            'sell_price',
                            'wholesale_price',
                            'sell_price_tax_exclusive',
                            'wholesale_price_tax_exclusive',
                            'sell_price_tax_exclusive_rounded',
                            'wholesale_price_tax_exclusive_rounded',
                            'cost',
                            'cost_tax_exclusive',
                            'cost_tax_exclusive_rounded'
                        ];

                        foreach ($priceFields as $field) {
                            $productArray[$field] = (string)($productArray[$field] ?? '0');
                        }

                        // Add to cart with return flag and specified quantity (as negative)
                        $this->cartProducts[$productKey] = $productArray;
                        $this->cartProducts[$productKey]["cartQuantity"] = -$returnQty; // Store as negative
                        $this->cartProducts[$productKey]["rank"] = $this->rank;
                        $this->cartProducts[$productKey]["is_return"] = true;
                        $this->cartProducts[$productKey]["original_cart_id"] = $variantProduct->cart_id;
                        $this->cartProducts[$productKey]["invoice_type_performed"] = InvoiceTypePerformedEnum::RETURN->value;

                        // Store the original invoice quantity for reference
                        $this->cartProducts[$productKey]["original_invoice_quantity"] = $originalQuantity;

                        // Store the remaining returnable quantity for reference
                        $this->cartProducts[$productKey]["remaining_qty"] = $remainingQuantity;

                        // Store the absolute return quantity for reference
                        $this->cartProducts[$productKey]["absolute_return_qty"] = $returnQty;

                        // Store the original invoice ID if available
                        if ($originalInvoiceId) {
                            $this->cartProducts[$productKey]["original_invoice_id"] = $originalInvoiceId;
                        }

                        // Store the customer ID if available
                        if ($customerId) {
                            $this->cartProducts[$productKey]["customer_id"] = $customerId;
                        }

                        // Preserve promotional information if available
                        if (isset($product['has_promotion'])) {
                            $this->cartProducts[$productKey]['has_promotion'] = $product['has_promotion'];
                        }
                        if (isset($product['promotion_id'])) {
                            $this->cartProducts[$productKey]['promotion_id'] = $product['promotion_id'];
                        }

                        // Preserve variant-specific properties
                        $this->cartProducts[$productKey]['is_variant'] = true;
                        $this->cartProducts[$productKey]['variant_id'] = $variantProduct->variant_id;
                        $this->cartProducts[$productKey]['variant_name'] = $variantProduct->variant_name;
                        $this->cartProducts[$productKey]['unique_id'] = $variantProduct->unique_id;
                        $this->cartProducts[$productKey]['cart_id'] = $productKey;

                        // Update changedQuantity array - use positive value for UI
                        $this->changedQuantity[$productKey] = $returnQty;

                        // Increase rank for next product
                        $this->increaseRank();

                        // Set tax rate if not already set
                        if ($this->taxRate === null) {
                            $this->taxRate = $variantProduct->percentage;
                            $this->dispatch('setTaxRate', rate: $this->taxRate);
                        }

                        // Add fulfillment status
                        $this->updateFulfillmentStatus($productKey);

                        \Illuminate\Support\Facades\Log::debug('Added variant return product to cart: ' . json_encode($this->cartProducts[$productKey]));
                    }
                }
            } else {
                // For regular products, get the product and add it to cart (include deleted products for returns)
                $regularProduct = $this->getProduct($product['product_id'], 'id', true)->first();

                if ($regularProduct && $this->validateProduct($regularProduct)) {

                    // Create a unique key for the return product
                    $productKey = $regularProduct->id . '-return';

                    // Convert the product to array and ensure all required fields are present
                    $productArray = $regularProduct->toArray();

                    // Override with original invoice pricing data to preserve promotional prices
                    if (isset($product['price_tax_inclusive'])) {
                        $productArray['sell_price'] = (string)$product['price_tax_inclusive'];
                    }
                    if (isset($product['price_tax_exclusive'])) {
                        $productArray['sell_price_tax_exclusive'] = (string)$product['price_tax_exclusive'];
                    }
                    if (isset($product['price_tax_exclusive_rounded'])) {
                        $productArray['sell_price_tax_exclusive_rounded'] = (string)$product['price_tax_exclusive_rounded'];
                    }
                    if (isset($product['cost_tax_inclusive'])) {
                        $productArray['cost'] = (string)$product['cost_tax_inclusive'];
                    }
                    if (isset($product['cost_tax_exclusive'])) {
                        $productArray['cost_tax_exclusive'] = (string)$product['cost_tax_exclusive'];
                        $productArray['cost_tax_exclusive_rounded'] = (string)$product['cost_tax_exclusive'];
                    }

                    // Ensure price fields are set to "0" string if null to avoid BC math errors
                    $priceFields = [
                        'sell_price',
                        'wholesale_price',
                        'sell_price_tax_exclusive',
                        'wholesale_price_tax_exclusive',
                        'sell_price_tax_exclusive_rounded',
                        'wholesale_price_tax_exclusive_rounded',
                        'cost',
                        'cost_tax_exclusive',
                        'cost_tax_exclusive_rounded'
                    ];

                    foreach ($priceFields as $field) {
                        $productArray[$field] = (string)($productArray[$field] ?? '0');
                    }

                    // Add to cart with return flag and specified quantity (as negative)
                    $this->cartProducts[$productKey] = $productArray;
                    $this->cartProducts[$productKey]["cartQuantity"] = -$returnQty; // Store as negative
                    $this->cartProducts[$productKey]["rank"] = $this->rank;
                    $this->cartProducts[$productKey]["is_return"] = true;
                    $this->cartProducts[$productKey]["original_cart_id"] = $regularProduct->id;
                    $this->cartProducts[$productKey]["cart_id"] = $productKey;
                    $this->cartProducts[$productKey]["invoice_type_performed"] = InvoiceTypePerformedEnum::RETURN->value;

                    // Store the original invoice quantity for reference
                    $this->cartProducts[$productKey]["original_invoice_quantity"] = $originalQuantity;

                    // Store the remaining returnable quantity for reference
                    $this->cartProducts[$productKey]["remaining_qty"] = $remainingQuantity;

                    // Store the absolute return quantity for reference
                    $this->cartProducts[$productKey]["absolute_return_qty"] = $returnQty;

                    // Store the original invoice ID if available
                    if ($originalInvoiceId) {
                        $this->cartProducts[$productKey]["original_invoice_id"] = $originalInvoiceId;
                    }

                    // Store the customer ID if available
                    if ($customerId) {
                        $this->cartProducts[$productKey]["customer_id"] = $customerId;
                    }

                    // Preserve promotional information if available
                    if (isset($product['has_promotion'])) {
                        $this->cartProducts[$productKey]['has_promotion'] = $product['has_promotion'];
                    }
                    if (isset($product['promotion_id'])) {
                        $this->cartProducts[$productKey]['promotion_id'] = $product['promotion_id'];
                    }

                    // Update changedQuantity array - use positive value for UI
                    $this->changedQuantity[$productKey] = $returnQty;

                    // Increase rank for next product
                    $this->increaseRank();

                    // Set tax rate if not already set
                    if ($this->taxRate === null) {
                        $this->taxRate = $regularProduct->percentage;
                        $this->dispatch('setTaxRate', rate: $this->taxRate);
                    }

                    // Add fulfillment status
                    $this->updateFulfillmentStatus($productKey);

                }
            }
        }

        // Update the cart
        $this->dispatch("CartAdded", cart: $this->cartProducts);
        $this->dispatch("calculatingNumbers", cart: $this->cartProducts);
        $this->dispatch('productUpdated');
    }

    public function createFulfillmentsFromCart(MerchantInvoice $invoice, $products = null)
    {
        // Add debug logging
        \Illuminate\Support\Facades\Log::debug('Creating fulfillments from cart for invoice: ' . $invoice->id);

        // Use the products passed from PosCart if available, otherwise use cartProducts
        $productsToProcess = $products ?? $this->cartProducts;
        \Illuminate\Support\Facades\Log::debug('Products to process: ' . json_encode($productsToProcess));

        // Prepare fulfillment data from cart
        $fulfillmentData = [
            'branch_transfer' => [],
            'delivery' => []
        ];

        // Process each product in the cart
        foreach ($productsToProcess as $product) {
            // Skip products that don't need fulfillment
            if (!isset($product['invoice_product_id'])) {
                \Illuminate\Support\Facades\Log::debug('Product missing invoice_product_id: ' . json_encode($product));
                continue;
            }

            // Check if this product has branch fulfillment
            if (isset($product['fulfillment_from_branch'])) {
                \Illuminate\Support\Facades\Log::debug('Adding branch fulfillment for product: ' . $product['id']);
                $fulfillmentData['branch_transfer'][] = [
                    'invoice_product_id' => $product['invoice_product_id'],
                    'product_id' => $product['id'],
                    'variant_id' => $product['variant_id'] ?? null,
                    'fulfillment_branch_id' => $product['fulfillment_from_branch']['branch_id'],
                    'quantity' => $product['fulfillment_from_branch']['quantity']
                ];
            }

            // Check if this product has delivery fulfillment
            if (isset($product['fulfillment_by_delivery'])) {
                \Illuminate\Support\Facades\Log::debug('Adding delivery fulfillment for product: ' . $product['id']);
                $fulfillmentData['delivery'][] = [
                    'invoice_product_id' => $product['invoice_product_id'],
                    'product_id' => $product['id'],
                    'variant_id' => $product['variant_id'] ?? null,
                    'fulfillment_branch_id' => $this->branchId, // Self-fulfillment from current branch
                    'quantity' => $product['cartQuantity'],
                    'delivery_address' => $product['fulfillment_by_delivery']['address']
                ];
            }
        }

        // If no fulfillment needed, return empty array
        if (empty($fulfillmentData['branch_transfer']) && empty($fulfillmentData['delivery'])) {
            \Illuminate\Support\Facades\Log::debug('No fulfillment data found, returning empty array');
            return [];
        }

        \Illuminate\Support\Facades\Log::debug('Fulfillment data: ' . json_encode($fulfillmentData));

        // Create fulfillment orders using the service
        $fulfillmentService = app(FulfillmentService::class);
        $result = $fulfillmentService->createFulfillmentsFromInvoice($invoice, $fulfillmentData);

        \Illuminate\Support\Facades\Log::debug('Fulfillment creation result: ' . json_encode($result));

        return $result;
    }

    /**
     * Reset promotion state for all products in the cart
     */
    private function resetPromotionState()
    {
        // Reset the applied promotions array
        $this->appliedPromotions = [];

        // Loop through all products in the cart
        foreach ($this->cartProducts as $cartId => $product) {
            // If the product has original prices stored, restore them
            if (isset($product['original_sell_price'])) {
                $this->cartProducts[$cartId]['sell_price'] = $product['original_sell_price'];
                $this->cartProducts[$cartId]['wholesale_price'] = $product['original_wholesale_price'];

                if (isset($product['original_sell_price_tax_exclusive'])) {
                    $this->cartProducts[$cartId]['sell_price_tax_exclusive'] = $product['original_sell_price_tax_exclusive'];
                    $this->cartProducts[$cartId]['wholesale_price_tax_exclusive'] = $product['original_wholesale_price_tax_exclusive'];
                    $this->cartProducts[$cartId]['sell_price_tax_exclusive_rounded'] = $product['original_sell_price_tax_exclusive_rounded'];
                    $this->cartProducts[$cartId]['wholesale_price_tax_exclusive_rounded'] = $product['original_wholesale_price_tax_exclusive_rounded'];
                }
            }

            // Remove promotion flags
            unset($this->cartProducts[$cartId]['has_promotion']);
            unset($this->cartProducts[$cartId]['promotion_id']);
            unset($this->cartProducts[$cartId]['promotion_type']);
            unset($this->cartProducts[$cartId]['promotion_discount_percentage']);
            unset($this->cartProducts[$cartId]['promotion_free_quantity']);
            unset($this->cartProducts[$cartId]['promotion_discounted_quantity']);
            unset($this->cartProducts[$cartId]['promotion_get_y_type']);
            unset($this->cartProducts[$cartId]['promotion_name']);
            unset($this->cartProducts[$cartId]['original_sell_price']);
            unset($this->cartProducts[$cartId]['original_wholesale_price']);
            unset($this->cartProducts[$cartId]['original_sell_price_tax_exclusive']);
            unset($this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive']);
            unset($this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded']);
            unset($this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded']);
            unset($this->cartProducts[$cartId]['is_free']);
        }
    }

    /**
     * Identify and consolidate products that have lost their promotion status
     */
    private function consolidateUnpromotedProducts()
    {
        // Identify products with promotion IDs in their cart_id
        $promotedProducts = [];
        $baseProducts = [];
        $promotionIdPattern = '/-\d+$/'; // Pattern to match -number at the end (promotion ID)

        foreach ($this->cartProducts as $cartId => $product) {

            // Check if this is a promoted product (cart_id ends with -promotionId)
            if (preg_match($promotionIdPattern, $cartId)) {
                // Extract the promotion ID from the cart ID
                $parts = explode('-', $cartId);

                // Determine the base product ID by removing the promotion ID
                // For variants, this will be product_id-variant_id
                // For regular products, this will be product_id
                $baseProductId = preg_replace($promotionIdPattern, '', $cartId);

                // For variant products, ensure we're using the full cart ID (product_id-variant_id)
                // This prevents multiple variants from being consolidated into a single record
                if (isset($product['is_variant']) && $product['is_variant'] && isset($product['variant_id'])) {
                    // Make sure the baseProductId includes the variant ID
                    if (strpos($baseProductId, '-' . $product['variant_id']) === false) {
                        $baseProductId = $product['id'] . '-' . $product['variant_id'];
                    }
                }

                // Use the cart_id from the product if available, as it's more reliable
                if (isset($product['cart_id']) && $product['cart_id'] !== $cartId) {
                    // Remove the promotion ID from the cart_id
                    $baseProductId = preg_replace($promotionIdPattern, '', $product['cart_id']);
                }

                // Skip if we couldn't determine the base product ID
                if (empty($baseProductId)) {
                    continue;
                }

                // Add to the list of promoted products
                if (!isset($promotedProducts[$baseProductId])) {
                    $promotedProducts[$baseProductId] = [];
                }
                $promotedProducts[$baseProductId][$cartId] = $product;
            } else {
                // This is a base product (not promoted)
                $baseProducts[$cartId] = $product;
            }
        }

        // Now check each group of promoted products to see if they still qualify for the promotion
        foreach ($promotedProducts as $baseProductId => $products) {
            foreach ($products as $cartId => $product) {
                // If the product doesn't have promotion flags or has lost its promotion status
                // This can happen when the buy_x quantity decreases below the threshold
                if (!isset($product['has_promotion']) || !$product['has_promotion'] ||
                    (isset($product['promotion_type']) && $product['promotion_type'] === PromotionTypeEnum::BUY_X_GET_Y->value &&
                     !$this->doesBuyXGetYPromotionQualify($product['promotion_id']))) {
                    // This product has lost its promotion status, merge it back with the base product
                    // Use the cart_id from the product if available, otherwise use the baseProductId
                    $baseCartId = isset($product['cart_id']) ? preg_replace($promotionIdPattern, '', $product['cart_id']) : $baseProductId;

                    // For variant products, ensure the baseCartId includes the variant ID
                    if (isset($product['is_variant']) && $product['is_variant'] && isset($product['variant_id'])) {
                        if (strpos($baseCartId, '-' . $product['variant_id']) === false) {
                            $baseCartId = $product['id'] . '-' . $product['variant_id'];
                        }
                    }

                    if (isset($baseProducts[$baseCartId])) {
                        // Add the quantity to the base product
                        $baseProducts[$baseCartId]['cartQuantity'] += $product['cartQuantity'];

                        // Update the changedQuantity array for the base product
                        if (isset($this->changedQuantity[$baseCartId])) {
                            $this->changedQuantity[$baseCartId] = $baseProducts[$baseCartId]['cartQuantity'];
                        } else {
                            $this->changedQuantity[$baseCartId] = $baseProducts[$baseCartId]['cartQuantity'];
                        }

                        // Log the quantity change for debugging
                        Log::info("Merging product {$cartId} into {$baseCartId}: Adding {$product['cartQuantity']} to make {$baseProducts[$baseCartId]['cartQuantity']}");

                        // Remove the promoted product from the cart and changedQuantity array
                        unset($this->cartProducts[$cartId]);
                        unset($this->changedQuantity[$cartId]);
                    } else {
                        // Base product doesn't exist, create it
                        $baseProduct = $product;



                        // Remove promotion flags
                        unset($baseProduct['has_promotion']);
                        unset($baseProduct['promotion_id']);
                        unset($baseProduct['promotion_type']);
                        unset($baseProduct['promotion_discount_percentage']);
                        unset($baseProduct['promotion_free_quantity']);
                        unset($baseProduct['promotion_discounted_quantity']);
                        unset($baseProduct['promotion_get_y_type']);
                        unset($baseProduct['promotion_name']);

                        // Restore original prices if available
                        if (isset($baseProduct['original_sell_price'])) {
                            $baseProduct['sell_price'] = $baseProduct['original_sell_price'];
                            $baseProduct['wholesale_price'] = $baseProduct['original_wholesale_price'];
                            $baseProduct['sell_price_tax_exclusive'] = $baseProduct['original_sell_price_tax_exclusive'];
                            $baseProduct['wholesale_price_tax_exclusive'] = $baseProduct['original_wholesale_price_tax_exclusive'];
                            $baseProduct['sell_price_tax_exclusive_rounded'] = $baseProduct['original_sell_price_tax_exclusive_rounded'];
                            $baseProduct['wholesale_price_tax_exclusive_rounded'] = $baseProduct['original_wholesale_price_tax_exclusive_rounded'];

                            unset($baseProduct['original_sell_price']);
                            unset($baseProduct['original_wholesale_price']);
                            unset($baseProduct['original_sell_price_tax_exclusive']);
                            unset($baseProduct['original_wholesale_price_tax_exclusive']);
                            unset($baseProduct['original_sell_price_tax_exclusive_rounded']);
                            unset($baseProduct['original_wholesale_price_tax_exclusive_rounded']);
                        }

                        // Ensure the cart_id is set correctly
                        // For variant products, it should be product_id-variant_id
                        // For regular products, it should be product_id
                        $baseProduct['cart_id'] = $baseProductId;

                        // Double-check for variant products to ensure the cart_id includes the variant ID
                        if (isset($baseProduct['is_variant']) && $baseProduct['is_variant'] && isset($baseProduct['variant_id'])) {
                            // Check if the cart_id already includes the variant ID
                            if (strpos($baseProduct['cart_id'], '-' . $baseProduct['variant_id']) === false) {
                                // If not, add the variant ID to the cart_id
                                $baseProduct['cart_id'] = $baseProduct['id'] . '-' . $baseProduct['variant_id'];
                            }
                        }

                        // Make sure the ID field is also updated correctly
                        if (strpos($baseProductId, '-') !== false) {
                            // For variant products, the ID is the first part
                            $idParts = explode('-', $baseProductId);
                            $baseProduct['id'] = $idParts[0];
                        } else {
                            // For simple products, the ID is the same as the cart_id
                            $baseProduct['id'] = $baseProductId;
                        }

                        // Add to the cart using the baseCartId
                        $baseProducts[$baseCartId] = $baseProduct;

                        // Update the changedQuantity array for the new base product
                        $this->changedQuantity[$baseCartId] = $baseProduct['cartQuantity'];

                        // Log the quantity change for debugging
                        Log::info("Creating new base product {$baseCartId} from {$cartId} with quantity {$baseProduct['cartQuantity']}");

                        // Remove the promoted product from the cart and changedQuantity array
                        unset($this->cartProducts[$cartId]);
                        unset($this->changedQuantity[$cartId]);
                    }
                }
            }
        }

        // Update the cart with the consolidated products
        foreach ($baseProducts as $cartId => $product) {
            // Use the cart_id from the product, not the key from the array
            // This ensures variant products have the correct cart_id format
            $actualCartId = $product['cart_id'];

            $this->cartProducts[$actualCartId] = $product;

            // Ensure the changedQuantity array is updated for this product
            $this->changedQuantity[$actualCartId] = $product['cartQuantity'];

            // Log the final quantity for debugging
            Log::info("Final product {$actualCartId} quantity: {$product['cartQuantity']}");

            // If the actual cart ID is different from the original key, remove the original entry
            if ($actualCartId !== $cartId && isset($this->cartProducts[$cartId])) {
                unset($this->cartProducts[$cartId]);
                unset($this->changedQuantity[$cartId]);
            }
        }
    }

    /**
     * Process cart items and apply promotions
     */
    public function processPromotions()
    {
            if (empty($this->cartProducts)) {
                $this->appliedPromotions = [];
                $this->promotionGroups = []; // Clear promotion groups when cart is empty
                return;
            }

            // Clear old promotion groups before processing new ones
            $this->promotionGroups = [];

            // First, identify and consolidate any products that have lost their promotion status
            $this->consolidateUnpromotedProducts();

            // Get all product IDs in the cart
            $productIds = array_keys($this->cartProducts);

            // Extract base product IDs and variant IDs
            $baseProductIds = [];
            $productIdsWithoutVariants = [];
            $variantIds = [];

            foreach ($productIds as $cartId) {
                // If the cart ID contains a dash, it could be a variant or a promotion
                if (strpos($cartId, '-') !== false) {
                    $parts = explode('-', $cartId);

                    // Extract the base product ID (without variant or promotion)
                    $baseProductId = $parts[0];
                    $productIdsWithoutVariants[] = $baseProductId;

                    if (count($parts) >= 2) {
                        // This could be a variant (product_id-variant_id) or a promotion (product_id-promotion_id)
                        // For our purposes, we'll treat the second part as a potential variant ID
                        if (is_numeric($parts[1])) {
                            $variantIds[] = $parts[1];
                        }

                        // Format: product_id-variant_id or product_id-promotion_id
                        $baseProductIds[] = $parts[0];

                        if (count($parts) == 3) {
                            // Format: product_id-variant_id-promotion_id
                            $baseProductIds[] = $parts[0] . '-' . $parts[1];
                        }
                    }
                } else {
                    // Regular product ID
                    $baseProductIds[] = $cartId;
                    $productIdsWithoutVariants[] = $cartId;
                }
            }

            // Combine all product IDs for promotion lookup
            $allProductIds = array_unique(array_merge($productIds, $baseProductIds, $productIdsWithoutVariants));

            // Get all active promotions for these products directly
            $directProductPromotions = MerchantPromotion::query()
                ->withoutBusinessFiltering() // Remove the global scope
                ->active()
                ->whereHas('products', function ($query) use ($allProductIds) {
                    $query->withoutBusinessFiltering() // Remove the global scope
                        ->whereIn('product_id', $allProductIds);
                })
                ->where('merchant_promotions.merchant_id', BusinessContextService::getMerchantId())
                ->where('merchant_promotions.business_id', BusinessContextService::getBusinessId())
                ->with(['products' => function ($query) {
                    $query->with(['product', 'variants']);
                }])
                ->orderBy('created_at', 'desc')
                ->get();

            // If we have variant IDs, also get promotions that apply to those variants
            if (!empty($variantIds)) {
                $variantPromotions = MerchantPromotion::query()
                    ->withoutBusinessFiltering()
                    ->active()
                    ->whereHas('products.product.variants', function ($query) use ($variantIds) {
                        $query->withoutBusinessFiltering()
                            ->whereIn('id', $variantIds);
                    })
                    ->where('merchant_promotions.merchant_id', BusinessContextService::getMerchantId())
                    ->where('merchant_promotions.business_id', BusinessContextService::getBusinessId())
                    ->with(['products' => function ($query) {
                        $query->with(['product', 'variants']);
                    }])
                    ->orderBy('created_at', 'desc')
                    ->get();

                // Merge with direct product promotions
                $directProductPromotions = $directProductPromotions->concat($variantPromotions)->unique('id');
            }

            // Get all active promotions for categories that these products belong to
            $categoryPromotions = MerchantPromotion::query()
                ->withoutBusinessFiltering() // Remove the global scope
                ->active()
                ->whereHas('categories', function ($query) use ($allProductIds) {
                    $query->withoutBusinessFiltering() // Remove the global scope
                        ->whereHas('products', function ($subQuery) use ($allProductIds) {
                            $subQuery->withoutBusinessFiltering() // Remove the global scope
                                ->whereIn('merchants_products.id', $allProductIds);
                        });
                })
                ->where('merchant_promotions.merchant_id', BusinessContextService::getMerchantId())
                ->where('merchant_promotions.business_id', BusinessContextService::getBusinessId())
                ->with(['categories.products.variants', 'products.variants', 'products.product'])
                ->orderBy('created_at', 'desc')
                ->get();

            // If we have variant IDs, also get category promotions that apply to those variants
            if (!empty($variantIds)) {
                $categoryVariantPromotions = MerchantPromotion::query()
                    ->withoutBusinessFiltering()
                    ->active()
                    ->whereHas('categories.products.variants', function ($query) use ($variantIds) {
                        $query->withoutBusinessFiltering()
                            ->whereIn('id', $variantIds);
                    })
                    ->where('merchant_promotions.merchant_id', BusinessContextService::getMerchantId())
                    ->where('merchant_promotions.business_id', BusinessContextService::getBusinessId())
                    ->with(['categories.products.variants', 'products.variants', 'products.product'])
                    ->orderBy('created_at', 'desc')
                    ->get();

                // Merge with category promotions
                $categoryPromotions = $categoryPromotions->concat($categoryVariantPromotions)->unique('id');
            }

            // Get all active promotions for tags that these products have
            $tagPromotions = MerchantPromotion::query()
                ->withoutBusinessFiltering() // Remove the global scope
                ->active()
                ->whereHas('tags', function ($query) use ($allProductIds) {
                    $query->withoutBusinessFiltering() // Remove the global scope
                        ->whereHas('products', function ($subQuery) use ($allProductIds) {
                            $subQuery->withoutBusinessFiltering() // Remove the global scope
                                ->whereIn('merchants_products.id', $allProductIds);
                        });
                })
                ->where('merchant_promotions.merchant_id', BusinessContextService::getMerchantId())
                ->where('merchant_promotions.business_id', BusinessContextService::getBusinessId())
                ->with(['tags.products.variants', 'products.variants', 'products.product'])
                ->orderBy('created_at', 'desc')
                ->get();

            // If we have variant IDs, also get tag promotions that apply to those variants
            if (!empty($variantIds)) {
                $tagVariantPromotions = MerchantPromotion::query()
                    ->withoutBusinessFiltering()
                    ->active()
                    ->whereHas('tags.products.variants', function ($query) use ($variantIds) {
                        $query->withoutBusinessFiltering()
                            ->whereIn('id', $variantIds);
                    })
                    ->where('merchant_promotions.merchant_id', BusinessContextService::getMerchantId())
                    ->where('merchant_promotions.business_id', BusinessContextService::getBusinessId())
                    ->with(['tags.products.variants', 'products.variants', 'products.product'])
                    ->orderBy('created_at', 'desc')
                    ->get();

                // Merge with tag promotions
                $tagPromotions = $tagPromotions->concat($tagVariantPromotions)->unique('id');
            }

            // Combine all promotions and sort by created_at in descending order (newest first)
            $this->activePromotions = $directProductPromotions->concat($categoryPromotions)
                ->concat($tagPromotions)
                ->unique('id')
                ->sortByDesc('created_at');

            // If no active promotions, return original cart
            if ($this->activePromotions->isEmpty()) {
                $this->appliedPromotions = [];
                return;
            }

            // Reset promotion state for all products
            $this->resetPromotionState();

            // Track which products have had promotions applied
            $processedProducts = [];

            // Process all promotions in order (newest first)
            foreach ($this->activePromotions as $promotion) {
                if ($promotion->type === PromotionTypeEnum::DISCOUNT->value) {
                    // Process discount promotion
                    $this->applyDiscountPromotion($promotion, $processedProducts);
                } elseif ($promotion->type === PromotionTypeEnum::BUY_X_GET_Y->value) {
                    // Process buy_x_get_y promotion
                    $this->applyBuyXGetYPromotion($promotion, $processedProducts);
                }
            }

            // After processing all promotions, ensure changedQuantity is updated for all products
            foreach ($this->cartProducts as $cartId => $product) {
                if (!isset($this->changedQuantity[$cartId])) {
                    $this->changedQuantity[$cartId] = $product['cartQuantity'];
                }
            }
        }

    /**
     * Apply a single discount promotion to cart products
     *
     * @param MerchantPromotion $promotion
     * @param array $processedProducts Reference to array of already processed product IDs
     */
    private function applyDiscountPromotion($promotion, &$processedProducts)
    {
        // Get products directly assigned to this promotion
        $promotionProducts = $promotion->products->pluck('product_id')->toArray();

        // Get products from categories in this promotion
        $categoryProducts = collect();
        if ($promotion->categories->isNotEmpty()) {
            foreach ($promotion->categories as $category) {
                $categoryProducts = $categoryProducts->concat($category->products->pluck('id'));
            }
        }

        // Get products from tags in this promotion
        $tagProducts = collect();
        if ($promotion->tags->isNotEmpty()) {
            foreach ($promotion->tags as $tag) {
                $tagProducts = $tagProducts->concat($tag->products->pluck('id'));
            }
        }

        // Combine all product IDs that are eligible for this promotion
        $eligibleProductIds = array_unique(array_merge(
            $promotionProducts,
            $categoryProducts->toArray(),
            $tagProducts->toArray()
        ));

        // Find all products in the cart that match the eligible product IDs
        // This includes both regular products and variants
        $eligibleCartProducts = [];

        foreach ($this->cartProducts as $cartId => $product) {
            // Skip if this product has already been processed
            if (in_array($cartId, $processedProducts)) {
                continue;
            }

            // For variant products, we need to check the product_id field
            // First, try to get it from the product_id field
            $productIdInCart = $product['product_id'] ?? null;

            // If that doesn't work, try to extract it from the cart_id
            if (!$productIdInCart && isset($product['cart_id'])) {
                // For variant products, the cart_id will be in the format productid-variantid
                $cartIdParts = explode('-', $product['cart_id']);
                if (count($cartIdParts) > 0) {
                    $productIdInCart = $cartIdParts[0];
                }
            }

            // Skip if we can't determine the product ID
            if (!$productIdInCart) {
                continue;
            }

            // Check if this product is eligible for the promotion
            if (in_array($productIdInCart, $eligibleProductIds)) {
                $eligibleCartProducts[$cartId] = $product;
            }
        }

        foreach ($eligibleCartProducts as $cartId => $product) {
            // Apply discount to the product
            $this->cartProducts[$cartId]['original_sell_price'] = $this->cartProducts[$cartId]['sell_price'];
            $this->cartProducts[$cartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['wholesale_price'];
            $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['sell_price_tax_exclusive'];
            $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive'];
            $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['sell_price_tax_exclusive_rounded'];
            $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive_rounded'];

            // Calculate discounted prices
            $discountPercentage = $promotion->discount_percentage;
            $discountMultiplier = BC::sub(1, BC::div($discountPercentage, 100, 10), 10);

            // Apply discount to sell price
            $originalSellPrice = $this->cartProducts[$cartId]['sell_price'];
            $discountedSellPrice = BC::mul($originalSellPrice, $discountMultiplier, 10);
            $this->cartProducts[$cartId]['sell_price'] = $discountedSellPrice;

            // Apply discount to wholesale price
            $originalWholesalePrice = $this->cartProducts[$cartId]['wholesale_price'];
            $discountedWholesalePrice = BC::mul($originalWholesalePrice, $discountMultiplier, 10);
            $this->cartProducts[$cartId]['wholesale_price'] = $discountedWholesalePrice;

            // Apply discount to tax exclusive prices
            $originalSellPriceTaxExclusive = $this->cartProducts[$cartId]['sell_price_tax_exclusive'];
            $discountedSellPriceTaxExclusive = BC::mul($originalSellPriceTaxExclusive, $discountMultiplier, 10);
            $this->cartProducts[$cartId]['sell_price_tax_exclusive'] = $discountedSellPriceTaxExclusive;

            $originalWholesalePriceTaxExclusive = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive'];
            $discountedWholesalePriceTaxExclusive = BC::mul($originalWholesalePriceTaxExclusive, $discountMultiplier, 10);
            $this->cartProducts[$cartId]['wholesale_price_tax_exclusive'] = $discountedWholesalePriceTaxExclusive;

            // Apply discount to rounded tax exclusive prices
            $originalSellPriceTaxExclusiveRounded = $this->cartProducts[$cartId]['sell_price_tax_exclusive_rounded'];
            $discountedSellPriceTaxExclusiveRounded = BC::mul($originalSellPriceTaxExclusiveRounded, $discountMultiplier, 10);
            $this->cartProducts[$cartId]['sell_price_tax_exclusive_rounded'] = $discountedSellPriceTaxExclusiveRounded;

            $originalWholesalePriceTaxExclusiveRounded = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive_rounded'];
            $discountedWholesalePriceTaxExclusiveRounded = BC::mul($originalWholesalePriceTaxExclusiveRounded, $discountMultiplier, 10);
            $this->cartProducts[$cartId]['wholesale_price_tax_exclusive_rounded'] = $discountedWholesalePriceTaxExclusiveRounded;

            // Store promotion information
            $this->cartProducts[$cartId]['has_promotion'] = true;
            $this->cartProducts[$cartId]['promotion_id'] = $promotion->id;
            $this->cartProducts[$cartId]['promotion_type'] = PromotionTypeEnum::DISCOUNT->value;
            $this->cartProducts[$cartId]['promotion_discount_percentage'] = $discountPercentage;
            $this->cartProducts[$cartId]['promotion_name'] = $promotion->name;

            // Add to applied promotions list
            $this->appliedPromotions[] = [
                'promotion_id' => $promotion->id,
                'promotion_name' => $promotion->name,
                'promotion_type' => PromotionTypeEnum::DISCOUNT->value,
                'product_id' => $cartId,
                'product_name' => isset($this->cartProducts[$cartId]['variant_name']) && !empty($this->cartProducts[$cartId]['variant_name'])
                    ? $this->cartProducts[$cartId]['variant_name']
                    : $this->cartProducts[$cartId]['name'],
                'discount_percentage' => $discountPercentage,
                'original_price' => $originalSellPrice,
                'discounted_price' => $discountedSellPrice
            ];

            // Mark as processed
            $processedProducts[] = $cartId;
        }
    }

    /**
     * Apply a single buy_x_get_y promotion to cart products
     *
     * @param MerchantPromotion $promotion
     * @param array $processedProducts Reference to array of already processed product IDs
     */
    private function applyBuyXGetYPromotion($promotion, &$processedProducts)
    {
        try {
            // Get buy_x products from this promotion
            // Initialize arrays for product IDs
            $buyXProductIds = [];
            $buyXVariantCartIds = [];

            // Get buy_x products and their variants
            $buyXProducts = $promotion->products()
                ->where('type', PromotionProductTypeEnum::BUY_X->value)
                ->with('product.variants')
                ->get();

            foreach ($buyXProducts as $promotionProduct) {
                if ($promotionProduct->product) {
                    // Check if this product has variants
                    if ($promotionProduct->product->variants && $promotionProduct->product->variants->isNotEmpty()) {

                        foreach ($promotionProduct->product->variants as $variant) {
                            // Create cart ID in format product_id-variant_id
                            $buyXVariantCartIds[] = $promotionProduct->product_id . '-' . $variant->id;
                        }
                    } else {
                        // If it doesn't have variants, add it to the regular product list
                        $buyXProductIds[] = $promotionProduct->product_id;
                    }
                }
            }

            // Get buy_x products from categories
            $buyXCategoryProductIds = collect();
            $buyXCategories = $promotion->buyXCategories;
            if ($buyXCategories->isNotEmpty()) {
                foreach ($buyXCategories as $category) {
                    foreach ($category->products as $product) {
                        // Check if this product has variants
                        if ($product->variants && $product->variants->isNotEmpty()) {

                            foreach ($product->variants as $variant) {
                                // Create cart ID in format product_id-variant_id
                                $buyXVariantCartIds[] = $product->id . '-' . $variant->id;
                            }
                        } else {
                            // If it doesn't have variants, add it to the regular product list
                            $buyXCategoryProductIds->push($product->id);
                        }
                    }
                }
            }

            // Get buy_x products from tags
            $buyXTagProductIds = collect();
            $buyXTags = $promotion->buyXTags;
            if ($buyXTags->isNotEmpty()) {
                foreach ($buyXTags as $tag) {
                    foreach ($tag->products as $product) {
                        // Check if this product has variants
                        if ($product->variants && $product->variants->isNotEmpty()) {

                            foreach ($product->variants as $variant) {
                                // Create cart ID in format product_id-variant_id
                                $buyXVariantCartIds[] = $product->id . '-' . $variant->id;
                            }
                        } else {
                            // If it doesn't have variants, add it to the regular product list
                            $buyXTagProductIds->push($product->id);
                        }
                    }
                }
            }

            // Combine all buy_x product IDs and variant cart IDs
            $allBuyXProductIds = array_unique(array_merge(
                $buyXProductIds,
                $buyXCategoryProductIds->toArray(),
                $buyXTagProductIds->toArray(),
                $buyXVariantCartIds
            ));

            // Get get_y products from this promotion
            $getYProductIds = [];
            $getYVariantIds = [];
            $getYVariantCartIds = [];
            $getYProductsWithVariants = [];

            // Get get_y products and their variants
            $getYProducts = $promotion->products()
                ->where('type', PromotionProductTypeEnum::GET_Y->value)
                ->with('product.variants')
                ->get();

            foreach ($getYProducts as $promotionProduct) {
                if ($promotionProduct->product) {
                    // Check if this product has variants
                    if ($promotionProduct->product->variants && $promotionProduct->product->variants->isNotEmpty()) {
                        foreach ($promotionProduct->product->variants as $variant) {
                            // Create cart ID in format product_id-variant_id
                            $getYVariantCartIds[] = $promotionProduct->product_id . '-' . $variant->id;
                        }
                    } else {
                        // If it doesn't have variants, add it to the regular product list
                        $getYProductIds[] = $promotionProduct->product_id;
                    }
                }
            }

            // Get get_y products from categories
            $getYCategoryProductIds = collect();
            $getYCategories = $promotion->getYCategories;
            if ($getYCategories->isNotEmpty()) {
                foreach ($getYCategories as $category) {
                    foreach ($category->products as $product) {
                        // Check if this product has variants
                        if ($product->variants && $product->variants->isNotEmpty()) {
                            foreach ($product->variants as $variant) {
                                // Create cart ID in format product_id-variant_id
                                $getYVariantCartIds[] = $product->id . '-' . $variant->id;
                            }
                        } else {
                            // If it doesn't have variants, add it to the regular product list
                            $getYCategoryProductIds->push($product->id);
                        }
                    }
                }
            }

            // Get get_y products from tags
            $getYTagProductIds = collect();
            $getYTags = $promotion->getYTags;
            if ($getYTags->isNotEmpty()) {
                foreach ($getYTags as $tag) {
                    foreach ($tag->products as $product) {
                        // Check if this product has variants
                        if ($product->variants && $product->variants->isNotEmpty()) {
                            foreach ($product->variants as $variant) {
                                $getYVariantIds[] = $variant->id;
                                // Create cart ID in format product_id-variant_id
                                $getYVariantCartIds[] = $product->id . '-' . $variant->id;
                            }
                        } else {
                            // If it doesn't have variants, add it to the regular product list
                            $getYTagProductIds->push($product->id);
                        }
                    }
                }
            }

            // Combine all get_y product IDs and variant cart IDs
            $allGetYProductIds = array_unique(array_merge(
                $getYProductIds,
                $getYCategoryProductIds->toArray(),
                $getYTagProductIds->toArray(),
                $getYVariantCartIds
            ));

            // Skip if no buy_x or get_y products
            if (empty($allBuyXProductIds) || empty($allGetYProductIds)) {
                return;
            }

            // Find get_y products in cart first to ensure they exist before processing buy_x
            $getYProductsInCart = [];

            // First check for exact product IDs
            foreach ($allGetYProductIds as $productId) {
                if (isset($this->cartProducts[$productId])) {
                    $getYProductsInCart[$productId] = [
                        'cart_id' => $productId,
                        'price' => $this->cartProducts[$productId]['sell_price'],
                        'quantity' => $this->cartProducts[$productId]['cartQuantity'],
                    ];
                }
            }

            // Then check for variant products
            foreach ($this->cartProducts as $cartId => $cartProduct) {
                if (strpos($cartId, '-') !== false) {
                    $parts = explode('-', $cartId);
                    $baseProductId = $parts[0];

                    if (in_array($baseProductId, $allGetYProductIds) && !isset($getYProductsInCart[$cartId])) {
                        $getYProductsInCart[$cartId] = [
                            'cart_id' => $cartId,
                            'price' => $cartProduct['sell_price'],
                            'quantity' => $cartProduct['cartQuantity'],
                        ];
                    }
                }
            }

            // Skip if no get_y products are actually in the cart
            if (empty($getYProductsInCart)) {
                return;
            }

            // Handle products that are both in buy_x and get_y lists
            // If a product is in both lists, prioritize it as a buy_x product
            $overlappingProducts = array_intersect($allBuyXProductIds, $allGetYProductIds);
            if (!empty($overlappingProducts)) {
                // Remove overlapping products from get_y list
                $allGetYProductIds = array_diff($allGetYProductIds, $overlappingProducts);
            }

            // Calculate total quantity of buy_x products in cart
            $totalBuyXQuantity = 0;

            // Calculate total buy_x quantity first (for fulfillment calculation)
            foreach ($allBuyXProductIds as $productId) {
                if (isset($this->cartProducts[$productId])) {
                    // Skip if this product has already been processed by another promotion
                    if (in_array($productId, $processedProducts)) {
                        continue;
                    }

                    $quantity = $this->cartProducts[$productId]['cartQuantity'];
                    $totalBuyXQuantity += $quantity;
                }
            }

            // Calculate how many times the promotion is fulfilled
            $requiredQuantity = $promotion->buy_x_quantity;
            $fulfillmentCount = floor($totalBuyXQuantity / $requiredQuantity);

            // Get the quantity of get_y products to apply for each fulfillment
            $getYQuantity = $promotion->get_y_quantity;

            // Calculate total get_y items that can be given based on fulfillment count
            $totalGetYItems = $fulfillmentCount * $getYQuantity;







            if ($fulfillmentCount <= 0) {
                return;
            }

            // Only create promotion groups for buy_x_get_y promotions with 'free' or 'discount' type
            $shouldCreateGroups = $promotion->type === PromotionTypeEnum::BUY_X_GET_Y->value &&
                                  in_array($promotion->get_y_type, ['free', 'discount']);

            // Create ONE promotion group per promotion (only for eligible promotions)
            if ($shouldCreateGroups) {
                $groupId = uniqid('group_' . $promotion->id . '_' . now()->timestamp . '_');

                $this->promotionGroups[] = [
                    'group_id' => $groupId,
                    'original_promotion_id' => $promotion->id,
                    'promotion_snapshot' => [
                        'original_promotion_id' => $promotion->id,
                        'promotion_name' => $promotion->name,
                        'promotion_type' => PromotionTypeEnum::BUY_X_GET_Y->value,
                        'buy_x_quantity' => $promotion->buy_x_quantity,
                        'get_y_quantity' => $promotion->get_y_quantity,
                        'get_y_type' => $promotion->get_y_type,
                        'get_y_discount_percentage' => $promotion->get_y_discount_percentage,
                        'created_at' => now()->toISOString(),
                    ],
                    'buy_x_quantity_required' => $promotion->buy_x_quantity,
                    'get_y_quantity_given' => $promotion->get_y_quantity,
                    'total_fulfillments' => $fulfillmentCount,
                    'promotion_type' => PromotionTypeEnum::BUY_X_GET_Y->value,
                    'buy_x_products' => [],
                    'get_y_products' => []
                ];
            }

            // Now process buy_x products and assign them to groups
            // Need to check both exact product IDs and variant products
            foreach ($this->cartProducts as $cartId => $cartProduct) {
                // Check if this cart product matches any buy_x product
                $isMatch = false;
                $baseProductId = null;

                // For simple products, check if the cart ID matches any buy_x product ID
                if (in_array($cartId, $allBuyXProductIds)) {
                    $isMatch = true;
                    $baseProductId = $cartId;
                }
                // For variant products, check if the base product ID matches
                elseif (strpos($cartId, '-') !== false) {
                    $parts = explode('-', $cartId);
                    $baseProductId = $parts[0];

                    if (in_array($baseProductId, $allBuyXProductIds)) {
                        $isMatch = true;
                    }
                }

                if ($isMatch) {
                    // Skip if this product has already been processed by another promotion
                    if (in_array($cartId, $processedProducts)) {
                        continue;
                    }

                    $quantity = $cartProduct['cartQuantity'];

                    // Mark buy_x products with promotion group information (only if groups are created)
                    if ($shouldCreateGroups) {
                        $this->cartProducts[$cartId]['promotion_group_key'] = $this->generatePromotionGroupKey($cartId, $promotion->id);
                        $this->cartProducts[$cartId]['promotion_product_type'] = 'buy_x';

                        // Track buy_x products for group assignment
                        $this->trackBuyXProductForGroups($cartId, $quantity, $promotion->buy_x_quantity);
                    }

                    // Add promotion quantities for snapshot
                    $this->cartProducts[$cartId]['promotion_buy_x_quantity'] = $promotion->buy_x_quantity;
                    $this->cartProducts[$cartId]['promotion_get_y_quantity'] = $promotion->get_y_quantity;
                }
            }

            // Sort get_y products by price (cheapest first)

            // Sort get_y products by price (cheapest first)
            usort($getYProductsInCart, function ($a, $b) {
                return $a['price'] <=> $b['price'];
            });

            // Apply promotion to get_y products
            $remainingGetYItems = $totalGetYItems;
            foreach ($getYProductsInCart as $productInfo) {

                $cartId = $productInfo['cart_id'];

                // Skip if this product has already been processed by another promotion
                if (in_array($cartId, $processedProducts)) {
                    continue;
                }

                if ($remainingGetYItems <= 0) {
                    break;
                }

                // Apply promotion to as many items as possible, up to the remaining get_y items
                $quantityToApply = min($productInfo['quantity'], $remainingGetYItems);
                $remainingGetYItems -= $quantityToApply;

                // Store original prices if not already stored
                if (!isset($this->cartProducts[$cartId]['original_sell_price'])) {
                    $this->cartProducts[$cartId]['original_sell_price'] = $this->cartProducts[$cartId]['sell_price'];
                    $this->cartProducts[$cartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['wholesale_price'];
                    $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['sell_price_tax_exclusive'];
                    $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive'];
                    $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['sell_price_tax_exclusive_rounded'];
                    $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive_rounded'];
                }

                // Apply free or discount based on promotion type
                if ($promotion->get_y_type === PromotionDiscountTypeEnum::FREE->value) {
                    // Create a new cart ID for the promoted product
                    $originalCartId = $cartId;

                    // For variant products, the cart ID should be product_id-variant_id-promotion_id
                    // For regular products, the cart ID should be product_id-promotion_id
                    $newCartId = $originalCartId . '-' . $promotion->id;

                    // If this product is not in the cart with the new ID, create a new entry
                    if (!isset($this->cartProducts[$newCartId])) {
                        // Copy the product data to the new cart ID
                        $this->cartProducts[$newCartId] = $this->cartProducts[$cartId];

                        // Update the cart ID in the product data
                        $this->cartProducts[$newCartId]['cart_id'] = $newCartId;

                        // Set the quantity for the new cart ID
                        $this->cartProducts[$newCartId]['cartQuantity'] = $quantityToApply;

                        // Update the changedQuantity array for the new cart ID
                        $this->changedQuantity[$newCartId] = $quantityToApply;

                        // Ensure original prices are copied to the new cart entry
                        $this->cartProducts[$newCartId]['original_sell_price'] = $this->cartProducts[$cartId]['original_sell_price'];
                        $this->cartProducts[$newCartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['original_wholesale_price'];
                        $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'];
                        $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'];
                        $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'];
                        $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'];

                        // Reduce the quantity of the original product
                        $this->cartProducts[$cartId]['cartQuantity'] -= $quantityToApply;

                        // Update the changedQuantity array for the original cart ID
                        if (isset($this->changedQuantity[$cartId])) {
                            $this->changedQuantity[$cartId] = $this->cartProducts[$cartId]['cartQuantity'];
                        }

                        // If the original product quantity is now 0, remove it from the cart and changedQuantity array
                        if ($this->cartProducts[$cartId]['cartQuantity'] <= 0) {
                            unset($this->cartProducts[$cartId]);
                            unset($this->changedQuantity[$cartId]);
                        }
                    } else {
                        // If the product is already in the cart with the new ID, just update the quantity
                        $this->cartProducts[$newCartId]['cartQuantity'] += $quantityToApply;

                        // Update the changedQuantity array for the new cart ID
                        $this->changedQuantity[$newCartId] = $this->cartProducts[$newCartId]['cartQuantity'];

                        // Ensure original prices are set in the promoted product
                        if (!isset($this->cartProducts[$newCartId]['original_sell_price'])) {
                            $this->cartProducts[$newCartId]['original_sell_price'] = $this->cartProducts[$cartId]['original_sell_price'];
                            $this->cartProducts[$newCartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['original_wholesale_price'];
                            $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'];
                            $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'];
                            $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'];
                            $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'];
                        }

                        // Reduce the quantity of the original product
                        $this->cartProducts[$cartId]['cartQuantity'] -= $quantityToApply;

                        // Update the changedQuantity array for the original cart ID
                        if (isset($this->changedQuantity[$cartId])) {
                            $this->changedQuantity[$cartId] = $this->cartProducts[$cartId]['cartQuantity'];
                        }

                        // If the original product quantity is now 0, remove it from the cart and changedQuantity array
                        if ($this->cartProducts[$cartId]['cartQuantity'] <= 0) {
                            unset($this->cartProducts[$cartId]);
                            unset($this->changedQuantity[$cartId]);
                        }
                    }

                    // Mark as free product
                    $this->cartProducts[$newCartId]['has_promotion'] = true;
                    $this->cartProducts[$newCartId]['promotion_id'] = $promotion->id;
                    $this->cartProducts[$newCartId]['promotion_type'] = PromotionTypeEnum::BUY_X_GET_Y->value;
                    $this->cartProducts[$newCartId]['promotion_get_y_type'] = PromotionDiscountTypeEnum::FREE->value;
                    $this->cartProducts[$newCartId]['promotion_free_quantity'] = $quantityToApply;
                    $this->cartProducts[$newCartId]['promotion_name'] = $promotion->name;

                    // Add promotion group tracking information (only if groups are created)
                    if ($shouldCreateGroups) {
                        $this->cartProducts[$newCartId]['promotion_group_key'] = $this->generatePromotionGroupKey($originalCartId, $promotion->id);
                        $this->cartProducts[$newCartId]['promotion_product_type'] = 'get_y';

                        // Assign this get_y product to the appropriate group
                        $groupId = $this->assignGetYProductToGroup($newCartId, $quantityToApply);
                        $this->cartProducts[$newCartId]['promotion_group_id'] = $groupId;
                    }

                    // Add promotion quantities for snapshot
                    $this->cartProducts[$newCartId]['promotion_buy_x_quantity'] = $promotion->buy_x_quantity;
                    $this->cartProducts[$newCartId]['promotion_get_y_quantity'] = $promotion->get_y_quantity;

                    // Calculate how much of the product should be free vs. paid
                    $totalQuantity = $this->cartProducts[$newCartId]['cartQuantity'];
                    $freeQuantity = $quantityToApply;
                    $paidQuantity = max(0, $totalQuantity - $freeQuantity);

                    // If all quantity is free, set price to 0
                    if ($paidQuantity == 0) {
                        // Set all prices to 0
                        $this->cartProducts[$newCartId]['sell_price'] = 0;
                        $this->cartProducts[$newCartId]['wholesale_price'] = 0;
                        $this->cartProducts[$newCartId]['sell_price_tax_exclusive'] = 0;
                        $this->cartProducts[$newCartId]['wholesale_price_tax_exclusive'] = 0;
                        $this->cartProducts[$newCartId]['sell_price_tax_exclusive_rounded'] = 0;
                        $this->cartProducts[$newCartId]['wholesale_price_tax_exclusive_rounded'] = 0;
                    }

                    // Debug line removed
                    // Record the applied promotion
                    $this->appliedPromotions[] = [
                        'promotion_id' => $promotion->id,
                        'promotion_name' => $promotion->name,
                        'promotion_type' => PromotionTypeEnum::BUY_X_GET_Y->value,
                        'get_y_type' => PromotionDiscountTypeEnum::FREE->value,
                        'product_id' => $newCartId, // Using the new cart ID
                        'product_name' => $this->cartProducts[$newCartId]['variant_name'] ?? $this->cartProducts[$newCartId]['name'],
                        'quantity' => $quantityToApply,
                        'original_price' => $this->cartProducts[$newCartId]['original_sell_price'],
                        'discounted_price' => 0
                    ];

                    // Mark as processed
                    $processedProducts[] = $newCartId;
                } else {
                    // Apply discount
                    $discountPercentage = $promotion->get_y_discount_percentage;
                    $discountMultiplier = BC::sub(1, BC::div($discountPercentage, 100, 10), 10);

                    // Calculate discounted prices
                    $originalSellPrice = $this->cartProducts[$cartId]['original_sell_price'];
                    $discountedSellPrice = BC::mul($originalSellPrice, $discountMultiplier, 10);

                    $originalWholesalePrice = $this->cartProducts[$cartId]['original_wholesale_price'];
                    $discountedWholesalePrice = BC::mul($originalWholesalePrice, $discountMultiplier, 10);

                    $originalSellPriceTaxExclusive = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'];
                    $discountedSellPriceTaxExclusive = BC::mul($originalSellPriceTaxExclusive, $discountMultiplier, 10);

                    $originalWholesalePriceTaxExclusive = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'];
                    $discountedWholesalePriceTaxExclusive = BC::mul($originalWholesalePriceTaxExclusive, $discountMultiplier, 10);

                    $originalSellPriceTaxExclusiveRounded = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'];
                    $discountedSellPriceTaxExclusiveRounded = BC::mul($originalSellPriceTaxExclusiveRounded, $discountMultiplier, 10);

                    $originalWholesalePriceTaxExclusiveRounded = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'];
                    $discountedWholesalePriceTaxExclusiveRounded = BC::mul($originalWholesalePriceTaxExclusiveRounded, $discountMultiplier, 10);

                    // Create a new cart ID for the promoted product
                    $originalCartId = $cartId;

                    // For variant products, the cart ID should be product_id-variant_id-promotion_id
                    // For regular products, the cart ID should be product_id-promotion_id
                    $newCartId = $originalCartId . '-' . $promotion->id;

                    // Ensure original prices are stored in the original product
                    if (!isset($this->cartProducts[$cartId]['original_sell_price'])) {
                        $this->cartProducts[$cartId]['original_sell_price'] = $this->cartProducts[$cartId]['sell_price'];
                        $this->cartProducts[$cartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['wholesale_price'];
                        $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['sell_price_tax_exclusive'];
                        $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive'];
                        $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['sell_price_tax_exclusive_rounded'];
                        $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['wholesale_price_tax_exclusive_rounded'];
                    }

                    // If this product is not in the cart with the new ID, create a new entry
                    if (!isset($this->cartProducts[$newCartId])) {
                        // Copy the product data to the new cart ID
                        $this->cartProducts[$newCartId] = $this->cartProducts[$cartId];

                        // Update the cart ID in the product data
                        $this->cartProducts[$newCartId]['cart_id'] = $newCartId;

                        // Apply the discounted prices to the new cart ID
                        $this->cartProducts[$newCartId]['sell_price'] = $discountedSellPrice;
                        $this->cartProducts[$newCartId]['wholesale_price'] = $discountedWholesalePrice;
                        $this->cartProducts[$newCartId]['sell_price_tax_exclusive'] = $discountedSellPriceTaxExclusive;
                        $this->cartProducts[$newCartId]['wholesale_price_tax_exclusive'] = $discountedWholesalePriceTaxExclusive;
                        $this->cartProducts[$newCartId]['sell_price_tax_exclusive_rounded'] = $discountedSellPriceTaxExclusiveRounded;
                        $this->cartProducts[$newCartId]['wholesale_price_tax_exclusive_rounded'] = $discountedWholesalePriceTaxExclusiveRounded;

                        // Ensure original prices are copied to the new cart entry
                        $this->cartProducts[$newCartId]['original_sell_price'] = $this->cartProducts[$cartId]['original_sell_price'];
                        $this->cartProducts[$newCartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['original_wholesale_price'];
                        $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'];
                        $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'];
                        $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'];
                        $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'];

                        // Set the quantity for the new cart ID
                        $this->cartProducts[$newCartId]['cartQuantity'] = $quantityToApply;

                        // Update the changedQuantity array for the new cart ID
                        $this->changedQuantity[$newCartId] = $quantityToApply;

                        // Reduce the quantity of the original product
                        $this->cartProducts[$cartId]['cartQuantity'] -= $quantityToApply;

                        // Update the changedQuantity array for the original cart ID
                        if (isset($this->changedQuantity[$cartId])) {
                            $this->changedQuantity[$cartId] = $this->cartProducts[$cartId]['cartQuantity'];
                        }

                        // If the original product quantity is now 0, remove it from the cart and changedQuantity array
                        if ($this->cartProducts[$cartId]['cartQuantity'] <= 0) {
                            unset($this->cartProducts[$cartId]);
                            unset($this->changedQuantity[$cartId]);
                        }
                    } else {
                        // If the product is already in the cart with the new ID, just update the quantity
                        $this->cartProducts[$newCartId]['cartQuantity'] += $quantityToApply;

                        // Update the changedQuantity array for the new cart ID
                        $this->changedQuantity[$newCartId] = $this->cartProducts[$newCartId]['cartQuantity'];

                        // Make sure the discounted prices are applied
                        $this->cartProducts[$newCartId]['sell_price'] = $discountedSellPrice;
                        $this->cartProducts[$newCartId]['wholesale_price'] = $discountedWholesalePrice;
                        $this->cartProducts[$newCartId]['sell_price_tax_exclusive'] = $discountedSellPriceTaxExclusive;
                        $this->cartProducts[$newCartId]['wholesale_price_tax_exclusive'] = $discountedWholesalePriceTaxExclusive;
                        $this->cartProducts[$newCartId]['sell_price_tax_exclusive_rounded'] = $discountedSellPriceTaxExclusiveRounded;
                        $this->cartProducts[$newCartId]['wholesale_price_tax_exclusive_rounded'] = $discountedWholesalePriceTaxExclusiveRounded;

                        // Ensure original prices are set in the promoted product
                        if (!isset($this->cartProducts[$newCartId]['original_sell_price'])) {
                            $this->cartProducts[$newCartId]['original_sell_price'] = $this->cartProducts[$cartId]['original_sell_price'];
                            $this->cartProducts[$newCartId]['original_wholesale_price'] = $this->cartProducts[$cartId]['original_wholesale_price'];
                            $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive'];
                            $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive'];
                            $this->cartProducts[$newCartId]['original_sell_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_sell_price_tax_exclusive_rounded'];
                            $this->cartProducts[$newCartId]['original_wholesale_price_tax_exclusive_rounded'] = $this->cartProducts[$cartId]['original_wholesale_price_tax_exclusive_rounded'];
                        }

                        // Reduce the quantity of the original product
                        $this->cartProducts[$cartId]['cartQuantity'] -= $quantityToApply;

                        // Update the changedQuantity array for the original cart ID
                        if (isset($this->changedQuantity[$cartId])) {
                            $this->changedQuantity[$cartId] = $this->cartProducts[$cartId]['cartQuantity'];
                        }

                        // If the original product quantity is now 0, remove it from the cart and changedQuantity array
                        if ($this->cartProducts[$cartId]['cartQuantity'] <= 0) {
                            unset($this->cartProducts[$cartId]);
                            unset($this->changedQuantity[$cartId]);
                        }
                    }

                    // Store promotion information
                    $this->cartProducts[$newCartId]['has_promotion'] = true;
                    $this->cartProducts[$newCartId]['promotion_id'] = $promotion->id;
                    $this->cartProducts[$newCartId]['promotion_type'] = PromotionTypeEnum::BUY_X_GET_Y->value;
                    $this->cartProducts[$newCartId]['promotion_get_y_type'] = PromotionDiscountTypeEnum::DISCOUNT->value;
                    $this->cartProducts[$newCartId]['promotion_discount_percentage'] = $discountPercentage;
                    $this->cartProducts[$newCartId]['promotion_discounted_quantity'] = $quantityToApply;
                    $this->cartProducts[$newCartId]['promotion_name'] = $promotion->name;

                    // Add promotion group tracking information (only if groups are created)
                    if ($shouldCreateGroups) {
                        $this->cartProducts[$newCartId]['promotion_group_key'] = $this->generatePromotionGroupKey($originalCartId, $promotion->id);
                        $this->cartProducts[$newCartId]['promotion_product_type'] = 'get_y';

                        // Assign this get_y product to the appropriate group
                        $groupId = $this->assignGetYProductToGroup($newCartId, $quantityToApply);
                        $this->cartProducts[$newCartId]['promotion_group_id'] = $groupId;
                    }

                    // Add promotion quantities for snapshot
                    $this->cartProducts[$newCartId]['promotion_buy_x_quantity'] = $promotion->buy_x_quantity;
                    $this->cartProducts[$newCartId]['promotion_get_y_quantity'] = $promotion->get_y_quantity;

                    // Record the applied promotion
                    $this->appliedPromotions[] = [
                        'promotion_id' => $promotion->id,
                        'promotion_name' => $promotion->name,
                        'promotion_type' => PromotionTypeEnum::BUY_X_GET_Y->value,
                        'get_y_type' => PromotionDiscountTypeEnum::DISCOUNT->value,
                        'product_id' => $newCartId, // Using the new cart ID
                        'product_name' => $this->cartProducts[$newCartId]['variant_name'] ?? $this->cartProducts[$newCartId]['name'],
                        'quantity' => $quantityToApply,
                        'original_price' => $this->cartProducts[$newCartId]['original_sell_price'],
                        'discounted_price' => $discountedSellPrice,
                        'discount_percentage' => $discountPercentage
                    ];

                    // Mark as processed
                    $processedProducts[] = $cartId;
                }
            }

        } catch (\Exception $e) {
            Log::error('Error processing promotions: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            // Don't let promotion errors break the cart
            throw $e;
        }
    }

    /**
     * Check if a buy_x_get_y promotion still qualifies based on the current cart contents
     *
     * @param int $promotionId The promotion ID to check
     * @return bool True if the promotion qualifies, false otherwise
     */
    private function doesBuyXGetYPromotionQualify($promotionId)
    {
        // Get the promotion details
        $promotion = MerchantPromotion::find($promotionId);
        if (!$promotion || $promotion->type !== PromotionTypeEnum::BUY_X_GET_Y->value) {
            return false;
        }

        // Get buy_x products from this promotion
        $buyXProductIds = [];
        $buyXVariantIds = [];

        // Get products directly assigned to the buy_x part of this promotion
        if ($promotion->buy_x_products) {
            $buyXProductIds = array_merge($buyXProductIds, $promotion->buy_x_products);

            // Get variants for these products
            $variants = MerchantsProduct::whereIn('id', $promotion->buy_x_products)
                ->with('variants')
                ->get();

            foreach ($variants as $product) {
                if ($product->variants && $product->variants->isNotEmpty()) {
                    $buyXVariantIds = array_merge($buyXVariantIds, $product->variants->pluck('id')->toArray());
                }
            }
        }

        // Get products from categories in the buy_x part of this promotion
        if ($promotion->buy_x_categories) {
            foreach ($promotion->buy_x_categories as $categoryId) {
                $category = MerchantCategory::find($categoryId);
                if ($category) {
                    // Get products in this category
                    $categoryProducts = $category->products->pluck('id')->toArray();
                    $buyXProductIds = array_merge($buyXProductIds, $categoryProducts);

                    // Get variants for products in this category
                    foreach ($category->products as $product) {
                        if ($product->variants && $product->variants->isNotEmpty()) {
                            $buyXVariantIds = array_merge($buyXVariantIds, $product->variants->pluck('id')->toArray());
                        }
                    }
                }
            }
        }

        // Get products from tags in the buy_x part of this promotion
        if ($promotion->buy_x_tags) {
            foreach ($promotion->buy_x_tags as $tagId) {
                $tag = MerchantTag::find($tagId);
                if ($tag) {
                    // Get products with this tag
                    $tagProducts = $tag->products->pluck('id')->toArray();
                    $buyXProductIds = array_merge($buyXProductIds, $tagProducts);

                    // Get variants for products with this tag
                    foreach ($tag->products as $product) {
                        if ($product->variants && $product->variants->isNotEmpty()) {
                            $buyXVariantIds = array_merge($buyXVariantIds, $product->variants->pluck('id')->toArray());
                        }
                    }
                }
            }
        }

        // Remove duplicates
        $buyXProductIds = array_unique($buyXProductIds);
        $buyXVariantIds = array_unique($buyXVariantIds);

        // Calculate total buy_x quantity in cart
        $totalBuyXQuantity = 0;
        foreach ($this->cartProducts as $cartId => $product) {
            // Check if this product is a buy_x product
            $isMatch = false;

            // For simple products, check if the ID matches
            if (in_array($product['id'], $buyXProductIds)) {
                $isMatch = true;
            }
            // For variant products, check if the base product ID matches or the variant ID matches
            elseif (strpos($cartId, '-') !== false) {
                $parts = explode('-', $cartId);

                // Check if the base product ID matches
                if (in_array($parts[0], $buyXProductIds)) {
                    $isMatch = true;
                }
                // Check if this is a variant and if the variant ID matches
                elseif (isset($product['is_variant']) && $product['is_variant'] && isset($product['variant_id'])) {
                    if (in_array($product['variant_id'], $buyXVariantIds)) {
                        $isMatch = true;
                    }
                }
            }

            if ($isMatch) {
                $totalBuyXQuantity += $product['cartQuantity'];
            }
        }

        // Check if the promotion is fulfilled
        $requiredQuantity = $promotion->buy_x_quantity;
        $fulfillmentCount = floor($totalBuyXQuantity / $requiredQuantity);

        return $fulfillmentCount > 0;
    }

    /**
     * Generate a promotion group key for tracking related products
     */
    private function generatePromotionGroupKey($cartId, $promotionId)
    {
        // Remove promotion suffix if it exists to get base product identifier
        $parts = explode('-', $cartId);

        // If last part is promotion_id, remove it
        if (end($parts) == $promotionId) {
            array_pop($parts);
        }

        // The remaining parts form the group key
        return implode('-', $parts) . '-promo-' . $promotionId;
    }

    /**
     * Track buy_x products and assign them to promotion groups
     */
    private function trackBuyXProductForGroups($productId, $quantity, $buyXRequired)
    {
        // Find the latest promotion group (now we only have one group per promotion)
        if (empty($this->promotionGroups)) {
            return;
        }

        $group = &$this->promotionGroups[count($this->promotionGroups) - 1];

        // Add all buy_x products to the single group (no quantity limits)
        $group['buy_x_products'][] = [
            'cart_id' => $productId,
            'quantity' => $quantity
        ];

        // Assign group ID to the cart product
        $this->cartProducts[$productId]['promotion_group_id'] = $group['group_id'];
    }

    /**
     * Assign get_y product to the appropriate promotion group
     */
    private function assignGetYProductToGroup($cartId, $quantity)
    {
        // Find the latest promotion group (now we only have one group per promotion)
        if (empty($this->promotionGroups)) {
            return null;
        }

        $group = &$this->promotionGroups[count($this->promotionGroups) - 1];

        // Add all get_y products to the single group (no quantity limits)
        $group['get_y_products'][] = [
            'cart_id' => $cartId,
            'quantity' => $quantity
        ];

        return $group['group_id'];
    }

    /**
     * Get promotion groups data for invoice creation
     */
    public function getPromotionGroupsData()
    {
        return $this->promotionGroups;
    }

    /**
     * Clear promotion groups (called when cart is cleared)
     */
    public function clearPromotionGroups()
    {
        $this->promotionGroups = [];
    }
}
