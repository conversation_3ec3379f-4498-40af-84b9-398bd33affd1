<?php

namespace App\Livewire;

use App\Helpers\Helper;
use App\Models\MerchantBranch;
use App\Models\MerchantInvoice;
use App\Models\State;
use BCMathExtended\BC;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ReportsSalesSummary extends Component
{
    use WithPagination;

    public $date_start;
    public $date_end;
    public $state_id;
    public $branch_id;
    public $sortBy = 'created_at'; // Default sort column
    public $sortDirection = 'desc'; // Default sort direction
    public $fullDate;
    public $states = [];
    public $branches = [];

    protected $queryString = [
        'date_start',
        'date_end',
        'state_id',
        'branch_id',
    ];

    public function mount()
    {
        $user = Auth::user();

        // Get only states and branches that exist in invoices in a single query
        $invoiceData = MerchantInvoice::query()
            ->select('state_id', 'branch_id')
            ->where(function($query) {
                $query->whereNotNull('state_id')
                      ->orWhereNotNull('branch_id');
            })
            ->distinct()
            ->get();


        $stateIds = $invoiceData->pluck('state_id')->filter()->unique();
        $branchIds = $invoiceData->pluck('branch_id')->filter()->unique();

        $this->states = State::whereIn('id', $stateIds)->get();

        // If user is not main account, only show branches they have access to
        if (!$user->main_account) {
            $permittedBranches = $user->branches->pluck('branch_id')->toArray();
            $branchIds = $branchIds->filter(function($id) use ($permittedBranches) {
                return in_array($id, $permittedBranches);
            });
        }

        // Fetch branches in a single query with proper filtering
        $this->branches = MerchantBranch::whereIn('id', $branchIds)
            ->get();
    }


    public function render()
    {
        $user = Auth::user();
        $main_account = $user->main_account;

        // Start building the query with optimized selects
        $query = MerchantInvoice::query()
            ->select([
                'id', 'created_at', 'state_id', 'branch_id',
                'total_tax_exclusive', 'total_tax', 'total_tax_inclusive', 'total_cost_tax_exclusive'
            ]);

        // Apply branch filtering if not main account
        if (!$main_account) {
            $permittedBranches = $user->branches->pluck('branch_id')->toArray();
            $query->whereIn('branch_id', $permittedBranches);
        }

        // Apply date filtering if provided
        if (!empty($this->date_start) && !empty($this->date_end)) {
            $startDate = Carbon::parse($this->date_start)->startOfDay();
            $endDate = Carbon::parse($this->date_end)->endOfDay();
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply state filtering if provided
        if (!empty($this->state_id)) {
            $query->where('state_id', $this->state_id);
        }

        // Apply branch filtering if provided
        if (!empty($this->branch_id)) {
            $query->where('branch_id', $this->branch_id);
        }

        // Apply ordering
        $query->orderBy('created_at', 'desc');

        // Execute the query once
        $records = $query->get();

        // Group records by date
        $groupedRecords = $records->groupBy(function($record) {
            return $record->created_at->format('Y-m-d');
        });

        // Process each group to calculate aggregated values
        $aggregatedResults = $groupedRecords->map(function($group) {
            $transactions = $group->count();
            $netSales = Helper::bcCollectionSum($group, 'total_tax_exclusive');
            $totalTax = Helper::bcCollectionSum($group, 'total_tax');
            $totalSales = Helper::bcCollectionSum($group, 'total_tax_inclusive');
            $COGS = Helper::bcCollectionSum($group, 'total_cost_tax_exclusive');
            $grossProfit = BC::sub($netSales, $COGS, 10);

            // Calculate average transaction value with BC Math
            $averageTransactionValue = '0';
            if (BC::comp($transactions, '0', 10) > 0) {
                $averageTransactionValue = BC::round(BC::div($netSales, $transactions, 10), 2);
            }

            // Calculate gross profit margin with BC Math
            $grossProfitMargin = '0';
            if (BC::comp($netSales, '0', 10) != 0) {
                $grossProfitMargin = BC::round(BC::mul(BC::div($grossProfit, $netSales, 10), 100, 10), 2);
            }

            return [
                "transactions" => $transactions,
                'netSales' => $netSales,
                'totalTax' => $totalTax,
                'totalSales' => $totalSales,
                'COGS' => $COGS,
                'grossProfit' => $grossProfit,
                'averageTransactionValue' => $averageTransactionValue,
                'grossProfitMargin' => $grossProfitMargin,
                'created_at' => $group->first()->created_at->format('Y-m-d')
            ];
        });

        $paginatedData = Helper::paginate($aggregatedResults, 10, $this->sortBy, $this->sortDirection);
        return view('livewire.reports-sales-summary')->with('data', $paginatedData);
    }

    public function sortByFunction($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortBy = $field;
        $this->resetPage();
    }

    public function dateChanged($start, $end, $fullDate)
    {
        $this->date_start = $start;
        $this->date_end = $end;
        $this->fullDate = $fullDate;
        $this->resetPage();
    }

    public function stateChanged($stateId)
    {
        $this->state_id = $stateId;
        $this->resetPage();
    }

    public function branchChanged($branchId)
    {
        $this->branch_id = $branchId;
        $this->resetPage();
    }
}
