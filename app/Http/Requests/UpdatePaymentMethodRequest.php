<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePaymentMethodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        return $main_account || $permissions['settings_edit_payment_methods'];
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $paymentMethod = $this->route('paymentMethod');
        
        $rules = [
            'is_cash' => 'boolean',
            'is_rounding_activiated' => 'boolean',
        ];

        // Only validate name if it's not a default payment type
        if (!$paymentMethod->is_default) {
            $rules['name'] = [
                'required',
                'string',
                'max:255',
                Rule::unique('merchant_payment_methods', 'name')
                    ->ignore($paymentMethod->id)
                    ->where('merchant_id', getCurrentUser()->id)
                    ->where('business_id', getCurrentUser()->business_id)
            ];
        }

        return $rules;
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('messages.payment_method_name_required'),
            'name.unique' => __('messages.payment_method_name_unique'),
            'name.max' => __('messages.payment_method_name_max'),
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $paymentMethod = $this->route('paymentMethod');
            
            // Check if trying to change name of default payment type
            if ($paymentMethod->is_default && $this->has('name') && $this->input('name') !== $paymentMethod->name) {
                $validator->errors()->add('name', __('messages.cannot_edit_default_payment_type_name'));
            }
        });
    }
}
