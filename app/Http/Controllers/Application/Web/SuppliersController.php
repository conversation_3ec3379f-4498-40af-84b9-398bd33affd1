<?php

namespace App\Http\Controllers\Application\Web;

use App\Http\Requests\Application\Web\Supplier\CreateSupplierRequest;
use App\Http\Requests\Application\Web\Supplier\UpdateSupplierRequest;
use App\Models\MerchantSupplier;
use App\Services\BusinessContextService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SuppliersController extends Controller
{
    public function index(){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['suppliers_show']){
            return View("theme::marble.suppliers")->with(['title' => 'الموردين']);
        }else{
            return back();
        }
    }

    public function display($id){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['customers_show']){
            $supplier = $user->suppliers()->whereId($id)->first();
            return View("theme::marble.supplier-details")->with(['supplier'=> $supplier,'main_account' => $main_account,'permissions' => $permissions, 'title' => 'مشاهدة مورد']);
        }else{
            return back();
        }

    }

    public function add(Request $request){
        $user = getCurrentUser();
        $plan = $user->plan;

        if($user->main_account || $user->group->permissions['customers_add']){
            if($plan->is_suppliers_supported){
                return view('theme::marble.suppliers-add')->with([
                        'title' => 'إضافة مورد'
                    ]);
            }else{
                return View("theme::marble.needs-subscription", ['title' => 'إضافة مورد']);
            }
        }else{
            return back();
        }
    }

    public function edit($id,Request $request){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['customers_edit']){

            $supplier = $user->suppliers()->whereId($id)->first();
            $address = $supplier->address()->first();
            $plan = $user->plan;


            // removing the unneeded info for $changed
            $msupplier = array(
                "name" => $supplier->name,
                "phone_number" => $supplier->phone_number,
                "address" => $supplier->address,
                "vat_number" => $supplier->vat_number,
                "notes" => $supplier->notes,
                'street' => $address?->street ?: '',
                'building_number' => $address?->building_number ?: '',
                'postal_code' => $address?->postal_code ?: '',
                'state_id' => $address?->state_id ?: '',
                'city_id' =>  $address?->city_id ?: '',
                'district' =>  $address?->district ?: '',
            );
            $session = $request->session()->put('msupplier',$msupplier);

            if($plan->is_suppliers_supported){
                return view('theme::marble.suppliers-edit')->with([
                        'supplier' => $supplier,
                        'address' => $address,
                        'title' => 'تعديل مورد',
                ]);
            }else{
                return View("theme::marble.needs-subscription", ['title' => 'تعديل مورد']);
            }
        }else{
            return back();
        }

    }

    public function store(CreateSupplierRequest $request){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['customers_add']){
            $userid = $user->merchant_id;
            $plan = $user->plan;

            if(!$plan->is_suppliers_supported){
                return redirect('suppliers');
            }

            DB::transaction(function() use ($request,$userid){
                $createSupplier= MerchantSupplier::create([
                    "name" => $request->name,
                    'phone_number' => $request->phone_number,
                    'notes' => $request->notes,
                    'vat_number' => $request->vat_number,
                ]);

                $createSupplier->address()->create([
                    'street' => $request->street,
                    'building_number' => $request->building_number,
                    'postal_code' => $request->postal_code,
                    'state_id' => $request->state_id,
                    'city_id' => $request->city_id,
                    'district' => $request->district,
                    'merchant_id' => BusinessContextService::getMerchantId(),
                    'business_id' => BusinessContextService::getBusinessId(),
                ]);

            });

            return redirect("/suppliers")->with('success', __('messages.created_successfully'));
        }else{
            return back();
        }
    }


    public function edit_store($id,UpdateSupplierRequest $request){

        $originalInputs = $request->session()->get('msupplier');

        $user = getCurrentUser();
        $supplier = $user->suppliers()->whereId($id)->first();

        if($user->main_account || $user->group->permissions['suppliers_edit']){

            $plan = $user->plan;

            if(!$plan->is_suppliers_supported){
                return redirect('suppliers');
            }

            foreach ($originalInputs as $name => $value) {
                // Handle main settings changes
                if ($name == 'name' || $name == 'phone_number' || $name == "vat_number" || $name == 'notes') {
                    if ($request->input($name) != $value) {
                        $changedSettings[$name] = $request->input($name);
                    }else{
                        $changedSettings[$name] = "unchanged";
                    }
                }

                // Handle address changes
                if ($name == 'street' || $name == 'building_number' || $name == 'postal_code' || $name == 'state_id' || $name == 'city_id' || $name == 'district') {
                    if ($request->input($name) != $value) {
                        $changedAddress[$name] = $request->input($name);
                    }else{
                        $changedAddress[$name] = "unchanged";
                    }
                }
            }

            //removing unchanged items cause no validation is needed
            foreach (array_keys($changedAddress, 'unchanged') as $key) {
                unset($changedAddress[$key]);
            }

            foreach (array_keys($changedSettings, 'unchanged') as $key) {
                unset($changedSettings[$key]);
            }

             // Update main settings in database
            if (!empty($changedSettings)) {
                $supplier->update($changedSettings);
            }

            // Update address in database
            if (!empty($changedAddress)) {
                $supplier->address()
                    ->updateOrCreate([
                        'merchant_id' => BusinessContextService::getMerchantId(),
                        'business_id' => BusinessContextService::getBusinessId(),
                    ], $changedAddress);
            }

            return redirect("/suppliers")->with('success', __('messages.updated_successfully'));

        }else{
            return back();
        }
    }

}
