<?php

namespace App\Http\Controllers\Application\Web;

use App\Models\MerchantGroup;
use App\Models\MerchantGroupPermission;
use App\Services\BusinessContextService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class GroupsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:merchants');
        $this->middleware('throttle:60,1');

    }

    public function index(){
        $user = Auth::user();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['employees_show']){
            return View("theme::marble.groups", ['title' => 'صلاحيات الموظفين']);
        }else{
            return back();
        }
    }
    public function add(){
        return View("theme::marble.groups-add", ['title' => 'إضافة صلاحيات الموظفين']);
    }

    public function edit($id, Request $request){
        $user = Auth::user();
        $group = MerchantGroup::where([
            'id' => $id,
            'merchant_id' => BusinessContextService::getMerchantId(),
            'business_id' => BusinessContextService::getBusinessId(),
            ])->first();
        $permissions = MerchantGroupPermission::where([
            'merchant_id' => BusinessContextService::getMerchantId(),
            'business_id' => BusinessContextService::getBusinessId(),
            'group_id' => $id,
        ])->first();

        unset($permissions['id'],$permissions['merchant_id'],$permissions['group_id']);
        unset($permissions['created_at'],$permissions['deleted_at'],$permissions['updated_at']);

        $originals = [
            "name" => $group['name'],
            "dashboard_sales_show" => $permissions['dashboard_sales_show'],
            "dashboard_net_profit_show" => $permissions['dashboard_net_profit_show'],
            "dashboard_transactions_show" => $permissions['dashboard_transactions_show'],
            "dashbaord_payment_methods_show" => $permissions['dashbaord_payment_methods_show'],
            "dashboard_expenses_show" => $permissions['dashboard_expenses_show'],
            "dashbaord_sales_last_45_show" => $permissions['dashbaord_sales_last_45_show'],
            "cashier_open" => $permissions['cashier_open'],
            "cashier_close" => $permissions['cashier_close'],
            "cashier_pause_sale" => $permissions['cashier_pause_sale'],
            "cashier_wholesale" => $permissions['cashier_wholesale'],
            "cashier_add_customer" => $permissions['cashier_wholesale'],
            "cashier_postpay" => $permissions['cashier_postpay'],
            "cashier_multipay" => $permissions['cashier_multipay'],
            "cashier_discount" => $permissions['cashier_discount'],
            "cashier_discount_max_number" => $permissions['cashier_discount_max_number'],
            "cashier_discount_max_percentage" => $permissions['cashier_discount_max_percentage'],
            "reports_sales_summary_show" => $permissions['reports_sales_summary_show'],
            "reports_sales_summary_export" => $permissions['reports_sales_summary_export'],
            "reports_sales_product_show" => $permissions['reports_sales_product_show'],
            "reports_sales_product_export" => $permissions['reports_sales_product_export'],
            "reports_sales_payments_show" => $permissions['reports_sales_payments_show'],
            "reports_sales_payments_export" => $permissions['reports_sales_payments_export'],
            "reports_tax_show" => $permissions['reports_tax_show'],
            "reports_tax_export" => $permissions['reports_tax_export'],
            "reports_cashier_recons_show" => $permissions['reports_cashier_recons_show'],
            "products_show" => $permissions['products_show'],
            "products_import" => $permissions['products_import'],
            "products_export" => $permissions['products_export'],
            "products_add" => $permissions['products_add'],
            "products_edit" => $permissions['products_edit'],
            "products_remove" => $permissions['products_remove'],
            "products_category_show" => $permissions['products_category_show'],
            "products_category_add" => $permissions['products_category_add'],
            "products_category_edit" => $permissions['products_category_edit'],
            "products_category_remove" => $permissions['products_category_remove'],
            "products_status" => $permissions['products_status'],
            "products_wholesale" => $permissions['products_wholesale'],
            "products_cost_show" => $permissions['products_cost_show'],
            "products_cost_edit" => $permissions['products_cost_edit'],
            "invoices_show" => $permissions['invoices_show'],
            "invoices_export" => $permissions['invoices_export'],
            "invoices_receive" => $permissions['invoices_receive'],
            "invoices_return" => $permissions['invoices_return'],
            "expenses_add" => $permissions['expenses_add'],
            "expenses_edit" => $permissions['expenses_edit'],
            "expenses_delete" => $permissions['expenses_delete'],
            "expenses_show" => $permissions['expenses_show'],
            "customers_add" => $permissions['customers_add'],
            "customers_numbers" => $permissions['customers_numbers'],
            "customers_show" => $permissions['customers_show'],
            "suppliers_show" => $permissions['suppliers_show'],
            "suppliers_add" => $permissions['suppliers_add'],
            "suppliers_edit" => $permissions['suppliers_edit'],
            "suppliers_numbers" => $permissions['suppliers_numbers'],
            "settings_main_show" => $permissions['settings_main_show'],
            "settings_show_payment_methods" => $permissions['settings_show_payment_methods'],
            "settings_show_taxes" => $permissions['settings_show_taxes'],
            "settings_show_currencies" => $permissions['settings_show_currencies'],
            "settings_main_edit" => $permissions['settings_main_edit'],
            "settings_add_payment_methods" => $permissions['settings_add_payment_methods'],
            "settings_edit_payment_methods" => $permissions['settings_edit_payment_methods'],
            "settings_add_taxes" => $permissions['settings_add_taxes'],
            "settings_edit_taxes" => $permissions['settings_edit_taxes'],
            "settings_add_currencies" => $permissions['settings_add_currencies'],
            "settings_edit_currencies" => $permissions['settings_edit_currencies'],
            "employees_add" => $permissions['employees_add'],
            "employees_show" => $permissions['employees_show'],
            "employees_edit" => $permissions['employees_edit'],
            "employee_permissions_add" => $permissions['employee_permissions_add'],
            "employee_permissions_edit" => $permissions['employee_permissions_edit'],
            "employee_permissions_show" => $permissions['employee_permissions_show'],
            "purchase_order_show" => $permissions['purchase_order_show'],
            "purchase_order_add" => $permissions['purchase_order_add'],
            "transfer_orders_show" => $permissions['transfer_orders_show'],
            "transfer_orders_add" => $permissions['transfer_orders_add'],
            "stock_count_orders_show" => $permissions['stock_count_orders_show'],
            "stock_count_orders_add" => $permissions['stock_count_orders_add'],
            "remove_orders_show" => $permissions['remove_orders_show'],
            "remove_orders_add" => $permissions['remove_orders_add'],
            "branches_registers_show_add_edit" => $permissions['branches_registers_show_add_edit'],
            "subscription_show" => $permissions['subscription_show'],
            "subscription_edit" => $permissions['subscription_edit'],
            "reports_expenses_show" => $permissions['reports_expenses_show'],
            "reports_expenses_export" => $permissions['reports_expenses_export'],
            "product_returnable_status" => $permissions['product_returnable_status'],
            "apps_add" => $permissions['apps_add'],
            "apps_edit" => $permissions['apps_edit'],
            "apps_show" => $permissions['apps_show'],
            "apps_delete" => $permissions['apps_delete'],
            "invoice_settings_show" => $permissions['invoice_settings_show'],
            "invoice_settings_edit" => $permissions['invoice_settings_edit'],


        ];
        $session = $request->session()->put('originals',$originals);

        return View("theme::marble.groups-edit", ['title' => 'تعديل صلاحيات الموظفين','group'=> $group]);
    }

    public function store(Request $request){
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                Rule::unique('merchant_groups')->where(function ($query) {
                    return $query->where('merchant_id', BusinessContextService::getMerchantId())
                        ->where('business_id', BusinessContextService::getBusinessId())
                        ->whereNull('deleted_at');
                })
            ],
            'cashier_discount_max_number' => [
                'numeric',

            ],
            'cashier_discount_max_percentage' => [
                'numeric',
                'max:100',
            ]
        ]);

        if ($validator->fails()) {
            return back()
                        ->withErrors($validator)
                        ->withInput();
        }


        DB::transaction(function () use ($user,$request) {

            $group = MerchantGroup::create([
                'name' => $request['name'],
                'merchant_id' => BusinessContextService::getMerchantId(),
                'business_id' => BusinessContextService::getBusinessId(),
            ]);

            unset($request['_token']);
            $originals = [
                "name" => $request['name'],
                "dashboard_sales_show" => $request['dashboard_sales_show'],
                "dashboard_net_profit_show" => $request['dashboard_net_profit_show'],
                "dashboard_transactions_show" => $request['dashboard_transactions_show'],
                "dashbaord_payment_methods_show" => $request['dashbaord_payment_methods_show'],
                "dashboard_expenses_show" => $request['dashboard_expenses_show'],
                "dashbaord_sales_last_45_show" => $request['dashbaord_sales_last_45_show'],
                "cashier_open" => $request['cashier_open'],
                "cashier_close" => $request['cashier_close'],
                "cashier_pause_sale" => $request['cashier_pause_sale'],
                "cashier_wholesale" => $request['cashier_wholesale'],
                "cashier_add_customer" => $request['cashier_wholesale'],
                "cashier_postpay" => $request['cashier_postpay'],
                "cashier_multipay" => $request['cashier_multipay'],
                "cashier_discount" => $request['cashier_discount'],
                "cashier_discount_max_number" =>$request['cashier_discount_max_number'],
                "cashier_discount_max_percentage" =>$request['cashier_discount_max_percentage'],
                "reports_sales_summary_show" => $request['reports_sales_summary_show'],
                "reports_sales_summary_export" => $request['reports_sales_summary_export'],
                "reports_sales_product_show" => $request['reports_sales_product_show'],
                "reports_sales_product_export" => $request['reports_sales_product_export'],
                "reports_sales_payments_show" => $request['reports_sales_payments_show'],
                "reports_sales_payments_export" => $request['reports_sales_payments_export'],
                "reports_tax_show" => $request['reports_tax_show'],
                "reports_tax_export" => $request['reports_tax_export'],
                "reports_cashier_recons_show" => $request['reports_cashier_recons_show'],
                "products_show" => $request['products_show'],
                "products_import" => $request['products_import'],
                "products_export" => $request['products_export'],
                "products_add" => $request['products_add'],
                "products_edit" => $request['products_edit'],
                "products_remove" => $request['products_remove'],
                "products_category_show" => $request['products_category_show'],
                "products_category_add" => $request['products_category_add'],
                "products_category_edit" => $request['products_category_edit'],
                "products_category_remove" => $request['products_category_remove'],
                "products_status" => $request['products_status'],
                "products_wholesale" => $request['products_wholesale'],
                "products_cost_show" => $request['products_cost_show'],
                "products_cost_edit" => $request['products_cost_edit'],
                "invoices_show" => $request['invoices_show'],
                "invoices_export" => $request['invoices_export'],
                "invoices_receive" => $request['invoices_receive'],
                "invoices_return" => $request['invoices_return'],
                "expenses_add" => $request['expenses_add'],
                "expenses_edit" => $request['expenses_edit'],
                "expenses_delete" => $request['expenses_delete'],
                "expenses_show" => $request['expenses_show'],

                "purchase_order_show" => $request['purchase_order_show'],
                "purchase_order_add" => $request['purchase_order_add'],
                "transfer_orders_show" => $request['transfer_orders_show'],
                "transfer_orders_add" => $request['transfer_orders_add'],
                "stock_count_orders_show" => $request['stock_count_orders_show'],
                "stock_count_orders_add" => $request['stock_count_orders_add'],
                "remove_orders_show" => $request['remove_orders_show'],
                "remove_orders_add" => $request['remove_orders_add'],

                "customers_add" => $request['customers_add'],
                "customers_numbers" => $request['customers_numbers'],
                "customers_show" => $request['customers_show'],
                "suppliers_show" => $request['suppliers_show'],
                "suppliers_add" => $request['suppliers_add'],
                "suppliers_edit" => $request['suppliers_edit'],
                "suppliers_numbers" => $request['suppliers_numbers'],
                "settings_main_show" => $request['settings_main_show'],
                "settings_show_payment_methods" => $request['settings_show_payment_methods'],
                "settings_show_taxes" => $request['settings_show_taxes'],
                "settings_show_currencies" => $request['settings_show_currencies'],
                "settings_main_edit" => $request['settings_main_edit'],
                "settings_add_payment_methods" => $request['settings_add_payment_methods'],
                "settings_edit_payment_methods" => $request['settings_edit_payment_methods'],
                "settings_add_taxes" => $request['settings_add_taxes'],
                "settings_edit_taxes" => $request['settings_edit_taxes'],
                "settings_add_currencies" => $request['settings_add_currencies'],
                "settings_edit_currencies" => $request['settings_edit_currencies'],
                "employees_add" => $request['employees_add'],
                "employees_show" => $request['employees_show'],
                "employees_edit" => $request['employees_edit'],
                "employee_permissions_add" => $request['employee_permissions_add'],
                "employee_permissions_edit" => $request['employee_permissions_edit'],
                "employee_permissions_show" => $request['employee_permissions_show'],
                "branches_registers_show_add_edit" => $request['branches_registers_show_add_edit'],
                "subscription_show" => $request['subscription_show'],
                "subscription_edit" => $request['subscription_edit'],
                "reports_expenses_show" => $request['reports_expenses_show'],
                "reports_expenses_export" => $request['reports_expenses_export'],

                "product_returnable_status" => $request['product_returnable_status'],
                "apps_add" => $request['apps_add'],
                "apps_edit" => $request['apps_edit'],
                "apps_show" => $request['apps_show'],
                "apps_delete" => $request['apps_delete'],
                "invoice_settings_show" => $request['invoice_settings_show'],
                "invoice_settings_edit" => $request['invoice_settings_edit'],

                'group_id' => $group->id,
                'merchant_id' => BusinessContextService::getMerchantId(),
                'business_id' => BusinessContextService::getBusinessId(),

            ];
            foreach($originals as $k => $original){
                $originals[$k] = !is_null($original)?$original:0;
            }
            MerchantGroupPermission::create($originals);
        });

        return redirect('groups')->with('success', __('messages.created_successfully'));
    }

    public function edit_store($id,Request $request){
        $user = Auth::user();

        $originalInputs = $request->session()->get('originals');
        $changedInputs = [];
        foreach ($originalInputs as $name => $value) {
            if ($request->input($name) != $value) {
                $changedInputs[$name] = $request->input($name) != null? $request->input($name):0;
            }else{
                $changedInputs[$name] = "unchanged";
            }
        }
        // removing unchanged items cause no validation is needed
        foreach (array_keys($changedInputs, 'unchanged') as $key) {
            unset($changedInputs[$key]);

        }

        $validator = Validator::make($changedInputs, [
            'name' => [
                'sometimes',
                'required',
                Rule::unique('merchant_groups')->where(function ($query) {
                    return $query->where('merchant_id', BusinessContextService::getMerchantId())
                        ->where('business_id', BusinessContextService::getBusinessId())
                        ->whereNull('deleted_at');
                })
            ],

            'cashier_discount_max_number' => [
                'sometimes',
                'numeric',
            ],
            'cashier_discount_max_percentage' => [
                'sometimes',
                'numeric',
                'max:100',

            ]
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::transaction(function () use ($changedInputs,$id) {
            if(isset($changedInputs['name'])){
                $group = MerchantGroup::where([
                    'merchant_id' => BusinessContextService::getMerchantId(),
                    'business_id' => BusinessContextService::getBusinessId(),
                    'id' => $id,
                ])->first();
                $group->name = $changedInputs['name'];
                $group->save();
            }
            unset($changedInputs['name']);

            if(count($changedInputs) > 0){
                DB::table('merchant_group_permissions')->where('group_id',$id)->update($changedInputs);
            }

            return redirect()->back()->with('success', __('messages.updated_successfully'));

        });

        return redirect('groups');
    }
    //
}
