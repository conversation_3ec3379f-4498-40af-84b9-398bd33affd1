<?php

namespace App\Http\Controllers\Application\Web;

use App\Models\MerchantBranch;
use App\Models\MerchantGroup;
use App\Models\MerchantUser;
use App\Models\MerchantUserBranch;
use App\Services\BusinessContextService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;


class EmployeesController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth:merchants');
        $this->middleware('throttle:60,1');
    }

    public function index(){
        $user = Auth::user();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['employees_show']){

            return View("theme::marble.employees", ['title' => 'الموظفين']);
        }else{
            return back();
        }
    }
    public function add(){
        $user = Auth::user();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['employees_add']){
            $groups = MerchantGroup::where([
                'merchant_id' => BusinessContextService::getMerchantId(),
                'business_id' => BusinessContextService::getBusinessId(),
            ])->get();
            $groupsCount = $groups->count();
            $branches = MerchantBranch::query()->get();
            $branchesCount = $branches->count();
            return View("theme::marble.employees-add", ['title' => 'إضافة موظف','groups' => $groups,'branches' => $branches,'groupsCount' => $groupsCount,'branchesCount' => $branchesCount]);
        }else{
            return back();
        }
    }
    public function edit($id, Request $request){
        $user = Auth::user();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['employees_edit']){

            $employee = MerchantUser::query()
                ->where([
                    'id' => $id,
                    'merchant_id' => BusinessContextService::getMerchantId(),
                    'business_id' => BusinessContextService::getBusinessId(),
                ])
                ->first();

            $groups = MerchantGroup::query()
                ->where([
                    'merchant_id' => BusinessContextService::getMerchantId(),
                    'business_id' => BusinessContextService::getBusinessId(),
                ])
                ->get();

            $branches = MerchantBranch::query()->get();

            $accessedBranches = MerchantUserBranch::query()->with('branch')->where('user_id', $id)->get();

            $storedBranches = [];
            foreach ($accessedBranches as $k => $b){
                $accessedBranches[$k]['name'] =  $b?->branch?->branch_name;
                $storedBranches[$b['branch_id']] = ['code' => $b['branch_id'],'value' => $b?->branch?->branch_name];

            }
            $muser= array(
                "name" => $employee->name,
                "email" => $employee->email,
                "password" => $employee->password,
                "group_id" => $employee->group_id,
                'main_account' => $employee->main_account,
                'branches' => $storedBranches,
            );

            $session = $request->session()->put('muser',$muser);
            return view('theme::marble.employees-edit')->with(
                [
                    'user' => $employee,
                    'groups' => $groups,
                    'branches' => $branches,
                    'accessedBranches' => $accessedBranches,
                    'title' => 'تعديل مستخدم'
                ]
            );
        }else{
            return back();
        }

    }

    public function store(Request $request){
        $user = Auth::user();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if(!$main_account || !$permissions['employees_add']) {
            return back();
        }

        $userid = $user->merchant_id;
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                Rule::unique('merchant_users')->where(function ($query) {
                    return $query->where('merchant_id', BusinessContextService::getMerchantId())
                        ->where('business_id', BusinessContextService::getBusinessId())
                        ->whereNull('deleted_at');
                }),
            ],
            'email' => [
                'required',
                'email',
                'unique:merchant_users,email',
            ],
            'password' => ['required'],
            'group_id' => ['required'],
            'branches' => ['required'],

        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        DB::transaction(function () use ($userid,$request,$user) {
            //TODO test this with trait

            $group = MerchantGroup::where([
                'merchant_id' => BusinessContextService::getMerchantId(),
                'business_id' => BusinessContextService::getBusinessId(),
                'id' => $request['group_id'],
            ])->count();

            $createUser = MerchantUser::create([
                "name" => $request->name,
                'email' => $request->email,
                'merchant_id' => $userid,
                'business_id' => BusinessContextService::getBusinessId(),
                'password' => bcrypt($request->password),
                'group_id' => $group > 0? $request->group_id:0,
                'plan_id' => $user->plan_id,
            ]);

            $chosenBranches = json_decode($request['branches']);
            foreach($chosenBranches as $branch){
                $b  = MerchantBranch::where('id', $branch->code)->exists();
                if($b){
                    MerchantUserBranch::create([
                        'user_id' => $createUser->id,
                        'branch_id' => $branch->code,
                    ]);
                }
            }
        });

        return redirect('employees')->with('success', __('messages.created_successfully'));
    }

    public function edit_store(Request $request,$id){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['employees_edit']){
            $originalInputs = $request->session()->get('muser');
            $request['branches'] = json_decode($request['branches']);
            $changedInputs = [];
            $requestBranches = [];
            $changedBranches = [];
            if(!empty($request['branches'])){
                foreach ($request['branches'] as $k => $branch){
                    $requestBranches[$branch->code] = $branch;
                    if(!array_key_exists($branch->code,$originalInputs['branches'])){
                        $changedBranches[$branch->code] = ['code' => $branch->code,'value' => $branch->value,'removed' => false];
                    }
                }
            }

            foreach ($originalInputs as $name => $value) {
                if($name == 'branches'){
                    foreach($value as $value){
                        if(!array_key_exists($value['code'],$requestBranches)){
                            $changedBranches[$value['code']] = ['code' => $value['code'],'value' => $value['value'],'removed' => true];
                        }
                    }
                }
                if ($request->input($name) != $value) {
                    if($request->input($name) != null){

                        if($name == 'password'){
                            $changedInputs['password'] = bcrypt($request->input($name));
                        }else{
                            $changedInputs[$name] = $request->input($name);
                        }
                    }
                }else{
                    $changedInputs[$name] = "unchanged";
                }
            }

            unset($changedInputs['branches']);
            foreach (array_keys($changedInputs, 'unchanged') as $key) {
                unset($changedInputs[$key]);
            }

            $validator = Validator::make($changedInputs, [
                'name' => [
                    'sometimes',
                    'required',
                    Rule::unique('merchant_users')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'email' => ['sometimes','unique:merchant_users,email','required','email'],
                'password' => ['sometimes','required'],
                'group_id' => ['sometimes','required'],



            ]);
            if($request['branches'] == null){
                return back()->with('branches_required',1);
            }
            if ($validator->fails() ) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }else{

                if($originalInputs['main_account']){
                    unset(
                        $changedInputs['email'],
                        $changedInputs['password'],
                        $changedInputs['group_id'],
                        $changedBranches,
                        $changedInputs['group_id'],
                    );
                    $changedBranches = [];
                }

                DB::transaction(function () use ($id,$changedBranches,$changedInputs) {
                        if(!empty($changedInputs)){
                            DB::table('merchant_users')->where('id',$id)->update($changedInputs);
                        }
                        if(!empty($changedBranches)){
                            foreach($changedBranches as $branch){

                                $branchExists = MerchantBranch::where('id', $branch['code'])->exists();

                                if($branchExists){
                                    if($branch['removed'] == true){
                                    MerchantUserBranch::where(['user_id' => $id,'branch_id' => $branch['code']])->first()->delete();
                                    }else{
                                        MerchantUserBranch::create([
                                            'user_id' => $id,
                                            'branch_id' => $branch['code'],
                                        ]);
                                    }
                                }
                            }
                        }
                });

                return redirect('employees')->with('success', __('messages.updated_successfully'));
            }
        }else{
            return back();
        }
    }
}
