<?php

namespace App\Http\Controllers\Application\Web;

use App\Http\Requests\Application\Web\Category\CreateCategoryRequest;
use App\Http\Requests\Application\Web\Category\UpdateCategoryRequest;
use App\Models\MerchantCategory;
use Illuminate\Support\Arr;
use Symfony\Component\HttpFoundation\File\UploadedFile;


class CategoriesController extends Controller
{
    public function index()
    {
        $user = getCurrentUser();
        $merchantCategories = MerchantCategory::query()
            ->where("merchant_id", $user->merchant_id)
            ->get();
        $permissions = $user->group->permissions;

        if($user->main_account || $permissions->products_category_show){
            return view('theme::marble.categories')->with([
                "categories" => $merchantCategories,
                'permissions' => $permissions,
                'title' => 'الاقسام',
                ]);
        }else{
            return back();
        }
    }

    public function add(){
        $user =getCurrentUser();
        $permissions = $user->group->permissions;

        if($user->main_account || $permissions->products_category_add){
            return view('theme::marble.categories-add');
        }else{
            return back();
        }
    }

    public function store(CreateCategoryRequest $request){
        $user = getCurrentUser();

        $permissions = $user->group->permissions;

        if($user->main_account || $permissions->products_category_add){

            $path = 'merchant_categories/blank-image.png';

            if ($request->hasFile('thumbnail'))
            {
                $path = $request->file('thumbnail')->store('merchant_categories');
            }

            MerchantCategory::create([
                "name" => $request->name,
                'status' => $request->status,
                'thumbnail' => $path
            ]);

            return redirect("/products/categories")->with('success', __('messages.created_successfully'));
        }
    }

    public function edit(MerchantCategory $category){
        $user = getCurrentUser();

        $permissions = $user->group->permissions;

        if($user->main_account || $permissions['products_category_edit']){
            return view('theme::marble.categories-edit')->with(["category" => $category]);
        }else{
            return back();
        }

    }

    public function edit_store(MerchantCategory $category, UpdateCategoryRequest $request){
        $user = getCurrentUser();
        $permissions = $user->group->permissions;

        if($user->main_account || $permissions->products_category_edit){

            $path = $category->thumbnail;

            if($request->file('thumbnail') instanceof UploadedFile) {
                $path = $request->file('thumbnail')->store('merchant_categories');
            }

            $category->update(Arr::except($request->validated(), ['thumbnail']) + ['thumbnail' => $path]);

            return redirect("/products/categories")->with('success', __('messages.updated_successfully'));
        }
    }
}
