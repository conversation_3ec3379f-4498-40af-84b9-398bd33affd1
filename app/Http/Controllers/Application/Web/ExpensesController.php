<?php

namespace App\Http\Controllers\Application\Web;

use App\Enums\Application\TaxCodeEnum;
use App\Helpers\Helper;
use App\Models\MerchantExpense;
use App\Models\TaxType;
use BCMathExtended\BC;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;


class ExpensesController extends Controller
{
    //
    public function __construct()
    {
        $this->middleware('auth:merchants');
        $this->middleware('throttle:60,1');

    }

    public function index(){
        $user = Auth::user();

        $plan = $user->plan;
        $permissions = $user->group->permissions;
        if($permissions['expenses_show'] || $user->main_account){

            if($plan->is_expenses_supported){
                return view('theme::marble.expenses')->with(['title' => 'المصروفات']);

            }else{
                return View("theme::marble.needs-subscription", ['title' => 'المصروفات']);

            }
        }else{
            return back();
        }
    }

    public function add(Request $request){
        $user = Auth::user();
        $tax_types = TaxType::query()->get();
        $plan = $user->plan;
        $permissions = $user->group->permissions;
        if($permissions['expenses_add'] || $user->main_account){

            if($plan->is_expenses_supported){
                return view('theme::marble.expenses-add')->with(
                    [
                        'tax_types' => $tax_types
                    ]
                );
            }else{
                return redirect('expenses');

            }
        }else{
            return back();
        }
    }

    public function edit($id,Request $request){
        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['expenses_edit'] || $user->main_account){

            $expense = MerchantExpense::where('id', $id)->first();

            $taxQuery = TaxType::query();

            $tax = $taxQuery->get();
            $tax_types = $tax->toArray();
            $zero_tax = $taxQuery->where(['id'=> $expense->tax_id,'code' => "0"])->count() && $taxQuery->where(['id'=>$expense->tax_id,'code' => "0"])->first()->percentage == 0 ? true: false;

            // removing the unneeded info for $changed
            $mexpense = array(
                "name" => $expense['name'],
                "category" => $expense['barcode'],
                "cost" => $expense['cost'],
                "tax_id" => $expense['tax_id'],
                "zero_tax" => $zero_tax,
                "description" => $expense['description'],
            );
            $session = $request->session()->put('mexpense',$mexpense);

            $plan = $user->plan;
            if($plan->is_expenses_supported){
                return view('theme::marble.expenses-edit')->with(
                    [
                        'expense' => $expense,
                        'tax_types' => $tax_types,
                        'zero_tax' => $zero_tax,
                        'title' => 'تعديل مصروف',
                    ]
                );
            }else{
                    return redirect('expenses');
            }
        }else{
            return back();
        }
    }

    public function store(Request $request){
        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['expenses_add'] || $user->main_account){

            $plan = $user->plan;
            if(!$plan->is_expenses_supported){
                return redirect('expenses');
            }
            $validator = Validator::make($request->all(), [
                'name' => [
                    'required',
                ],
                'cost' => ['required','numeric'],
                'tax_id' => ['required_without:zero_tax'],
                'zero_tax' => ['required_without:tax_id'],
                'category' =>['required'],

            ]);

            if ($validator->fails()) {
                return back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $expenseArray = [
                "name" => $request->name,
                'cost' => $request->cost,
                'description' => $request->description,
                'category' => $request->category,
            ];

            if($request['zero_tax']){
                $taxId = TaxType::where('code', TaxCodeEnum::NONE->value)->first()->id;

                $expenseArray = array_merge($expenseArray, [
                    'tax_id' => $taxId,
                    'cost_tax_exclusive' => $request->cost,
                    'cost_tax_exclusive_rounded' => BC::round($request->cost, 2)
                ]);

            }else{
                $taxPercentage = TaxType::where( 'id', $request->tax_id)->first()->percentage;
                $costTaxExclusive = Helper::excludeTax($request->cost,$taxPercentage);

                $expenseArray = array_merge($expenseArray, [
                    'tax_id' => $request->tax_id,
                    'cost_tax_exclusive' => $costTaxExclusive,
                    'cost_tax_exclusive_rounded' => round($costTaxExclusive, 2),
                ]);
            }

            MerchantExpense::create($expenseArray);

            return redirect("/expenses")->with('success', __('messages.created_successfully'));

        }else{
            return back();
        }
    }

    public function edit_store($id,Request $request){

        $originalInputs = $request->session()->get('mexpense');
        $changedInputs = [];
        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['expenses_edit'] || $user->main_account){

            $plan = $user->plan;

            if(!$plan->is_expenses_supported){
                return redirect('expenses');
            }


            foreach ($originalInputs as $name => $value) {
                if ($request->input($name) != $value) {
                    $changedInputs[$name] = $request->input($name);

                    if(is_null($request->input('tax_id'))){
                        $changedInputs['tax_id'] = 'unchanged';
                    }

                    if(is_null($request->input('zero_tax'))){
                        $changedInputs['zero_tax'] = 'unchanged';
                    }


                }else{
                    $changedInputs[$name] = "unchanged";
                }
            }
            // removing unchanged items cause no validation is needed
            foreach (array_keys($changedInputs, 'unchanged') as $key) {
                unset($changedInputs[$key]);

            }
            // dd($changedInputs);
            $user = Auth::user();

            $userid = $user->merchant_id;
                $validator = Validator::make($changedInputs, [
                'name' => [
                    'sometimes',
                    'required',
                ],
                'cost' => ['sometimes','required','numeric'],
                'tax_id' =>['sometimes','required_without:zero_tax'],
                'zero_tax' =>['sometimes','required_without:tax_id'],
                'category' =>['sometimes','required'],
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }else{

                if(in_array("cost",array_keys($changedInputs))){
                    $taxPercentage = TaxType::where('id', $request->tax_id)->first()->percentage;

                    $costTaxExclusive = Helper::excludeTax($request->cost,$taxPercentage);
                    $changedInputs['cost_tax_exclusive'] = $costTaxExclusive;
                    $changedInputs['cost_tax_exclusive_rounded'] = round($costTaxExclusive,2);
                }elseif(in_array("tax_id",array_keys($changedInputs))){

                    $taxPercentage = TaxType::where('id', $request->tax_id)->first()->percentage;

                    $costTaxExclusive = Helper::excludeTax($request->cost,$taxPercentage);
                    $changedInputs['cost_tax_exclusive'] = $costTaxExclusive;
                    $changedInputs['cost_tax_exclusive_rounded'] = round($costTaxExclusive,2);

                }elseif(in_array("zero_tax",array_keys($changedInputs))){

                    $taxId = TaxType::where(["percentage" => 0, 'code' => "0"])->first()->id;
                    $changedInputs['cost_tax_exclusive'] = $request->cost;
                    $changedInputs['cost_tax_exclusive_rounded'] = round($request->cost,2);
                    $changedInputs['tax_id'] = $taxId;

                    unset($changedInputs['zero_tax']);
                }

                // dd($changedInputs);
                if(!empty($changedInputs)){
                    DB::table('merchant_expenses')->where('id',$id)->update($changedInputs);
                    return redirect("/expenses")->with('success', __('messages.updated_successfully'));
                }else{
                    return back();
                }

            }
        }else{
            return back();
        }
    }
    public function delete($id, Request $request){
        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['expenses_delete'] || $user->main_account){

            MerchantExpense::where('id', $id)->delete();
            return redirect("/expenses")->with('success', __('messages.deleted_successfully'));
        }else{
            return back();
        }
    }

}
