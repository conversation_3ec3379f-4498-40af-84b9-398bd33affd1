<?php

namespace App\Http\Controllers\Application\Web;

use App\Http\Controllers\MerchantExpense;
use App\Http\Controllers\TaxType;
use App\Http\Requests\Application\Web\Customer\CreateCustomerRequest;
use App\Http\Requests\Application\Web\Customer\UpdateCustomerRequest;
use App\Models\MerchantCustomer;
use App\Services\BusinessContextService;
use App\Services\CustomerService;
use BCMathExtended\BC;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CustomersController extends Controller
{
    protected $customerService;

    public function __construct(CustomerService $customerService)
    {
        $this->customerService = $customerService;
    }
    public function index(){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['customers_show']){
            return View("theme::marble.customers")->with(['title' => 'العملاء']);
        }else{
            return back();
        }
    }

    public function display($id){
        $user = getCurrentUser();

        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['customers_show']){
            $customer = $user->customers()->whereId($id)->first();
            $customerInvoiceQuery = $user->saleInvoices()->where( 'customer_id', $id)->with('salePaymentMethods');
            $invoices = $customerInvoiceQuery->get();
            $totalPaid = 0;
            $totalPostPaid = 0;
            $totalReceived = 0;
            $sumPaid = 0;

            foreach($invoices as $invoice){
                if(!$invoice['has_postpay']){
                    $totalPaid = BC::add($totalPaid,$invoice['total_tax_inclusive'],10);
                }else{
                    foreach($invoice->salePaymentMethods as $payment){
                        if($payment['is_postpay']){
                            $totalPostPaid = BC::add($totalPostPaid,$payment['amount'],10);
                        }else{
                            if($payment['invoice_type'] == 'receive'){

                                $totalReceived = BC::add($totalReceived,$payment['amount'],10);
                            }
                            $totalPaid = BC::add($totalPaid,$payment['amount'],10);

                        }
                    }
                }
                $sumPaid = BC::add($sumPaid,$invoice['total_tax_inclusive'],10);

            }
            $remainingPaid = BC::add($totalPostPaid,$totalReceived,10);

            return View("theme::marble.customer-details")->with(['customer'=> $customer,'remainingPaid' => $remainingPaid,'sumPaid'=> $sumPaid,'totalPaid' => $totalPaid,'totalPostPaid' => $totalPostPaid,'invoices' => $invoices,'main_account' => $main_account,'permissions' => $permissions, 'title' => 'مشاهدة عميل']);
        }else{
            return back();
        }
    }

    public function add(Request $request){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['customers_add']){
            $plan = $user->plan;
            if($plan->is_customers_supported){
                return view('theme::marble.customers-add')->with([
                        'title' => 'إضافة عميل'
                ]);
            }else{
                return View("theme::marble.needs-subscription", ['title' => 'إضافة عميل']);
            }
        }else{
            return back();
        }
    }

    public function edit($id,Request $request){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['customers_edit']){
            $customer = $user->customers()->whereId($id)->first();
            $address = $customer->address()->first();

            $plan = $user->plan;

            $mcustomer = array(
                "name" => $customer->name,
                "phone_number" => $customer->phone_number,
                "address" => $customer->address,
                "vat_number" => $customer->vat_number,
                "notes" => $customer->notes,
                'street' => $address?->street ?: '',
                'building_number' => $address?->building_number ?: '',
                'postal_code' => $address?->postal_code ?: '',
                'state_id' => $address?->state_id ?: '',
                'city_id' =>  $address?->city_id ?: '',
                'district' =>  $address?->district ?: '',
            );

            $session = $request->session()->put('mcustomer',$mcustomer);

            if($plan->is_suppliers_supported){
                return view('theme::marble.customers-edit')->with([
                        'customer' => $customer,
                        'address' => $address,
                        'title' => 'تعديل مورد',
                ]);
            } else {
                return View("theme::marble.needs-subscription", ['title' => 'تعديل مورد']);
            }
        }else{
            return back();
        }
    }

    public function store(CreateCustomerRequest $request){
        $user = getCurrentUser();

        if($user->main_account || $user->group->permissions['customers_add']){
            $plan = $user->plan;

            if(!$plan->is_customers_supported){
                return redirect('customers');
            }

            // Prepare customer data
            $customerData = [
                "name" => $request->name,
                'phone_number' => $request->phone_number,
                'notes' => $request->notes,
                'vat_number' => $request->vat_number,
            ];

            // Prepare address data if needed
            $addressData = null;
            if ($request->street || $request->vat_number) {
                $addressData = [
                    'street' => $request->street,
                    'building_number' => $request->building_number,
                    'postal_code' => $request->postal_code,
                    'state_id' => $request->state_id,
                    'city_id' => $request->city_id,
                    'district' => $request->district,
                ];
            }

            // Create customer using the service
            $this->customerService->createCustomer($customerData, $addressData);

            if (request()->has('redirect_url'))
            {
                return redirect(request()->get('redirect_url'))->with('success', __('messages.created_successfully'));
            }

            return redirect('/customers')->with('success', __('messages.created_successfully'));
        }else{
            return back();
        }
    }

    public function update($id,UpdateCustomerRequest $request){

        $originalInputs = $request->session()->get('mcustomer');

        $user = getCurrentUser();
        $main_account = $user->main_account;

        $customer = $user->customers()->whereId($id)->first();

        $permissions = $user->group->permissions;
        if($main_account || $permissions['customers_edit']){
            $plan = $user->plan;

            if(!$plan->is_customers_supported){
                return redirect('customers');
            }

            foreach ($originalInputs as $name => $value) {
                // Handle main settings changes
                if ($name == 'name' || $name == 'phone_number' || $name == "vat_number" || $name == 'notes') {
                    if ($request->input($name) != $value) {
                        $changedSettings[$name] = $request->input($name);
                    }else{
                        $changedSettings[$name] = "unchanged";
                    }
                }

                // Handle address changes
                if ($name == 'street' || $name == 'building_number' || $name == 'postal_code' || $name == 'state_id' || $name == 'city_id' || $name == 'district') {
                    if ($request->input($name) != $value) {
                        $changedAddress[$name] = $request->input($name);
                    }else{
                        $changedAddress[$name] = "unchanged";
                    }
                }
            }

            //removing unchanged items cause no validation is needed
            foreach (array_keys($changedAddress, 'unchanged') as $key) {
                unset($changedAddress[$key]);
            }

            foreach (array_keys($changedSettings, 'unchanged') as $key) {
                unset($changedSettings[$key]);
            }

            if (!empty($changedSettings)) {
                $customer->update($changedSettings);
            }

            // Update address in database
            if (!empty($changedAddress)) {
                $customer->address()
                    ->updateOrCreate([
                        'merchant_id' => BusinessContextService::getMerchantId(),
                        'business_id' => BusinessContextService::getBusinessId(),
                    ], $changedAddress);
            }

            if (request()->has('redirect_url'))
            {
                return redirect(request()->get('redirect_url'))->with('success', __('messages.updated_successfully'));
            }

            return redirect('/customers')->with('success', __('messages.updated_successfully'));
        }else{
            return back();
        }
    }
}
