<?php

namespace App\Http\Controllers\Application\Web;

use App\Enums\Application\InstalledAppStatus;
use App\Enums\Application\TaxCodeEnum;
use App\Enums\Application\Zatca\ZatcaInvoiceStatus;
use App\Jobs\Application\ZatcaUsersQuarterReportJob;
use App\Models\Address;
use App\Models\AutoNumber;
use App\Models\MerchantCustomer;
use App\Models\MerchantInvoice;
use App\Models\MerchantsProduct;
use App\Models\MerchantUser;
use App\Models\OasisMarketplace;
use App\Services\BusinessContextService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Storage;

class MarketplaceController extends Controller
{

    final public function index(): View
    {
        $marketplaceData = OasisMarketplace::with(['installedApps' => function ($query) {
            $query->limit(1);
        }])
            ->select('id', 'name', 'name_ar', 'price_tax_inclusive', 'description', 'image')
            ->get();

        return View("theme::marble.marketplace")->with([
            'title' => 'متجر التطبيقات',
            'marketplaceData' => $marketplaceData
        ]);
    }

    final public function details(OasisMarketplace $marketplace): View
    {
        $marketplace->load(['installedApps' => function ($query) {
            $query->limit(1);
        }]);
        $invoices = MerchantInvoice::query()
        ->whereNotNull('zatca_uuid')
        ->whereNotNull('zatca_invoice_hash')
        ->whereNotNull('zatca_previous_invoice_hash');

        $customers = MerchantCustomer::query()
            ->whereNotNull('vat_number')
            ->where(function ($query) {
                $query->whereRaw('CHAR_LENGTH(vat_number) < 15')
                    ->orWhere('vat_number', 'not like', '3%3')
                    ->orWhereHas('address', function ($query) {
                        $query->whereNull('state_id')
                            ->orWhereNull('city_id')
                            ->orWhereNull('street')
                            ->orWhereNull('postal_code')
                            ->orWhereNull('building_number')
                            ->orWhereNull('district');
                    });
            })
            ->select('id', 'name', 'vat_number')
            ->get();

        $products = MerchantsProduct::query()
            ->where(function ($query) {
                $query->whereNull('name_en')
                    ->orWhereHas('tax', function ($query) {
                        $query->where('percentage', 0)
                            ->where('code', TaxCodeEnum::NONE->value);
                    });
            })
            ->select('id', 'name', 'name_en')
            ->get();

        $invoicesStatuses = $invoices
        ->selectRaw('zatca_status, COUNT(*) as count')
        ->groupBy('zatca_status')
        ->pluck('count', 'zatca_status');

        $reportedCount = $invoicesStatuses->get(ZatcaInvoiceStatus::REPORTED->value, 0);
        $clearedCount = $invoicesStatuses->get(ZatcaInvoiceStatus::CLEARED->value, 0);
        $combinedReported = $reportedCount + $clearedCount;
        $failedCount = $invoicesStatuses->get(ZatcaInvoiceStatus::FAILED->value, 0);
        $inProgressCount = $invoicesStatuses->get(ZatcaInvoiceStatus::IN_PROGRESS->value, 0);


        $invoicesCount = $invoices->count();

        $last25Invoices = $invoices
        ->limit(25)
        ->select('id','pretty_id','zatca_status','zatca_invoice_name','created_at')
        ->orderBy('created_at','desc')
        ->groupBy('id')
        ->get();

        ZatcaUsersQuarterReportJob::dispatch(BusinessContextService::getMerchantId(), BusinessContextService::getMerchantId());
        return View("theme::marble.marketplace-details")->with([
            'title' => 'متجر التطبيقات',
            'marketplaceData' => $marketplace,
            'merchantUser' => getCurrentUser(),
            'installedAppData' => $marketplace->installedApps->first(),
            'invoicesCount' => $invoicesCount,
            'combinedReported' => $combinedReported,
            'failedCount' => $failedCount,
            'inProgressCount' => $inProgressCount,
            'last25Invoices' => $last25Invoices,
            'customers' => $customers,
            'products' => $products,

        ]);
    }

    final public function install(OasisMarketplace $marketplace): View
    {
        $user = getCurrentUser();
        $user->load('setting','registries');
        $address = Address::where('addressable_id', $user->merchant_id)
            ->where('addressable_type', MerchantUser::class)
            ->first();
        $cr = $user->setting->CR;
        $vat = $user->setting->VAT;
        $issues = [];

        if(empty($cr) || strlen($cr) != 10){
            $issues['cr'] =  Lang::get('messages.CR_10');
        }
        if(empty($vat) || strlen($vat) != 15){
            $issues['vat'] = Lang::get('messages.VAT_15');
        }
        if(
            empty($address->street) ||
            empty($address->building_number) ||
            empty($address->postal_code) ||
            empty($address->state_id) ||
            empty($address->city_id) ||
            empty($address->district)
        ){
            $issues['address'] = Lang::get('messages.address_issues');
        }


        return View("theme::marble.marketplace-install")->with([
            'title' => 'متجر التطبيقات',
            'issues' => $issues,
            'registers' => $user->registries,
            'marketplace' => $marketplace,
            'user' => $user,
        ]);
    }

    final public function deactivate(OasisMarketplace $marketplace): ?RedirectResponse
    {
        $merchantUser = getCurrentUser();

        DB::transaction(function () use ($marketplace, $merchantUser){

            $merchantUser->installedApps()->where('app_id', $marketplace->id)->update([
                'status' => InstalledAppStatus::INACTIVE->value
            ]);

            $merchantUser->setting()->update([
                'zatca_request_id' => null,
                'zatca_binary_security_token' => null,
                'zatca_secret' => null,
            ]);

            $merchantUser->registries()->update([
                'zatca_request_id' => null,
                'zatca_binary_security_token' => null,
                'zatca_secret' => null,
            ]);

            $merchantUser->zatcaIntegrationSteps()->delete();

            AutoNumber::query()->delete();

            Storage::deleteDirectory('configurations/merchant_user_' . $merchantUser->id);
        });

        return redirect()->route('marketplace.details', ['marketplace' => $marketplace->id]);
    }
}
