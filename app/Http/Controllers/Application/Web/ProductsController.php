<?php

namespace App\Http\Controllers\Application\Web;

use App\Actions\Application\Web\Products\CreateCompositeProductAction;
use App\Actions\Application\Web\Products\CreateProductAction;
use App\Actions\Application\Web\Products\UpdateCompositeProductAction;
use App\Actions\Application\Web\Products\CreateVariantProductAction;
use App\Actions\Application\Web\Products\UpdateVariantProductAction;
use App\Enums\Application\ProductStatus;
use App\Actions\Application\Web\Products\UpdateProductAction;
use App\Enums\Application\ProductTypeEnum;
use App\Exports\ProductExport;
use App\Exports\ProductTemplateExport;
use App\Http\Requests\Application\Web\Product\CreateProductRequest;
use App\Http\Requests\Application\Web\Product\CreateCompositeProductRequest;
use App\Http\Requests\Application\Web\Product\UpdateCompositeProductRequest;
use App\Http\Requests\UpdateVariantProductRequest;
use App\Http\Requests\CreateVariantProductRequest;
use App\Imports\ProductImport;
use App\Models\MerchantBranch;
use App\Models\MerchantCategory;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantsProduct;
use App\Models\MerchantTag;
use App\Models\MerchantProductVariant;
use App\Models\TaxType;
use App\Services\BusinessContextService;
use Milon\Barcode\DNS1D;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;


class ProductsController extends Controller
{
    public function index(){
        $user = getCurrentUser();
        $permissions = $user->group->permissions;
        if($permissions->products_show || $user->main_account){
            return view('theme::marble.products')->with(['title' => 'المخزون']);
        }else{
            return redirect('');
        }
    }

    public function add(Request $request){
        $user = getCurrentUser();
        $categories = MerchantCategory::query()->get();
        $tax_types = TaxType::query()
            ->when($user->isZatcaEnabled, function ($query) {
                return $query->whereNotNull('code');
            })->get();
        $main_account = $user->main_account;
        $tags = MerchantTag::query()->get();
        if($main_account){
            $branches = MerchantBranch::query()->get();
        }else{
            $permittedBranches = $user->branches->pluck('branch_id');
            $branches = MerchantBranch::query()->whereIn('id',$permittedBranches)->get();
        }
        $permissions = $user->group->permissions;

        $routeUri = $request->route()->uri();
        if (
            ( $routeUri === 'products/weighted/add' && !$permissions->add_weighted_products) ||
            ( $routeUri === 'products/service/add' && !$permissions->products_add) ||
            ( $routeUri === 'products/add' && !$permissions->products_add)
        ){
            return redirect('products');
        }

        $weightedProductSetting = null;
        $productType = ProductTypeEnum::SIMPLE->value;

        if ($routeUri === 'products/weighted/add') {
            $weightedProductSetting = getWeightedProductSetting();
            $productType = ProductTypeEnum::WEIGHTED->value;
        } elseif ($routeUri === 'products/service/add') {
            $productType = ProductTypeEnum::SERVICE->value;
        } elseif ($routeUri === 'products/composite/add') {
            $productType = ProductTypeEnum::COMPOSITE->value;
        }
        return view('theme::marble.products-add')->with([
            'categories' => $categories,
            'tax_types' => $tax_types,
            'branches' => $branches,
            'permissions' => $permissions,
            'main_account' => $main_account,
            'weighted_product_setting' => $weightedProductSetting,
            'product_type' => $productType,
            'title' => 'إضافة منتج',
            'tags' => $tags,
        ]);
    }

    public function add_composite(Request $request){
        $user = getCurrentUser();
        $categories = MerchantCategory::query()->get();
        $tax_types = TaxType::query()
            ->when($user->isZatcaEnabled, function ($query) {
                return $query->whereNotNull('code');
            })->get();
        $main_account = $user->main_account;

        $tags = MerchantTag::query()->get();

        if($main_account){
            $branches = MerchantBranch::query()->get();
        }else{
            $permittedBranches = $user->branches->pluck('branch_id');
            $branches = MerchantBranch::query()->whereIn('id',$permittedBranches)->get();
        }
        $permissions = $user->group->permissions;

        $weightedProductSetting = null;
        $productType = ProductTypeEnum::SIMPLE->value;

        $weightedProductSetting = getWeightedProductSetting();

        return view('theme::marble.product-composite-add')->with([
            'categories' => $categories,
            'tax_types' => $tax_types,
            'branches' => $branches,
            'permissions' => $permissions,
            'main_account' => $main_account,
            'weighted_product_setting' => $weightedProductSetting,
            'product_type' => $productType,
            'title' => 'إضافة منتج',
            'tags' => $tags,
        ]);
    }

    public function add_variant(Request $request){
        $user = getCurrentUser();
        $permissions = $user->group->permissions;
        $productType = ProductTypeEnum::VARIANT->value;
        $tax_types = TaxType::query()
        ->when($user->isZatcaEnabled, function ($query) {
            return $query->whereNotNull('code');
        })->get();

        if(!$permissions->products_add){
            return redirect('products');
        }
        $main_account = $user->main_account;
        if($main_account){
            $branches = MerchantBranch::query()->get();
        }else{
            $permittedBranches = $user->branches->pluck('branch_id');
                $branches = MerchantBranch::query()->whereIn('id',$permittedBranches)->get();
        }

        $categories = MerchantCategory::query()->get();

        return view('theme::marble.product-variant-add')->with([
            'categories' => $categories,
            'tax_types' => $tax_types,
            'branches' => $branches,
            'permissions' => $permissions,
            'main_account' => $main_account,
            'product_type' => $productType,
            'title' => 'إضافة منتج متعدد'
        ]);
    }

    public function edit(MerchantsProduct $product, Request $request){
        $user = getCurrentUser();
        $taxQuery = TaxType::query()
                    ->when($user->isZatcaEnabled, function ($query) {
                        return $query->whereNotNull('code');
                    });
        $tax_types = $taxQuery->get();
        $tags = MerchantTag::query()->get();

        $productTags = $product->tags()->with('tag')->get();

        if ($product->type === ProductTypeEnum::COMPOSITE->value) {
            return redirect('products/edit/composite/'.$product->id);
        }

        $zero_tax = $taxQuery->where(['id'=> $product->tax_id,'code' => "0"])->count() && $taxQuery->where(['id'=>$product->tax_id,'code' => "0"])->first()->percentage == 0 ? true: false;

        $main_account = $user->main_account;
        if($main_account){
            $branches = MerchantBranch::query()
                ->with(['branchQuantity' => function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                }])
                ->get();
        }else{
            $permittedBranches = $user->branches->pluck('branch_id');
            $branches = MerchantBranch::query()
                ->whereIn('id',$permittedBranches)
                ->with(['branchQuantity' => function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                }])
                ->get();
        }

        $categories = MerchantCategory::query()
            ->get();
        $main_account = $user->main_account;
        // removing the unneeded info for $changed
        $mproduct = array(
            "name" => $product->name,
            "name_en" => $product->name_en,
            "barcode" => $product->barcode,
            "cost" => $product->cost,
            "status" => $product->status,
            "tax_id" => $product->tax_id,
            "tax_schema" => $product->tax_schema,
            'category' => $product->category_id,
            "branches" => $branches,
            'returnable' => $product->returnable,
            'zero_tax' => $zero_tax,
            'purchaseable' => $product->purchaseable,
            'sellable' => $product->sellable,
            'existingTags' => $productTags,
        );
        $request->session()->put('mproduct',$mproduct);

        $permissions = $user->group->permissions;

        if (
           ( $product->type == ProductTypeEnum::WEIGHTED->value && !$permissions->edit_weighted_products) ||
           ( $product->type == ProductTypeEnum::SIMPLE->value && !$permissions->products_edit) ||
           ( $product->type == ProductTypeEnum::SERVICE->value && !$permissions->products_edit)
        ){
            return redirect('products');
        }

        $weightedProductSetting = null;
        if ($product->type === ProductTypeEnum::WEIGHTED->value) {
            $weightedProductSetting = getWeightedProductSetting();
        }

        return view('theme::marble.products-edit')->with(
            [
                'product' => $product,
                'tax_types' => $tax_types,
                'branches' => $branches,
                'categories' => $categories,
                'title' => 'تعديل منتج',
                'permissions' => $permissions,
                'main_account' => $main_account,
                'zero_tax' => $zero_tax,
                'weighted_product_setting' => $weightedProductSetting,
                'product_type' => $product->type,
                'tags' => $tags,
                'existingTags' => $productTags,
            ]
        );
    }

    public function barcode(MerchantsProduct $product){
        $product = $product->toArray();
        $barcode = new DNS1D();
        $barcodeImage = $barcode->getBarcodePNG($product['barcode'], 'C128');
        return view('theme::marble.product_barcode')->with([
            'product' => $product,
            'barcodeImage' => $barcodeImage,
            'title' => 'طباعة الباركود',
            ]);
    }

    public function store(CreateProductRequest $request)
    {
        return app(CreateProductAction::class)($request);
    }

    public function store_composite(CreateCompositeProductRequest $request)
    {
        return app(CreateCompositeProductAction::class)($request);
    }
    public function store_variant(CreateVariantProductRequest $request)
    {
        return app(CreateVariantProductAction::class)($request);
    }

    public function update(MerchantsProduct $product, Request $request)
    {
        return app(UpdateProductAction::class)($product, $request);
    }

    public function update_composite(MerchantsProduct $product, UpdateCompositeProductRequest $request)
    {
        return app(UpdateCompositeProductAction::class)($product, $request);
    }
    public function update_variant(MerchantsProduct $product, UpdateVariantProductRequest $request)
    {
        return app(UpdateVariantProductAction::class)($product, $request);
    }

    public function delete(MerchantsProduct $product, Request $request){
        $user = getCurrentUser();
        $permissions = $user->group->permissions;
        if($permissions['products_delete'] || $user->main_account){

            MerchantProductBranchQuantity::where("product_id", $product->id)
                ->delete();
            $product->delete();
            return back()->with('success', __('messages.deleted_successfully'));
        }else{
            return redirect('products');
        }
    }

    public function export(Request $request){
        $search = $request->get('search');
        $status = $request->get('status');

        $user = getCurrentUser();

        if($user?->group?->permissions->products_export || $user->main_account){
            // Dispatch the job to process the export in the background
            \App\Jobs\ProcessProductExport::dispatch(
                $user->id,
                BusinessContextService::getMerchantId(),
                BusinessContextService::getBusinessId(),
                $search,
                $status,
                $user->email
            );

            // Redirect back with a simple success message
            return redirect('products')->with('success', __('messages.export_email_notification'));
        } else {
            return redirect('products');
        }
    }

    public function templateExport($type = 'simple'){
        $user = getCurrentUser();
        if($user?->group?->permissions->products_import || $user->main_account){

            // Validate product type
            $validTypes = ['simple', 'weighted', 'service', 'variant'];
            if (!in_array($type, $validTypes)) {
                $type = 'simple';
            }

            // Switch to appropriate template export class
            switch ($type) {
                case 'weighted':
                    // TODO: Implement WeightedProductTemplateExport
                    return Excel::download(new ProductTemplateExport(), 'weighted-products-import-template.xlsx');
                case 'service':
                    // TODO: Implement ServiceProductTemplateExport
                    return Excel::download(new ProductTemplateExport(), 'service-products-import-template.xlsx');
                case 'variant':
                    // TODO: Implement VariantProductTemplateExport
                    return Excel::download(new ProductTemplateExport(), 'variant-products-import-template.xlsx');
                case 'simple':
                default:
                    return Excel::download(new ProductTemplateExport(), 'simple-products-import-template.xlsx');
            }
        }else{
            return redirect('products');
        }
    }

    public function import(Request $request){
        $user = getCurrentUser();
        if($user?->group?->permissions->products_import || $user->main_account){

            return view('theme::marble.products-import');
        }else{
            return redirect('products');
        }
    }

    public function import_store(Request $request){

        $request->validate([
            'file' => 'required|mimes:xls,xlsx,csv|max:1024',
            'product_type' => 'nullable|string|in:simple,weighted,service,variant',
        ]);

        $productType = $request->input('product_type', 'simple');

        // Switch to appropriate import class
        switch ($productType) {
            case 'weighted':
                // TODO: Implement WeightedProductImport
                $import = new ProductImport();
                break;
            case 'service':
                // TODO: Implement ServiceProductImport
                $import = new ProductImport();
                break;
            case 'variant':
                // TODO: Implement VariantProductImport
                $import = new ProductImport();
                break;
            case 'simple':
            default:
                $import = new ProductImport();
                break;
        }

        Excel::import($import, $request->file('file'));

        $import->onFinish();

        if (!empty($import->failures())) {
            return back()->with('failures', $import->failures());
        }

        return redirect('products')->with('success', __('messages.imported_successfully'));
    }

    public function edit_composite(MerchantsProduct $product, Request $request)
    {
        $user = getCurrentUser();
        $taxQuery = TaxType::query()
                    ->when($user->isZatcaEnabled, function ($query) {
                        return $query->whereNotNull('code');
                    });
        $tax_types = $taxQuery->get();
        $tags = MerchantTag::query()->get();
        $existingTags = $product->tags()->with('tag')->get();
        // Ensure the product is a composite type and load its components
        if ($product->type !== ProductTypeEnum::COMPOSITE->value) {
            return redirect('products');
        }

        $product->load(['compositeComponents.componentProduct']);

        $zero_tax = $taxQuery->where(['id'=> $product->tax_id,'code' => "0"])->count() &&
                    $taxQuery->where(['id'=>$product->tax_id,'code' => "0"])->first()->percentage == 0 ? true : false;

        $main_account = $user->main_account;

        // Get branches with their quantities
        if ($main_account) {
            $branches = MerchantBranch::query()
                ->with(['branchQuantity' => function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                }])
                ->get();
        } else {
            $permittedBranches = $user->branches->pluck('branch_id');
            $branches = MerchantBranch::query()
                ->whereIn('id', $permittedBranches)
                ->with(['branchQuantity' => function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                }])
                ->get();
        }

        $categories = MerchantCategory::query()
            ->get();

        // Store product data in session for comparison
        $mproduct = [
            "name" => $product->name,
            "name_en" => $product->name_en,
            "barcode" => $product->barcode,
            "status" => $product->status,
            "tax_id" => $product->tax_id,
            "tax_schema" => $product->tax_schema,
            'category' => $product->category_id,
            "branches" => $branches,
            'returnable' => $product->returnable,
            'zero_tax' => $zero_tax,
            'purchaseable' => $product->purchaseable,
            'sellable' => $product->sellable,
        ];
        $request->session()->put('mproduct', $mproduct);

        $permissions = $user->group->permissions;

        // Check permissions
        if (!$permissions->products_edit && !$user->main_account) {
            return redirect('products');
        }

        // Format existing components data for the Livewire component
        $existingComponents = [];
        foreach ($product->compositeComponents as $component) {
            $existingComponents[$component->component_product_id] = $component->quantity;
        }
        return view('theme::marble.products-composite-edit')->with([
            'product' => $product,
            'tax_types' => $tax_types,
            'branches' => $branches,
            'categories' => $categories,
            'title' => 'تعديل منتج مركب',
            'permissions' => $permissions,
            'main_account' => $main_account,
            'zero_tax' => $zero_tax,
            'product_type' => ProductTypeEnum::COMPOSITE->value,
            'products' => $existingComponents,
            'tags' => $tags,
            'existingTags' => $existingTags,
        ]);
    }

    public function edit_variant(MerchantsProduct $product)
    {
        $user = getCurrentUser();
        $taxQuery = TaxType::query()
                    ->when($user->isZatcaEnabled, function ($query) {
                        return $query->whereNotNull('code');
                    });

        // Ensure the product is a variant type
        if ($product->type !== ProductTypeEnum::VARIANT->value) {
            return redirect('products');
        }

        // Load the product's variants and their options
        $product->load(['variants']);

        $tax_types = $taxQuery->get();

        $zero_tax = $taxQuery->where(['id'=> $product->tax_id,'code' => "0"])->count() &&
                    $taxQuery->where(['id'=>$product->tax_id,'code' => "0"])->first()->percentage == 0 ? true : false;


        $main_account = $user->main_account;

        // Get branches with their quantities
        if ($main_account) {
            $branches = MerchantBranch::query()
                ->with(['branchQuantities' => function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                }])
                ->get();
        } else {
            $permittedBranches = $user->branches->pluck('branch_id');
            $branches = MerchantBranch::query()
                ->whereIn('id', $permittedBranches)
                ->with(['branchQuantities' => function ($query) use ($product) {
                    $query->where('product_id', $product->id);
                }])
                ->get();
        }

        $categories = MerchantCategory::query()->get();

        $permissions = $user->group->permissions;

        // Check permissions
        if (!$permissions->products_edit && !$user->main_account) {
            return redirect('products');
        }

        // Format existing variants data for the Livewire component
        $existingVariants = [];
        foreach ($product->variants as $variant) {
            $existingVariants[] = [
                'id' => $variant->id,
                'name' => $variant->name,
            ];
        }

        // Directly retrieve all existing variant products from the database with all relevant relationships
        $variantProducts = MerchantProductVariant::query()
            ->where('product_id', $product->id)
            ->with([
                'variantAttributes.variant',
                'variantAttributes.variantOption',
                'branchQuantities' // This loads the branch quantities relationship
            ])
            ->get();

        // Prepare the existing products array for the Livewire component
        $existingProducts = [];
        foreach ($variantProducts as $variantProduct) {
            // Skip variants without attributes
            if ($variantProduct->variantAttributes->isEmpty()) {
                continue;
            }

            // Extract variant IDs and option IDs for this product
            $variantIds = [];
            $variantOptionIds = [];

            foreach ($variantProduct->variantAttributes as $attribute) {
                $variantIds[] = $attribute->variant_id;
                $variantOptionIds[] = $attribute->variant_option_id;
            }

            // Format branch values for this product
            $branchValues = [];
            foreach ($branches as $branch) {
                // Find the branch quantity record for this branch and variant
                $branchQuantity = $variantProduct->branchQuantities
                    ->where('branch_id', $branch->id)
                    ->first();

                // If branch quantity exists, use its values, otherwise default to 0
                $branchValues[$branch->id] = [
                    'sell_price' => $branchQuantity ? $branchQuantity->sell_price : '0',
                    'wholesale_price' => $branchQuantity ? $branchQuantity->wholesale_price : '0',
                    'quantity' => $branchQuantity ? $branchQuantity->quantity : '0',
                    'cost' => $variantProduct->cost ?? '0', // Include variant cost for each branch
                ];
            }

            // Build the product name
            $optionNames = [];
            foreach ($variantProduct->variantAttributes as $attribute) {
                if ($attribute->variantOption) {
                    $optionNames[] = $attribute->variantOption->name;
                }
            }
            $variantPart = implode(' - ', $optionNames);
            $fullName = trim("{$product->name} - {$variantPart}", ' -');

            // Build the product structure expected by VariantProductList component
            $existingProducts[] = [
                'product_name' => $fullName,
                'name' => $fullName,
                'barcode' => $variantProduct->barcode,
                'cost' => $variantProduct->cost,
                'variant_id' => $variantIds,
                'variant_option_id' => $variantOptionIds,
                'branch_values' => $branchValues,
            ];
        }

        return view('theme::marble.products-variant-edit')->with([
            'product' => $product,
            'tax_types' => $tax_types,
            'branches' => $branches,
            'categories' => $categories,
            'title' => 'تعديل منتج متعدد',
            'permissions' => $permissions,
            'main_account' => $main_account,
            'zero_tax' => $zero_tax,
            'product_type' => ProductTypeEnum::VARIANT->value,
            'variants' => $existingVariants,
            'oldProducts' => $existingProducts
        ]);
    }

}
