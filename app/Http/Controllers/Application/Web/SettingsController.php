<?php

namespace App\Http\Controllers\Application\Web;

use App\Dto\Application\Web\WeightedProductSettingDto;
use App\Enums\WeightedProductStatus;
use App\Http\Requests\Application\Web\Setting\WeightedProductSettingRequest;
use App\Http\Requests\UpdatePaymentMethodRequest;
use App\MerchantProducts;
use App\Models\Address;
use App\Models\MercahntPaymentMethodRegister;
use App\Models\MerchantBranch;
use App\Models\MerchantCurrency;
use App\Models\MerchantPaymentMethod;
use App\Models\MerchantPaymentMethodRegister;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantProductVariant;
use App\Models\MerchantRegisterSetting;
use App\Models\MerchantRegistery;
use App\Models\MerchantsProduct;
use App\Models\MerchantsSetting;
use App\Models\MerchantTag;
use App\Models\MerchantUser;
use App\Models\TaxType;
use App\Models\WeightedProductSetting;
use App\Rules\VatNumberStartEndWithThree;
use App\Services\BusinessContextService;
use Exception;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Illuminate\View\View;


class SettingsController extends Controller
{
    public function main(){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_main_show'] || $permissions['settings_show_payment_methods'] || $permissions['settings_show_taxes'] || $permissions['settings_show_currencies']){

            $settings = MerchantsSetting::query()->first();
            $payments = MerchantPaymentMethod::query()->get()->toArray();
            $taxes = TaxType::query()->get()->toArray();
            $users = MerchantUser::where('merchant_id', BusinessContextService::getMerchantId())
                ->where('business_id', BusinessContextService::getBusinessId())
                ->get()
                ->toArray();
            $currencies = MerchantCurrency::query()->get()->toArray();
            $address = Address::where('addressable_id', $user->merchant_id)
                ->where('addressable_type', MerchantUser::class)
                ->first();
            $tags = MerchantTag::query()->get()->toArray();

            return view('theme::marble.settings',[
                'payments' => $payments,
                'taxes' => $taxes,
                'settings' => $settings,
                'users' => $users,
                'address' => $address,
                'currencies' => $currencies,
                'main_account' => $main_account,
                'permissions' => $permissions,
                'title' => 'الاعدادات الرئيسية',
                'tags' => $tags,
                ]);

        }else{
            return back();
        }
    }
    public function main_edit(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        $model = 'MerchantUser';
        if($main_account || $permissions['settings_main_edit']){

            $settings = $user->setting()->first();
            $address = Address::where('addressable_id', $user->merchant_id)
                ->where('addressable_type', MerchantUser::class)
                ->first();
            $settingsSession = array(
                "name" => $settings?->name ?: '',
                "logo" => $settings?->logo ?: '',
                "address" => $settings?->address ?: '',
                "vat" => $settings?->VAT ?: '',
                "phone" => $settings?->phone ?: '',
                "CR" => $settings?->CR ?: '',
                'street' => $address?->street ?: '',
                'building_number' => $address?->building_number ?: '',
                'postal_code' => $address?->postal_code ?: '',
                'state_id' => $address?->state_id ?: '',
                'city_id' =>  $address?->city_id ?: '',
                'district' =>  $address?->district ?: '',
                'business_category' => $address?->business_category ?: '',
            );
            $request->session()->put('msettings',$settingsSession);

            return view('theme::marble.settings-main-edit',['settings' => $settings,'address' => $address,'model' => $model,'user' => $user,'title' => 'الاعدادات الرئيسية']);
        }else{
            return back();
        }
    }

    public function main_edit_store(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        // dd($request,$user->merchant_id);
        if($main_account || $permissions['settings_main_edit']){

            $originalInputs = $request->session()->get('msettings');
            $changedSettings = [];
            $changedAddress = [];

            // Check each input to see if it has changed
            foreach ($originalInputs as $name => $value) {
                // Check if the logo has been uploaded
                if ($request->hasFile('logo')) {
                    $changedSettings['logo'] = $request->file('logo');
                }

                // Handle main settings changes
                if ($name == 'name' || $name == 'vat' || $name == 'phone' || $name == 'CR' || $name == 'business_category') {
                    $changedSettings[$name] = $request->input($name);
                }

                // Handle address changes
                if ($name == 'street' || $name == 'building_number' || $name == 'postal_code' || $name == 'state_id' || $name == 'city_id' || $name == 'district') {
                    $changedAddress[$name] = $request->input($name);
                }
            }

            $validatorSettings = Validator::make($changedSettings, [
                'name' => [
                    'required',
                    'string',
                    'max:255',
                ],
                'business_category' => [
                    'required',
                    'string',
                    'max:255',
                ],
                'logo' => [
                    'nullable',
                    File::image()->max(1024 * 5)
                ],
                'vat' => [
                    'nullable',
                    'integer',
                    'digits:15',
                    new VatNumberStartEndWithThree()
                ],
                'CR' => [
                    'required',
                    'integer',
                    'digits:10',
                ],
                'phone' => [ 'required', 'string']
            ]);

            // Validate address settings
            $validatorAddress = Validator::make($changedAddress, [
                'street' => ['required', 'string', 'max:255'],
                'building_number' => ['required', 'integer'],
                'postal_code' => ['required', 'string', 'max:255'],
                'state_id' => ['required', 'exists:states,id'],
                'city_id' => ['required', 'exists:cities,id'],
                'district' => ['required', 'string', 'max:255'],
            ]);

            // Handle validation failures
            if ($validatorSettings->fails() || $validatorAddress->fails()) {
                if($validatorSettings->fails()){
                    return back()
                    ->withErrors($validatorSettings)
                    ->withInput();
                }else{
                    return back()
                    ->withErrors($validatorAddress)
                    ->withInput();
                }
            } else {

                // Update logo path if new logo is uploaded
                if (!empty($request->file('logo'))) {
                    $path = $request->file('logo')->store('logos');
                    $changedSettings['logo'] = $path;
                }

                // Update main settings in database
                if (!empty($changedSettings)) {
                    $user->setting()
                        ->update($changedSettings);
                }

                // Update address in database
                if (!empty($changedAddress)) {
                        Address::where('addressable_id', $user->merchant_id)
                            ->where('addressable_type', MerchantUser::class)
                            ->updateOrCreate([
                                'merchant_id' => BusinessContextService::getMerchantId(),
                                'business_id' => BusinessContextService::getBusinessId(),
                                'addressable_id' => $user->merchant_id,
                                'addressable_type' => MerchantUser::class,
                                ], $changedAddress);
                }

                if (request()->has('redirect_url'))
                {
                    return redirect(request()->get('redirect_url'))->with('success', __('messages.updated_successfully'));
                }

                return redirect('/settings/main')->with('success', __('messages.updated_successfully'));
                }
        }
    }

    public function payments_add(){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_add_payment_methods']){
            return view('theme::marble.payments-add',['title' => 'اضافة طريقة دفع']);
        }else{
            return back();
        }

    }
    public function payments_edit(MerchantPaymentMethod $paymentMethod, Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['settings_edit_payment_methods']){
            // removing the unneeded info for $changed
            $mpayment = array(
                "name" => $paymentMethod->name,
                "is_cash" => $paymentMethod->is_cash,
                "is_rounding_activiated" => $paymentMethod->is_rounding_activiated,
            );

            $session = $request->session()->put('mpayment',$mpayment);

            return view('theme::marble.payments-edit',[
                'payment' => $paymentMethod,
                'title' => 'تعديل طريقة دفع'
            ]);
        }else{
            return back();
        }

    }

    public function payments_add_store(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_add_payment_methods']){

            $userid = $user->merchant_id;
            $validator = Validator::make($request->all(), [
                'name' => [
                    'required',
                    Rule::unique('merchant_payment_methods')->where(function ($query) use($userid) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    })
                ],
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            $createPayment = MerchantPaymentMethod::create([
                "name" => $request->name,
                'is_cash' => $request->is_cash?1:0,
                'is_rounding_activiated' => $request->is_rounding_activiated?1:0,
            ]);

            return redirect("/settings/payments/edit/{$createPayment['id']}")->with('success', __('messages.created_successfully'));

        }else{
            return back();
        }
    }

    public function payments_edit_store(UpdatePaymentMethodRequest $request, MerchantPaymentMethod $paymentMethod){
        $originalInputs = $request->session()->get('mpayment');
        $changedInputs = [];

        foreach ($originalInputs as $name => $value) {
            if ($request->input($name) != $value) {
                if($name == 'is_cash' || $name == 'is_rounding_activiated'){
                    $changedInputs[$name] = $request->input($name)?1:0;
                } else {
                    $changedInputs[$name] = $request->input($name);
                }
            }else{
                $changedInputs[$name] = "unchanged";
            }
        }

        //removing unchanged items cause no validation is needed
        foreach (array_keys($changedInputs, 'unchanged') as $key) {
            unset($changedInputs[$key]);
        }

        if(!empty($changedInputs)){
            $paymentMethod->update($changedInputs);
            return back()->with('success', __('messages.updated_successfully'));
        }else{
            return back();
        }
    }

    public function tags_add(){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['settings_add_payment_methods']){
            return view('theme::marble.tags-add',['title' => 'اضافة علامة']);
        }else{
            return back();
        }

    }
    public function tags_edit($id, Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['settings_edit_payment_methods']){
            $tag = MerchantTag::where('id', $id)->first();
            // removing the unneeded info for $changed
            $mtag = array(
                "name" => $tag->name,
            );

            $session = $request->session()->put('mtag',$mtag);



            return view('theme::marble.tags-edit',['tag' => $tag,'title' => 'تعديل علامة']);
        }else{
            return back();
        }

    }
    public function tags_add_store(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_add_payment_methods']){

            $userid = $user->merchant_id;
            $validator = Validator::make($request->all(), [
                'name' => [
                    'required',
                    Rule::unique('merchant_tags')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    })
                ],
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            $createTag = MerchantTag::create([
                "name" => $request->name,
            ]);

            return redirect("/settings/tags/edit/{$createTag['id']}")->with('success', __('messages.created_successfully'));
        }else{
            return back();
        }
    }

    public function tags_edit_store(Request $request){
        $user = getCurrentUser();

        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_edit_payment_methods']){

            $originalInputs = $request->session()->get('mtag');
            $changedInputs = [];
            foreach ($originalInputs as $name => $value) {
                if ($request->input($name) != $value) {
                    $changedInputs[$name] = $request->input($name);
                }else{
                    $changedInputs[$name] = "unchanged";
                }
            }
            //removing unchanged items cause no validation is needed
            foreach (array_keys($changedInputs, 'unchanged') as $key) {
                unset($changedInputs[$key]);
            }


            $userid = $user->merchant_id;
            $validator = Validator::make($changedInputs, [
                'name' => [
                    'sometimes',
                    'required',
                    Rule::unique('merchant_tags')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    })
                ],
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            if(!empty($changedInputs)){
                MerchantTag::query()->where('id',$request->id)->update($changedInputs);
                return back()->with('success', __('messages.updated_successfully'));
            }else{
                return back();
            }

        }else{
            return back();
        }
    }

    public function invoices_edit(){
        $settings = MerchantsSetting::query()->first();

        return view('theme::marble.settings-invoices-edit', ['title' => 'تعديل اعدادات الفاتورة','settings' => $settings]);
    }
    public function invoices_edit_store(Request $request){
        $validatedData = $request->validate([
            'invoice_notes' => 'required',
        ]);
        // Save content to the database
        $model = MerchantsSetting::query()->first();
        $model->invoice_notes = $validatedData['invoice_notes'];
        $model->save();

        return back()->with('success', __('messages.updated_successfully'));

    }
    public function products_edit(){
        $settings = MerchantsSetting::query()->first();

        return view('theme::marble.settings-products-edit', ['title' => 'تعديل اعدادات الفاتورة','settings' => $settings]);
    }
    public function products_edit_store(Request $request){

    }

    public function taxes_add(){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_add_taxes']){
            return view('theme::marble.taxes-add', ['title' => 'إضافة ضريبة']);
        }else{
            return back();
        }
    }

    public function taxes_edit($id, Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_edit_taxes']){

            $taxes = TaxType::where('id', $id)->first();
            // removing the unneeded info for $changed
            $mtax = array(
                "name" => $taxes->name,
                "percentage" => $taxes->percentage,
            );

            $session = $request->session()->put('mtax',$mtax);
            return view('theme::marble.taxes-edit')->with(
                [
                    'tax' => $taxes,
                    'title' => 'تعديل ضريبة'
                ]
            );
        }else{
            return back();
        }

    }

    public function taxes_add_store(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_add_taxes']){

            $userid = $user->merchant_id;

            $validator = Validator::make($request->all(), [
                'name' => [
                    'required',
                    Rule::unique('tax_types')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'percentage' => ['required','integer']
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            $createTax = TaxType::create([
                "name" => $request->name,
                'percentage' => $request->percentage,
            ]);

            return redirect("/settings/taxes/edit/{$createTax['id']}")->with('success', __('messages.created_successfully'));

        }else{
            return back();
        }
    }

    public function taxes_edit_store(Request $request,$id){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_edit_taxes']){
            $originalInputs = $request->session()->get('mtax');
            $changedInputs = [];
            foreach ($originalInputs as $name => $value) {
                if ($request->input($name) != $value) {
                    $changedInputs[$name] = $request->input($name);
                }else{
                    $changedInputs[$name] = "unchanged";
                }
            }
            //removing unchanged items cause no validation is needed
            foreach (array_keys($changedInputs, 'unchanged') as $key) {
                unset($changedInputs[$key]);
            }

            $user = getCurrentUser();
            $userid = $user->merchant_id;
            $validator = Validator::make($changedInputs, [
                'name' => [
                    'sometimes',
                    'required',
                    Rule::unique('tax_types')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'percentage' => ['sometimes','required','integer']
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            if(!empty($changedInputs)){
                DB::table('tax_types')->where('id',$id)->update($changedInputs);
                return back()->with('success', __('messages.updated_successfully'));
            }else{
                return back();
            }

        }else{
            return back();
        }
    }

    public function currencies_add(){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_add_currencies']){

            return view('theme::marble.currencies-add',['title' => 'اضافة عملة']);
        }else{
            return back();
        }

    }

    public function currencies_edit($id, Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_edit_currencies']){

            $currency = MerchantCurrency::where('id', $id)->first();
            // removing the unneeded info for $changed
            $mcurrency= array(
                "name" => $currency->name,
                "symbol" => $currency?->symbol,
                "default" => $currency->default,
            );

            $session = $request->session()->put('mcurrency',$mcurrency);

            return view('theme::marble.currencies-edit')->with(
                [
                    'currency' => $currency,
                    'user' => $user,
                    'title' => 'تعديل عملة'
                ]
            );
        }else{
            return back();
        }

    }

    public function currencies_add_store(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        // dd($request->all());
        if($main_account || $permissions['settings_add_currencies']){
            $userid = $user->merchant_id;
            $validator = Validator::make($request->all(), [
                'name' => [
                    'required',
                    Rule::unique('merchant_currencies')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'symbol' => ['required'],
                'default' => ['boolean','nullable'],
            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            $createCurrency = null;
            DB::transaction(function () use ($request, $userid,&$createCurrency) {
                // If the new record is set as default, update all other records for the same merchant_id
                if ($request->default) {
                    MerchantCurrency::query()
                        ->where('default', 1)
                        ->update(['default' => 0]);
                }

                // Create the new currency record
                $createCurrency = MerchantCurrency::create([
                    "name" => $request->name,
                    'symbol' => $request->symbol,
                    'default' => $request->default ? 1 : 0,
                ]);
            });

            return redirect("/settings/currencies/edit/{$createCurrency['id']}")->with('success', __('messages.created_successfully'));

        }else{
            return back();
        }
    }

    public function currencies_edit_store(Request $request,$id){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['settings_edit_currencies']){

            $originalInputs = $request->session()->get('mcurrency');
            $changedInputs = [];
            foreach ($originalInputs as $name => $value) {
                if ($request->input($name) != $value) {
                    $changedInputs[$name] = $request->input($name);
                }else{
                    $changedInputs[$name] = "unchanged";
                }
            }

            //removing unchanged items cause no validation is needed
            foreach (array_keys($changedInputs, 'unchanged') as $key) {
                unset($changedInputs[$key]);
            }
            $user = getCurrentUser();
            $userid = $user->merchant_id;
            $validator = Validator::make($changedInputs, [
                'name' => [
                    'sometimes',
                    'required',
                    Rule::unique('merchant_currencies')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'symbol' => ['sometimes','required'],
                'default' => ['sometimes','boolean','nullable'],
            ]);


            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }

            if(!empty($changedInputs)){
                DB::transaction(function () use ($request, $userid, $id, $changedInputs) {
                    // If the new record is set as default, update all other records for the same merchant_id
                    if ($request->default) {
                        MerchantCurrency::query()
                            ->where('default', 1)
                            ->update(['default' => 0]);
                    }

                    if(!isset($request['default']))
                    {
                        $changedInputs['default'] = 0;
                    }else{
                        $changedInputs['default'] = 1;

                    }

                    MerchantCurrency::query()->where('id',$id)->update($changedInputs);

                });

                return back()->with('success', __('messages.updated_successfully'));
            }else{
                return back();
            }

        }else{
            return back();
        }

    }
    public function branches_show(){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['branches_registers_show_add_edit']){
            $branch = MerchantBranch::query()->first();
            if ($branch) {
                return redirect()->route('branches.settings', $branch->id);
            }
        }else{
            return back();
        }

    }

    public function branches_store(Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['branches_registers_show_add_edit']){
            $userid = $user->merchant_id;


            if($user->main_account){
                $details = $user->details;
            }else{
                $details = $user->mainAccount->details;
            }

            $currentBranches = MerchantBranch::query()->count();
            $currentRegisters = MerchantRegistery::query()->count();

            if($currentBranches >= $details->branch_numbers){
                return back()->withErrors('لايوجد لديك اشتراك فروع اضافية, الرجاء الاشتراك بفرع جديد ثم الإضافة من هنا');
            }

            $availableRegistersToAdd =  $details->register_numbers - $currentRegisters;

            if( count($request['repeater']) > $availableRegistersToAdd){
                return back()->withErrors('لايوجد لديك اشتراك صناديق اضافية, الرجاء الاشتراك بصندوق جديد ثم الإضافة من هنا');
            }

            $validator = Validator::make($request->all(), [
                'branch_name' => [
                    'required',
                    Rule::unique('merchant_branches')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'currency_id' => [
                    'required',
                    Rule::exists('merchant_currencies', 'id')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'street' => ['required', 'string', 'max:255'],
                'building_number' => ['required', 'string'],
                'postal_code' => ['required', 'string', 'max:255'],
                'state_id' => ['required', 'exists:states,id'],
                'city_id' => ['required', 'exists:cities,id'],
                'district' => ['required', 'string', 'max:255'],
                'repeater' => 'array',
                'repeater.*.register_name' => [
                    'required',
                    // to ensure the names of registers
                    function ($attribute, $value, $fail) use ($request) {
                        $names = collect($request->input('repeater'))->pluck('register_name');
                        $count = $names->count();
                        $uniqueCount = $names->unique()->count();

                        if ($count !== $uniqueCount) {
                            $fail(" الرجاء عدم تكرار اسم الصندوق ");
                        }
                    },
                ]


            ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }else{

            $createdBranchId = null;
            DB::transaction(function() use ($request,&$createdBranchId){
                $createBranch = MerchantBranch::create([
                    "branch_name" => $request->branch_name,
                    'currency_id' => $request->currency_id,
                ]);
                $createBranchId = $createBranch->id;
                $createdBranchId = $createBranchId;

                // Create address for the branch if address fields are provided
                if ($request->filled('street') || $request->filled('building_number') ||
                    $request->filled('postal_code') || $request->filled('state_id') ||
                    $request->filled('city_id') || $request->filled('district')) {

                    $createBranch->address()->create([
                        'street' => $request->street,
                        'building_number' => $request->building_number,
                        'postal_code' => $request->postal_code,
                        'state_id' => $request->state_id,
                        'city_id' => $request->city_id,
                        'district' => $request->district,
                        'merchant_id' => BusinessContextService::getMerchantId(),
                        'business_id' => BusinessContextService::getBusinessId(),
                    ]);
                }

                $paymentMethods = MerchantPaymentMethod::query()
                    ->where('name', '!=' , 'دفع بالآجل')
                    ->get('id');

                foreach($request->repeater as $register){

                    $createRegistry = MerchantRegistery::create([
                        "name" => $register['register_name'],
                        "branch_id" => $createBranchId,
                        'uuid' => Str::uuid(),
                    ]);

                    MerchantRegisterSetting::create([
                        'register_id' =>  $createRegistry->id,
                        'screen' => 'sale_default',
                        'open_close_register' => 0,
                    ]);

                    foreach ($paymentMethods as $paymentMethod) {
                        MerchantPaymentMethodRegister::create([
                            'register_id' => $createRegistry->id,
                            'payment_id' => $paymentMethod->id,
                        ]);
                    }
                }

                $products = MerchantsProduct::query()->get();
                foreach($products as $product){
                    MerchantProductBranchQuantity::create([
                        'product_id' => $product['id'],
                        'sell_price' => $product['sell_price'],
                        'wholesale_price' => $product['wholesale_price'],
                        'sell_price_tax_exclusive' => $product['sell_price_tax_exclusive'],
                        'wholesale_price_tax_exclusive' => $product['wholesale_price_tax_exclusive'],
                        'sell_price_tax_exclusive_rounded' => $product['sell_price_tax_exclusive_rounded'],
                        'wholesale_price_tax_exclusive_rounded' => $product['wholesale_price_tax_exclusive_rounded'],
                        'branch_id' => $createBranchId,
                        'quantity' => 0,
                    ]);
                }

                // Create branch quantities for variant products
                $variantProducts = MerchantProductVariant::query()->get();
                foreach($variantProducts as $variantProduct){
                    // Get pricing from existing branch quantity or fallback to parent product
                    $existingBranchQuantity = MerchantProductBranchQuantity::where('product_variant_id', $variantProduct->id)
                        ->first();

                    $sellPrice = $existingBranchQuantity?->sell_price ?? $variantProduct->product->sell_price ?? '0';
                    $wholesalePrice = $existingBranchQuantity?->wholesale_price ?? $variantProduct->product->wholesale_price ?? '0';
                    $sellPriceTaxExclusive = $existingBranchQuantity?->sell_price_tax_exclusive ?? $variantProduct->product->sell_price_tax_exclusive ?? '0';
                    $wholesalePriceTaxExclusive = $existingBranchQuantity?->wholesale_price_tax_exclusive ?? $variantProduct->product->wholesale_price_tax_exclusive ?? '0';
                    $sellPriceTaxExclusiveRounded = $existingBranchQuantity?->sell_price_tax_exclusive_rounded ?? $variantProduct->product->sell_price_tax_exclusive_rounded ?? '0';
                    $wholesalePriceTaxExclusiveRounded = $existingBranchQuantity?->wholesale_price_tax_exclusive_rounded ?? $variantProduct->product->wholesale_price_tax_exclusive_rounded ?? '0';

                    MerchantProductBranchQuantity::create([
                        'product_id' => $variantProduct->product_id,
                        'product_variant_id' => $variantProduct->id,
                        'sell_price' => $sellPrice,
                        'wholesale_price' => $wholesalePrice,
                        'sell_price_tax_exclusive' => $sellPriceTaxExclusive,
                        'wholesale_price_tax_exclusive' => $wholesalePriceTaxExclusive,
                        'sell_price_tax_exclusive_rounded' => $sellPriceTaxExclusiveRounded,
                        'wholesale_price_tax_exclusive_rounded' => $wholesalePriceTaxExclusiveRounded,
                        'branch_id' => $createBranchId,
                        'quantity' => 0,
                    ]);
                }
            });
                return redirect()->route('branches.settings', $createdBranchId)->with('success', __('messages.created_successfully'));
            }
        }else{
            return back();
        }
    }

    public function branches_show_registers($id){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['branches_registers_show_add_edit']){
            $registers = MerchantRegistery::where('branch_id', $id)->get();

            return view('theme::marble.registers-show',['registers' => $registers, 'title' => 'الصناديق']);
        }else{
            return back();
        }

    }

    public function branches_settings($branchId) {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['branches_registers_show_add_edit']) {
            // Get current register

            // Get current branch
            //TODO check this with findorfail or change it
            $currentBranch = MerchantBranch::findOrFail($branchId);

            // Get all branches with their registers
            $branches =  MerchantBranch::query()
                ->with(['registers' => function($query) {
                    $query->orderBy('name');
                }])
                ->get();

            $currencies = MerchantCurrency::query()->get();

            return view('theme::marble.branches-settings', [
                'title' => 'اعدادات الفرع',
                'branches' => $branches,
                'currentBranch' => $currentBranch,
                'currencies' => $currencies,
            ]);
        }

        return back();
    }
    public function registers_settings($branchId, $registerId) {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        $currencies = MerchantCurrency::query()->get();


        if($main_account || $permissions['branches_registers_show_add_edit']) {
            // Get current register
            //TODO check this with findorfail or change it
            $currentRegister = MerchantRegistery::where('id', $registerId)->firstOrFail();

            // Get current branch
            $currentBranch = MerchantBranch::findOrFail($branchId);

            // Get all branches with their registers
            $branches =  MerchantBranch::query()
                ->with(['registers' => function($query) {
                    $query->orderBy('name');
                }])
                ->get();

            $paymentMethods = MerchantPaymentMethod::query()->get();

            $permittedPaymentMethods = MerchantPaymentMethodRegister::where(
                'register_id', $currentRegister->id
            )->get();

            try {
                $merchantRegister = MerchantRegisterSetting::where('register_id', $registerId)->firstOrFail();
            } catch(Exception $e) {
                MerchantRegisterSetting::create([
                    'register_id' => $currentRegister->id,
                    'screen' => 'sale_default'
                ]);

                $merchantRegister = MerchantRegisterSetting::where(
                    'register_id', $registerId
                )->first();
            }

            return view('theme::marble.registers-settings', [
                'title' => 'اعدادات الصندوق',
                'merchantRegister' => $merchantRegister,
                'paymentMethods' => $paymentMethods,
                'permittedPaymentMethods' => $permittedPaymentMethods,
                'branches' => $branches,
                'currentBranch' => $currentBranch,
                'currentRegister' => $currentRegister,
                'currencies' => $currencies,
            ]);
        }

        return back();
    }
    public function registers_settings_store($branchId,$registerId, Request $request){
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['branches_registers_show_add_edit']){
                if($request['prmittedPaymentMethods']){
                    $request['prmittedPaymentMethods'] = array_column(json_decode($request['prmittedPaymentMethods']),'value','code');
                }
                $merchantRegister = MerchantRegisterSetting::where('register_id', $registerId)->first();
                $validator = Validator::make($request->all(), [
                    'layout' => [
                        'sometimes',
                        'required',
                        'in:sale_default,sale_images'
                    ],
                    'open_close_register' => ['boolean'],
                    'max_open_hours' => ['integer', 'min:1', 'max:72'],
                ]);

                if ($validator->fails()) {
                    return back()
                                ->withErrors($validator)
                                ->withInput();
                }

                $open_close_register = $request->open_close_register?1:0;
                $merchantRegister->screen = $request->layout;
                $merchantRegister->open_close_register = $open_close_register;
                $merchantRegister->max_open_hours = $request->max_open_hours;
                $merchantRegister->save();

                MerchantPaymentMethodRegister::where(['register_id' => $registerId])->delete();

                if($request['prmittedPaymentMethods']){
                    foreach($request['prmittedPaymentMethods'] as $id => $payment){
                        if(MerchantPaymentMethod::where('id',$id)->exists()){
                            MerchantPaymentMethodRegister::create(['payment_id' => $id, 'register_id' => $registerId]);
                        }
                    }
                }
                return back()->with('success', __('messages.updated_successfully'));

        }else{
            return back();
        }
    }

    final public function updateWeightedProductSettings(WeightedProductSettingRequest $request): RedirectResponse
    {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;
        if($main_account || $permissions['change_weighted_product_setting']){
            $dto = WeightedProductSettingDto::from($request->validated());

            WeightedProductSetting::query()
                ->update($dto->toArray());

            return back()->with('success', __('messages.updated_successfully'));
        }else{
            return back();
        }
    }

    public function branches_settings_store(Request $request, $branchId)
    {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['branches_registers_show_add_edit']) {
            // Validate the request
            $validated = $request->validate([
                'branch_name' => [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique('merchant_branches')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    })->ignore($branchId), // Ignore current branch when checking uniqueness
                ],
                'currency_id' => 'required|exists:merchant_currencies,id',
                'street' => ['required', 'string', 'max:255'],
                'building_number' => ['required', 'string'],
                'postal_code' => ['required', 'string', 'max:255'],
                'state_id' => ['required', 'exists:states,id'],
                'city_id' => ['required', 'exists:cities,id'],
                'district' => ['required', 'string', 'max:255'],
            ]);

            try {
                // Find the branch and ensure it belongs to the merchant
                $branch = MerchantBranch::query()->findOrFail($branchId);

                // Update branch details
                $branch->update([
                    'branch_name' => $validated['branch_name'],
                    'currency_id' => $validated['currency_id'],
                ]);

                // Update or create address
                $addressData = [
                    'street' => $validated['street'] ?? null,
                    'building_number' => $validated['building_number'] ?? null,
                    'postal_code' => $validated['postal_code'] ?? null,
                    'state_id' => $validated['state_id'] ?? null,
                    'city_id' => $validated['city_id'] ?? null,
                    'district' => $validated['district'] ?? null,
                    'merchant_id' => BusinessContextService::getMerchantId(),
                    'business_id' => BusinessContextService::getBusinessId(),
                ];

                // Only update if at least one address field is provided
                if (array_filter($addressData, function($value) { return !is_null($value); })) {
                    $branch->address()->updateOrCreate(
                        [
                            'merchant_id' => BusinessContextService::getMerchantId(),
                            'business_id' => BusinessContextService::getBusinessId(),
                        ],
                        $addressData
                    );
                }

                return redirect()
                    ->back()
                    ->with('success', __('messages.updated_successfully'));
            } catch (\Exception $e) {
                return redirect()
                    ->back()
                    ->withErrors(['error' => __('messages.error_updating_branch')]);
            }
        }

        return back()->withErrors(['error' => __('messages.unauthorized_action')]);
    }
    public function registers_store(Request $request)
    {
        $user = getCurrentUser();
        $main_account = $user->main_account;
        $permissions = $user->group->permissions;

        if($main_account || $permissions['branches_registers_show_add_edit']) {
            $userid = $user->merchant_id;

            // Check subscription limits
            if($user->main_account) {
                $details = $user->details;
            } else {
                $details = $user->mainAccount->details;
            }

            // Check if user has available registers in their subscription
            $currentRegisters = MerchantRegistery::query()->count();

            if($currentRegisters >= $details->register_numbers) {
                return back()->withErrors('لايوجد لديك اشتراك صناديق اضافية, الرجاء الاشتراك بصندوق جديد ثم الإضافة من هنا');
            }

            // Validate the request
            $validator = Validator::make($request->all(), [
                'branch_id' => [
                    'required',
                    Rule::exists('merchant_branches', 'id')->where(function ($query) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->whereNull('deleted_at');
                    }),
                ],
                'register_name' => [
                    'required',
                    Rule::unique('merchant_registeries', 'name')->where(function ($query) use($request) {
                        return $query->where('merchant_id', BusinessContextService::getMerchantId())
                            ->where('business_id', BusinessContextService::getBusinessId())
                            ->where('branch_id', $request->branch_id)
                            ->whereNull('deleted_at');
                    }),
                ],
            ]);

            if ($validator->fails()) {
                return back()
                    ->withErrors($validator)
                    ->withInput();
            }

            try {
                // Declare the variable outside
                $createRegistry = null;
                DB::transaction(function() use ($request, $userid, $user, &$createRegistry) {
                    // Create the register
                    $createRegistry = MerchantRegistery::create([
                        "name" => $request->register_name,
                        "branch_id" => $request->branch_id,
                        'uuid' => Str::uuid(),
                    ]);

                    // Create register settings
                    MerchantRegisterSetting::create([
                        'register_id' => $createRegistry->id,
                        'screen' => 'sale_default',
                        'open_close_register' => 0,
                    ]);

                    // Add payment methods to register
                    $paymentMethods = MerchantPaymentMethod::query()
                        ->where('name', '!=' , 'دفع بالآجل')
                        ->get('id');

                    foreach ($paymentMethods as $paymentMethod) {
                        MerchantPaymentMethodRegister::create([
                            'register_id' => $createRegistry->id,
                            'payment_id' => $paymentMethod->id,
                        ]);
                    }
                });

                return redirect()
                    ->route('branches.registers_settings', ['branchId' => $request->branch_id, 'registerId' => $createRegistry->id])
                    ->with('success', __('messages.created_successfully'));

            } catch (\Exception $e) {
                return redirect()
                    ->back()
                    ->withErrors(['error' => __('messages.error_creating_register')]);
            }
        }

        return back()->withErrors(['error' => __('messages.unauthorized_action')]);
    }

}
