<?php

namespace App\Http\Controllers\Application\Web;

use App\Actions\Application\Web\Invoices\PrintInvoiceAction;
use App\Enums\Application\InvoiceTypeEnum;
use App\Enums\Application\Zatca\ZatcaInvoiceName;
use App\Exports\InvoiceExport;
use App\Models\MerchantBranch;
use App\Models\MerchantInvoice;
use App\Models\MerchantPaymentMethod;
use App\Models\MerchantSaleInvoiceDiscount;
use App\Models\MerchantSaleInvoicePaymentMethod;
use App\Models\MerchantSaleInvoiceProduct;
use App\Models\MerchantsProduct;
use App\Models\MerchantsSetting;
use App\Services\CurrencyService;
use App\Services\InvoiceReturnService;
use BCMathExtended\BC;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\View;
use Maatwebsite\Excel\Facades\Excel;
use Milon\Barcode\DNS1D;

class InvoicesController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:merchants');
        $this->middleware('throttle:60,1');
    }

    public function index(){
    $user = Auth::user();
    $permissions = $user->group->permissions;
    if($permissions['invoices_show'] || $user->main_account){

        return View("theme::marble.invoices")->with(['title' => 'الفواتير']);
    }else{
        return back();
    }

}

    public function display($id){
    $user = Auth::user();
    $permissions = $user->group->permissions;
    if($permissions['invoices_show'] || $user->main_account){

        $invoice = MerchantInvoice::where('id', $id)->first();
        if(!is_null($invoice['is_discounted']) && $invoice['is_discounted']){
            $discount = MerchantSaleInvoiceDiscount::where('invoice_id',$invoice['id'])->first();
            $invoice['total_discount_tax_exclusive_rounded'] = $discount['total_discount_tax_exclusive_rounded'];
            $invoice['total_discount_tax_rounded'] = $discount['total_discount_tax_rounded'];
        }

        $invoicePaymentMethods = MerchantSaleInvoicePaymentMethod::where('invoice_id', $invoice->id)->get();
        $paymentMethods = [];
        foreach($invoicePaymentMethods as $i => $paymentMethod){
            $payment = MerchantPaymentMethod::where('id', $paymentMethod['payment_id'])->first();
            $paymentMethods[$i]['id'] = $paymentMethod['payment_id'];
            $paymentMethods[$i]['name'] = $payment['name'];
        }

        $branchName = MerchantBranch::where('id',$invoice->branch_id)->first()->branch_name;

        $products = MerchantSaleInvoiceProduct::where('invoice_id', $invoice->id)->get();

        $productsWithMoreInformation = $products->map(function ($item) {
            $product = MerchantsProduct::withTrashed()->findOrFail($item->product_id);
            $item->product_name = $item->product_name_ar;
            $item->product_thumbnail = $product->thumbnail;
            $item->product_barcode = $item->barcode;

            return $item;
        });

        // Initialize payment variables
        $totalPostPay = 0;
        $totalReceived = 0;
        $total = 0;
        $receivePayments = [];

        // Process payment methods if this invoice has postpay
        if(isset($invoice->has_postpay) && $invoice->has_postpay) {
            // Get all payment methods for this invoice
            $allPaymentMethods = MerchantSaleInvoicePaymentMethod::where('invoice_id', $invoice->id)->get();
            $total = $invoice->total_tax_inclusive;
            // Process each payment method
            foreach($allPaymentMethods as $i => $paymentMethod) {
                $payment = MerchantPaymentMethod::where('id', $paymentMethod['payment_id'])->first();

                if($paymentMethod['is_postpay']) {
                    $totalPostPay = BC::add($totalPostPay, $paymentMethod['amount'], 10);
                } else {
                    if($paymentMethod['invoice_type'] === InvoiceTypeEnum::RECEIVE->value) {
                        $totalReceived = BC::add($totalReceived, $paymentMethod['amount'], 10);

                        // Add to receive payments for display in table
                        $receivedPayments[] = [
                            'id' => $paymentMethod['id'],
                            'payment_name' => $payment['name'],
                            'amount' => $paymentMethod['amount'],
                            'created_at' => $paymentMethod['created_at'],
                        ];
                    }
                }
            }
        }
        return View("theme::marble.invoices-details")->with([
            'invoice'=> $invoice,
            'products' => $productsWithMoreInformation,
            'paymentMethods' => $paymentMethods,
            'branchName' => $branchName,
            'totalPostPay' => $totalPostPay,
            'totalReceived' => $totalReceived,
            'total' => $total,
            'receivedPayments' => $receivedPayments ?? '',
            'title' => 'مشاهدة فاتورة'
        ]);
    }else{
        back();
    }

}

    public function return($id, Request $request){
    $user = Auth::user();
    $permissions = $user->group->permissions;
    $branchIds = $user->branches->pluck('branch_id')->toArray();
    if($permissions['invoices_return'] || $user->main_account){

        $backto = $request['backto'];
        $invoice = MerchantInvoice::where('id', $id)->first();
        if($user->main_account){
            $branches = MerchantBranch::query()->get();

        }else{
            $branches = MerchantBranch::query()->whereIn('id',$branchIds)->get();
        }
        if(!is_null($invoice['is_discounted']) && $invoice['is_discounted']){
            $discount = MerchantSaleInvoiceDiscount::where('invoice_id',$invoice['id'])->first();
            $invoice['total_discount_tax_exclusive_rounded'] = $discount['total_discount_tax_exclusive_rounded'];
            $invoice['total_discount_tax_rounded'] = $discount['total_discount_tax_rounded'];
        }

        $paymentMethods = MerchantPaymentMethod::query()->get();
        $paymentInvoiceMethods = MerchantSaleInvoicePaymentMethod::where('invoice_id', $id)->get();
        $postpaidAmount = 0;
        $receivedPostPaidAmount = 0;

        if($invoice['has_postpay'] == 1){
            foreach($paymentInvoiceMethods as $i => $payment){
                if($payment['is_postpay']){
                    $postpaidAmount = BC::add($postpaidAmount,$payment['amount'],10);
                }
                if($payment['invoice_type'] === InvoiceTypeEnum::RECEIVE->value){
                    $receivedPostPaidAmount = BC::add($receivedPostPaidAmount,$payment['amount'],10);
                }
            }
        }
        $remainingAmount = BC::abs(BC::add( $postpaidAmount,$receivedPostPaidAmount,10));
        $products = MerchantSaleInvoiceProduct::where('invoice_id', $invoice->id)
        ->orWhere('related_invoice', $invoice->id)
        ->get();
        $sameProducts = [];
        foreach($products as $product){
            // Construct cart_id based on product properties since it's not stored in DB:
            $productKey = $product->product_id; // Start with product_id

            // Add variant_id if it's a variant product
            if (isset($product->is_variant) && $product->is_variant && $product->product_variant_id) {
                $productKey .= '-' . $product->product_variant_id;
            }

            // Add promotion_id if it has promotion
            if (isset($product->has_promotion) && $product->has_promotion && $product->promotion_id) {
                $productKey .= '-' . $product->promotion_id;
            }

            if(array_key_exists($productKey, $sameProducts)){
                if($product->invoice_type === InvoiceTypeEnum::RETURN->value){
                    $sameProducts[$productKey] = BC::sub($sameProducts[$productKey], $product->cart_quantity, 10);
                }else{
                    if($product->returnable){
                        $sameProducts[$productKey] = BC::add($sameProducts[$productKey], $product->cart_quantity, 10);
                    }
                }

            }else{
                if($product->invoice_type === InvoiceTypeEnum::RETURN->value){
                    $sameProducts[$productKey] = BC::sub(0, $product->cart_quantity, 10);
                }else{
                    if($product->returnable){
                        $sameProducts[$productKey] = $product->cart_quantity;
                    }
                }
            }
        }
        $totalQuantity = array_sum($sameProducts);

        // Group products by promotion groups for better UI display
        // For buy_x_get_y promotions, group by promotion_id to handle cases where
        // buy_x and get_y products have different promotion_group_id values
        $promotionGroups = [];
        $regularProducts = [];


        foreach ($products as $product) {
            if ($product->promotion_group_id && $product->invoice_type !== InvoiceTypeEnum::RETURN->value) {
                // Use promotion_group_id directly since they should be the same now
                $groupKey = $product->promotion_group_id;

                // This product belongs to a promotion group
                if (!isset($promotionGroups[$groupKey])) {
                    $promotionGroups[$groupKey] = [
                        'group_id' => $groupKey,
                        'original_group_id' => $product->promotion_group_id,
                        'promotion_id' => $product->promotion_id,
                        'products' => [],
                        'buy_x_products' => [],
                        'get_y_products' => [],
                        'group_info' => null,
                        'returnable' => true
                    ];
                }

                // Add remaining quantity to the product
                $productKey = $product->product_id;
                if (isset($product->is_variant) && $product->is_variant && $product->product_variant_id) {
                    $productKey .= '-' . $product->product_variant_id;
                }
                if (isset($product->has_promotion) && $product->has_promotion && $product->promotion_id) {
                    $productKey .= '-' . $product->promotion_id;
                }
                $product->remaining_qty = $sameProducts[$productKey] ?? 0;

                $promotionGroups[$groupKey]['products'][] = $product;

                if ($product->promotion_product_type === 'buy_x') {
                    $promotionGroups[$groupKey]['buy_x_products'][] = $product;
                } elseif ($product->promotion_product_type === 'get_y') {
                    $promotionGroups[$groupKey]['get_y_products'][] = $product;
                }

                // If any product in the group is not returnable, mark the whole group as not returnable
                if (!$product->returnable) {
                    $promotionGroups[$groupKey]['returnable'] = false;
                }
            } else {
                // Regular product (not in promotion group) or return product
                $regularProducts[] = $product;
            }
        }

        // Get promotion group details
        $promotionGroupDetails = [];
        if (!empty($promotionGroups)) {
            $invoiceReturnService = new InvoiceReturnService();
            $promotionGroupDetails = $invoiceReturnService->getPromotionGroupsSummary($id);

            // Map group details to our promotion groups
            // Since we're grouping by promotion_id, we need to find the matching group details
            foreach ($promotionGroupDetails as $groupDetail) {
                foreach ($promotionGroups as $groupKey => &$group) {
                    // Check if this group detail matches any of the original group IDs in this promotion group
                    $matchFound = false;
                    foreach ($group['products'] as $product) {
                        if ($product->promotion_group_id === $groupDetail['group_id']) {
                            $group['group_info'] = $groupDetail;
                            $matchFound = true;
                            break;
                        }
                    }
                    if ($matchFound) break;
                }
            }
        }

        $productsWithMoreInformation = $products->map(function ($item) {
            $product = MerchantsProduct::withTrashed()->findOrFail($item->product_id);
            $item->product_name = $item->product_name_ar;
            $item->product_thumbnail = $product->thumbnail;
            $item->product_barcode = $item->barcode;

            return $item;
        });

        return View("theme::marble.invoices-return")->with([
            'invoice'=> $invoice,
            'remainingAmount' => $remainingAmount,
            'products' => $productsWithMoreInformation,
            'backto' => $backto,
            'sameProducts' => $sameProducts,
            'paymentMethods' => $paymentMethods,
            'branches' => $branches,
            'totalQuantity' => $totalQuantity,
            'promotionGroups' => $promotionGroups,
            'regularProducts' => $regularProducts,
            'hasPromotionGroups' => !empty($promotionGroups),
            'title' => 'استرجاع فاتورة'
        ]);
    }else{
        return back();
    }

}

    public function return_store($id, Request $request)
    {
        $user = getCurrentUser();

        // Use the InvoiceReturnService to process the return
        // We can pass the request directly since the service now accepts either a Request or an array
        $invoiceReturnService = new InvoiceReturnService();
        $result = $invoiceReturnService->processReturn($id, $request, $user);

        // Handle errors
        if (isset($result['error'])) {
            return back()->withErrors($result['error']);
        }

        // Handle redirects
        if (isset($result['redirect'])) {
            return redirect($result['redirect'])->with('returnedInvoiceId', $result['returnedInvoiceId'] ?? null);
        }

        // Fallback
        return back();
    }

    public function receive($id){

        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['invoices_receive'] || $user->main_account){

            $invoice = MerchantInvoice::where( 'id', $id)->first();
            if($invoice['has_postpay'] == 0){
                abort(404);
            }

            $invoicePaymentMethods = MerchantSaleInvoicePaymentMethod::where('invoice_id', $invoice->id)->get();

            $paymentMethods = [];

            $totalPostPay = 0;
            $totalReceived = 0;
            $total = 0;

            foreach($invoicePaymentMethods as $i => $paymentMethod){
                $payment = MerchantPaymentMethod::where('id', $paymentMethod['payment_id'])->first();
                $paymentMethods[$i]['id'] = $paymentMethod['payment_id'];
                $paymentMethods[$i]['name'] = $payment['name'];
                $paymentMethods[$i]['amount'] = $paymentMethod['amount'];


                if($paymentMethod['is_postpay']){
                    $totalPostPay = BC::add($totalPostPay,$paymentMethod['amount'],10);
                }else{
                    if($paymentMethod['invoice_type'] === InvoiceTypeEnum::RECEIVE->value){
                        $totalReceived = BC::add($totalReceived,$paymentMethod['amount'],10);
                    }else{
                        $total = BC::add($total,$paymentMethod['amount'],10);
                    }
                }
            }

            $remainingPostPay = BC::add($totalPostPay,$totalReceived,10);

            $branchName = MerchantBranch::where('id',$invoice->branch_id)->first()->branch_name;
            $payments = MerchantPaymentMethod::query()->get();

            return View('theme::marble.invoices-receive')->with(['invoice'=> $invoice,'remainingPostPay' => $remainingPostPay,'paymentMethods' => $paymentMethods,'totalPostPay' => $totalPostPay,'total' => $total,'totalReceived' => $totalReceived,'payments' => $payments,'branchName' => $branchName ,'title' => 'مشاهدة فاتورة']);
        }else{
            return back();
        }
    }

    public function receive_store($id,Request $requests){
        $returnedItems = [];
        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['invoices_receive'] || $user->main_account){


        $postPaymentMethod = MerchantPaymentMethod::where(['merchant_payment_methods.merchant_id' => $user->merchant_id,'merchant_payment_methods.is_postpay' => 1])
            ->crossJoin('merchant_sale_invoice_payment_methods','merchant_payment_methods.id','=','merchant_sale_invoice_payment_methods.payment_id')
            ->where('invoice_id' , $id)
            ->get();
        $totalPaid = MerchantSaleInvoicePaymentMethod::where(['invoice_id' =>$id,'invoice_type' => 'receive'])->sum('amount');

        $validator = Validator::make($requests->all(), [
            'amount' => [
                'required','numeric','min:0','max:'.abs(BC::add($postPaymentMethod[0]['amount'],$totalPaid,10)),
            ],
            'payment' => 'required',

        ]);

            if ($validator->fails()) {
                return back()
                            ->withErrors($validator)
                            ->withInput();
            }else{
                $addPaymentMethodReceipt = MerchantSaleInvoicePaymentMethod::create([
                    'invoice_id' => $id,
                    'payment_id' => $requests['payment'],
                    'invoice_type' => 'receive',
                    'amount' => $requests['amount'],
                    'is_postpay' => 0,

                ]);
                return redirect("/invoices/receive/{$id}")->with(['success' => __('messages.processed_successfully'),'payment_id' => $addPaymentMethodReceipt->id]);

            }

            $invoice = MerchantInvoice::where('id', $id)->first();
            $returnInvoice = MerchantInvoice::where('related_invoice', $id)->get();

        }else{
            return back();
        }

    }

    final public function print(int $id): View
    {
        $data = app(PrintInvoiceAction::class)($id, getCurrentUser());

        $invoice = data_get($data, 'invoice');
        $merchantUser = data_get($data, 'merchantUser');

        $defaultCurrency = app(CurrencyService::class)->getDefaultCurrency(getCurrentUser()->merchant_id);

        // Generate barcode for invoice pretty_id
        $barcode = new DNS1D();
        $invoiceBarcodeImage = $barcode->getBarcodePNG($invoice->pretty_id, 'C128');

        $view = $invoice?->zatca_invoice_name === ZatcaInvoiceName::SIMPLIFIED->value
            ? "theme::marble.simple-invoice-ar"
            : "theme::marble.a4-invoice-ar";

        return view($view)->with([
            'invoice' => $invoice,
            'address' => $merchantUser?->address?->district . ' - ' . $merchantUser?->address?->street,
            'products' => $invoice?->salesProducts,
            'customer' => $invoice?->customer,
            'customer_address' => $invoice?->customer?->address?->district . ' - ' . $invoice?->customer?->address?->street,
            'qrcode' => $invoice?->qr_zatca_image,
            'invoiceBarcodeImage' => $invoiceBarcodeImage,
            'settings' => $merchantUser?->setting,
            'paidAmount' => data_get($data, 'paidAmount'),
            'postPayAmount' => data_get($data, 'postPayAmount'),
            'defaultCurrency' => $defaultCurrency->symbol,
            'title' => 'طباعة فاتورة'
        ]);
    }

    final public function printa4(int $id): View
    {
        $data = app(PrintInvoiceAction::class)($id, getCurrentUser());

        $invoice = data_get($data, 'invoice');
        $merchantUser = data_get($data, 'merchantUser');
        $currencyService = new CurrencyService();
        $defaultCurrency = $currencyService->getDefaultCurrency(getCurrentUser()->merchant_id);

        // Generate barcode for invoice pretty_id
        $barcode = new DNS1D();
        $invoiceBarcodeImage = $barcode->getBarcodePNG($invoice->pretty_id, 'C128');

        return view("theme::marble.a4-invoice-ar")->with([
            'invoice' => $invoice,
            'address' => $merchantUser?->address?->district . ' - ' . $merchantUser?->address?->street,
            'products' => $invoice?->salesProducts,
            'customer' => $invoice?->customer,
            'customer_address' => $invoice?->customer?->address?->district . ' - ' . $invoice?->customer?->address?->street,
            'qrcode' => $invoice?->qr_zatca_image,
            'invoiceBarcodeImage' => $invoiceBarcodeImage,
            'settings' => $merchantUser?->setting,
            'defaultCurrency' => $defaultCurrency->symbol,
            'paidAmount' => data_get($data, 'paidAmount'),
            'postPayAmount' => data_get($data, 'postPayAmount'),
            'title' => 'طباعة فاتورة'
        ]);
    }

    final public function receive_print(int $paymentId): View
    {
        $user = getCurrentUser();
        $settings = MerchantsSetting::query()->first();
        // Get the payment record with its related invoice
        $payment = MerchantSaleInvoicePaymentMethod::where('invoice_type', 'receive')->with('invoice')->findOrFail($paymentId);
        // Make sure the invoice exists
        if (!$payment->invoice) {
            abort(404, 'Invoice not found for this payment');
        }

        $invoice = $payment->invoice;


        $currencyService = new CurrencyService();
        $defaultCurrency = $currencyService->getDefaultCurrency($user->merchant_id);

        // Get all payments for this invoice
        $allPayments = MerchantSaleInvoicePaymentMethod::where('invoice_id', $invoice->id)->get();

        // Get total paid amount for this invoice
        $totalPaidAmount = $allPayments->where('invoice_type','receive')->sum('amount');

        // Get previous payments (excluding current payment)
        $previousPaidAmount = abs($allPayments
            ->where('id', '!=', $paymentId)
            ->where('invoice_type','receive')
            ->sum('amount'));

        // Current payment amount
        $currentPaymentAmount = $payment->amount;

        // Remaining balance
        $remainingBalance = $invoice->total_tax_inclusive - $totalPaidAmount;

        return view("theme::marble.payment-receipt-ar")->with([
            'invoice' => $invoice,
            'payment' => $payment,
            'address' => $user?->address?->district . ' - ' . $user?->address?->street,
            'customer' => $invoice->customer,
            'customer_address' => $invoice->customer?->address?->district . ' - ' . $invoice->customer?->address?->street,
            'settings' => $settings,
            'totalPaidAmount' => $totalPaidAmount,
            'previousPaidAmount' => $previousPaidAmount,
            'currentPaymentAmount' => $currentPaymentAmount,
            'remainingBalance' => $remainingBalance,
            'defaultCurrency' => $defaultCurrency->symbol,
            'title' => 'إيصال دفع'
        ]);
    }

    public function export(Request $request){
        $user = Auth::user();
        $permissions = $user->group->permissions;
        if($permissions['invoices_export'] || $user->main_account){

            $date_start = $request['date_start'];
            $date_end = $request['date_end'];
            $search = $request['search'];
            $status = $request['status'];
            $merchantId = Auth::user()->merchant_id;
            return Excel::download(new InvoiceExport($merchantId,$date_start,$date_end,$search,$status), 'invoices.xlsx');
        }else{
            return back();
        }
    }
}
