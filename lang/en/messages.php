<?php

use App\Enums\Application\Zatca\ZatcaZeroVatCategory;

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | تُستخدم الأسطر اللغوية التالية أثناء المصادقة على مستوى المستخدم لرسائل مختلفة نحتاج إلى عرضها للمستخدم. يمكنك تعديل هذه الأسطر اللغوية حسب متطلبات التطبيق الخاص بك.
    |
    */

    'direction' => 'ltr',
    'style.bundle' => '/css/style.bundle.css',
    'plugins.global' => '/plugins/global/plugins.bundle.css',
    'plugins.datatables' => '/plugins/custom/datatables/datatables.bundle.css',
    'plugins.fullcalender' => '/plugins/custom/fullcalendar/fullcalendar.bundle.css',
    'lang' => 'en',


    'failed' => 'These credentials do not match our records.',
    'password' => 'The entered password is incorrect.',
    'throttle' => 'Too many login attempts. Please try again in :seconds seconds.',
    'dashboard' => 'Dashboard',
    'total_revenue' => 'Total Revenue for Today',
    'total_income' => 'Total Income for Today',
    'net_income' => 'Net Income for Today',
    'num_transaction' => 'Sales Transactions',
    'recent_order' => 'Latest 10 Orders',
    'item' => 'Item',
    'payment_method' => 'Payment Method',
    'price_tax_exclusive' => 'Price (Tax Exclusive)',
    'total_price' => 'Total Price',
    'point_of_sales' => 'Cashier',
    'pos_system' => 'POS System',
    'search_products' => 'Search Products',
    'search_category' => 'Search Category',
    'category_name' => 'Category Name',
    'category_name_desc' => 'Category name will be displayed on the sales screen',
    'recent_invoices' => 'Recent Invoices',
    'new_product' => 'New Product',
    'product' => 'Product',
    'qty' => 'Quantity',
    'add_product_msg' => 'You have not added any products to the cart yet',
    'add_product_msg2' => 'Enter the product name to add it',
    'product_page_first' => 'All added products for sale will appear here',
    'product_report_first' => 'All sold products will appear here',
    'expenses_report_first' => 'All expense information will appear here',
    'expenses_report_first_note' => 'Please add a new expense from',
    'payment_method_report_first' => 'Total operations summary for each payment method will appear here',
    'tax_report_first' => 'Tax for sold products will appear here',
    'product_page_first_note' => 'Please add a new product from',
    'product_expense_first' => 'All registered expenses will appear here',
    'product_expense_first_note' => 'Please add a new expense from',
    'product_invoice_first' => 'All sales and return invoices will appear here',
    'sales_summary_first' => 'Daily sales summary will appear here',
    'product_invoice_first_note' => 'To start selling, please click',
    'current_order' => 'Current Order',
    'clear_order' => 'Clear Cart',
    'pause_order' => 'Pause Cart',
    'paused_orders' => 'Paused Carts',
    'subtotal' => 'Subtotal',
    'tax' => 'Tax',
    'total' => 'Total',
    'from_date' => 'Starting Date',
    'to_date' => 'Ending Date',
    'pay' => 'Pay',
    'product_form' => 'Product Form',
    'product_path' => 'Product List - New Product',
    'thumbnail' => 'Product Image',
    'thumbnail_cat' => 'Category Image',
    'thumbnail_msg' => 'Set a thumbnail image for the product. Only *.png, *.jpg, and *.jpeg image files are accepted',
    'thumbnail_cat_msg' => 'Set a thumbnail image for the category. Only *.png, *.jpg, and *.jpeg image files are accepted',
    'active' => 'Active',
    'disabled' => 'Disabled',
    'set_product_status' => 'Set Product Status',
    'set_product_cat' => 'Select the category for the product',
    'set_category_status' => 'Set Category Status',
    'general' => 'General',
    'product_name' => 'Product Name',
    'product_name_msg' => 'Product name is required.',
    'inventory' => 'Inventory',
    'barcode' => 'Barcode',
    'generate' => 'Generate',
    'barcode_number' => 'Barcode Number',
    'barcode_msg' => 'The barcode number will be used to search for products',
    'quantity' => 'Quantity',
    'quantity_msg' => 'Available quantity',
    'enter_quantity_msg' => 'Enter the quantity for the mentioned branch above.',
    'enter_price_msg' => 'Enter the price (tax inclusive) for the mentioned branch above.',
    'pricing' => 'Pricing',
    'sell_price' => 'Selling Price',
    'wholesale_price' => 'Wholesale Price',
    'sell_price_tax_inclusive' => 'Selling Price (Tax Inclusive)',
    'wholesale_price_tax_inclusive' => 'Wholesale Price (Tax Inclusive)',
    'product_price' => 'Product Price',
    'product_price_msg' => 'The selling price that will appear on the invoice, you can input different prices for different branches below',
    'product_cost' => 'Product Cost',
    'product_cost_tax_inclusive' => 'Product Cost (Tax Inclusive)',
    'return_amount_tax_inclusive' => 'Return Amount (Tax Inclusive)',
    'product_cost_msg' => 'Specify the cost of the product.',
    'tax_type' => 'Tax Type',
    'tax_schema' => 'Tax Category',
    'tax_type_msg' => 'Select the product tax type.',
    'tax_schema_msg' => 'Select the product tax category.',
    'cancel' => 'Cancel',
    'name_error' => 'The name field is required.',
    'sell_price_error' => 'The selling price field is required.',
    'cost_error' => 'The cost field is required.',
    'tax_error' => 'The tax field is required.',
    'quantity_error' => 'The quantity field is required.',
    'image_error' => 'The uploaded image format is not accepted.',
    'sell_price_num_error' => 'The selling price must be a number.',
    'cost_num_error' => 'The cost must be a number.',
    'quantity_integer_error' => 'The quantity must be an integer.',
    'barcode_taken_error' => 'The entered barcode is already in use.',

    'products' => 'Products',
    'search_product' => 'Search for a product',
    'search_customer' => 'Search for a customer',
    'add_customer' => 'Add a customer to the order',
    'no_match_error' => 'No matching records found',
    'all' => 'All',
    'barcode2' => 'Barcode',
    'price' => 'Price',
    'status2' => 'Status',
    'actions' => 'Actions',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'product_update_success' => 'Product updated successfully',
    'add_product' => 'Add Product',
    'add_category' => 'Add Category',
    'edit_category' => 'Edit Category',
    'invoices' => 'Invoices',
    'invoices_path' => 'Invoices',
    'invoice_id' => 'Invoice ID',
    'type' => 'Type',
    'date_created' => 'Date Created',
    'pick_date_range' => 'Select Date Range',
    'sale' => 'Sale',
    'return' => 'Return',
    'returned' => 'Returned',
    'view' => 'View',
    'order_details' => 'Order Details',
    'order_summary' => 'Order Summary',
    'print' => 'Print',
    'unit_price' => 'Unit Price',
    'vat' => 'VAT',
    'grand_total' => 'Grand Total',
    'invoice_return' => 'Return Invoice',
    'returned_qty' => 'Returned Quantity',
    'expenses' => 'Expenses',
    'expense_path' => 'Expenses',
    'search_expense' => 'Search for an expense',
    'name' => 'Name',
    'category' => 'Category',
    'categories' => 'Categories',
    'categories_view' => 'View Categories',
    'cost' => 'Cost',
    'description' => 'Description',
    'shipping' => 'Shipping',
    'salaries' => 'Salaries',
    'other' => 'Other',
    'add_expense' => 'Add Expense',
    'expense_name' => 'Expense Name',
    'expense_new_path' => 'Expense List - Add Expense',
    'settings' => 'Settings',
    'main_settings' => 'Main Settings',
    'main_settings_msg' => 'Main settings are displayed in various places such as the invoice',
    'company_logo' => 'Company Logo',
    'company_name' => 'Company Name',
    'company_name_msg' => 'Company name.',
    'vat_number' => 'VAT Number',
    'vat_msg' => 'VAT number is required for ZATCA.',
    'address' => 'Address',
    'address_msg' => 'The address will appear on the invoice.',
    'phone_number' => 'Phone Number',
    'phone_number_msg' => 'The phone number will appear on the invoice.',
    'phone_number_966' => 'Mobile number starts with 9665xxx',
    'edit_main_setting' => 'Edit Main Settings',
    'payment_methods' => 'Payment Methods',
    'payment_methods_msg' => 'Entered payment methods',
    'add_payment_method' => 'Add Payment Method',
    'payment_method_name' => 'Payment Method Name',
    'payment_method_add_success' => 'Payment method added successfully',
    'tax_types' => 'Tax Types',
    'tax_types_msg' => 'Entered tax types',
    'add_tax_type' => 'Add Tax',
    'tax_name' => 'Tax Name',
    'tax_percentage' => 'Tax Percentage',
    'the_tax_percentage' => 'The Tax Percentage',
    'tax_per_error' => 'The percentage must be a valid number.',
    'home' => 'Home',
    'SR' => 'Saudi Riyal',
    'invoices_list' => 'Invoices List',
    'invoice_details' => 'Invoice Details',
    'search_invoice' => 'Search for an invoice',
    'product_edit_form' => 'Edit Product',
    'product_edit' => 'Edit Product',
    'cost_tax_inclusive' => 'Cost (Tax Inclusive)',
    'expense_edit' => 'Edit Expense',
    'expense_add' => 'Add Expense',
    'change_avatar' => 'Change Avatar',
    'remove_avatar' => 'Remove Avatar',
    'cancel_avatar' => 'Cancel Avatar',
    'light' => 'Light',
    'dark' => 'Dark',
    'language' => 'Language',
    'signout' => 'Sign Out',
    'signup' => 'Sign Up',
    'edit_payment_method' => 'Edit Payment Method',
    'taxes' => 'Taxes',
    'edit_tax_type' => 'Edit Tax',
    'disabled_product' => 'Product is disabled!',
    'doesnt_exist_product' => 'Product does not exist!',
    'no_quantity_product' => 'No quantity available for sale!',
    'sell_less_cost' => 'Selling price is less than cost!',
    'error' => 'Error',
    'max_to_sell' => 'The maximum quantity for sale is',
    'max_quantity_with_fulfillment' => 'The maximum quantity with fulfillment is',
    'max_return_quantity' => 'The maximum quantity for return is',
    'return_quantity_info' => 'The maximum returnable quantity is calculated based on the original invoice quantity minus any previously returned items.',
    'remaining' => 'Remaining',

    'original_invoice_not_found' => 'Original invoice information not found',
    'cannot_mix_sale_and_return' => 'Cannot mix sale and return products in the same cart',
    'return_successful' => 'Return processed successfully',
    'return_error' => 'An error occurred while processing the return',
    'variant_product_cannot_be_returned' => 'Variant products cannot be returned',
    'cannot_return_variant_products' => 'Cannot process return: Variant products are not allowed to be returned',

    // Generic success messages
    'created_successfully' => 'Created successfully',
    'updated_successfully' => 'Updated successfully',
    'deleted_successfully' => 'Deleted successfully',
    'saved_successfully' => 'Saved successfully',
    'processed_successfully' => 'Processed successfully',
    'imported_successfully' => 'Imported successfully',
    'exported_successfully' => 'Exported successfully',
    'filter_by_tag' => 'Filter by Tag',
    'all_tags' => 'All Tags',
    'filter_by_category' => 'Filter by Category',
    'all_categories' => 'All Categories',
    'filter_by_type' => 'Filter by Type',
    'all_types' => 'All Types',
    'cannot_edit_default_payment_type' => 'Cannot edit default payment types. Only custom payment types can be modified.',
    'cannot_delete_default_payment_type' => 'Cannot delete default payment types. Only custom payment types can be deleted.',
    'default_payment_type' => 'Default Payment Type',
    'custom_payment_type' => 'Custom Payment Type',
    'default_payment_type_name_readonly' => 'The name of default payment types cannot be changed. You can modify other settings.',
    'cannot_edit_default_payment_type_name' => 'Cannot change the name of default payment types.',
    'payment_method_name_required' => 'Payment method name is required.',
    'payment_method_name_unique' => 'Payment method name must be unique.',
    'payment_method_name_max' => 'Payment method name cannot exceed 255 characters.',
    'confirm_update' => 'Are you sure you want to update?',
    'confirm_delete' => 'Are you sure you want to delete?',

    'return_quantity' => 'Return Quantity',
    'select_products_to_return' => 'Select Products to Return',
    'export' => 'Export',
    'import' => 'Import',


    'import_file' => 'Import File',
    'import_file_download' => 'Download Import File',
    'import_product_file_download_desc' => 'The import file will help you upload all your products at once.',
    'import_product_file_download_desc2' => 'To download the import file, please click ',
    'here' => 'here',
    'file' => 'File',
    'users' => 'Users',
    'users_desc' => 'Control over users',
    'users_add' => 'Add User',
    'users_edit' => 'Edit User',
    'users_add_new' => 'Add New User',
    'users_name' => 'Username',
    'users_email' => 'Email',
    'users_password' => 'Password',
    'sales' => 'Sales',
    'return_sales' => 'Returned Sales',
    'return_sales_transactions' => 'Return Transactions',
    'sales_by_payment_methods' => 'Sales by Payment Methods',
    'invoice_settings' => 'Invoice Settings',
    'subscription_settings' => 'Subscription Settings',
    'currencies' => 'Currencies',
    'currencies_add' => 'Add Currency',
    'currencies_add_new' => 'Add New Currency',
    'currencies_edit' => 'Edit Currency',
    'currencies_desc' => 'Here, you can add or edit currencies.',
    'currency_name' => 'Currency Name',
    'currency' => 'Currency',
    'currency_symbol' => 'Currency Symbol',
    'monthly' => 'Monthly',
    'annually' => 'Annually',
    'free' => 'Free',
    'subscription' => 'Subscription',
    'subscription_page' => 'Subscription Page',
    'subscribe' => 'Subscribe',
    'subscribed' => 'Subscribed',
    'soon' => 'Soon',
    'print_disabled_message' => 'To print the barcode, please save the product.',
    'date' => 'Date',
    'open_registery' => 'Open Register',
    'close_registery' => 'Close Register',
    'closed_registery_success' => 'Register closed successfully',
    'registery_open' => 'Register Open',
    'registery_close' => 'Register Close',
    'registery_opener' => 'Register Opener',
    'registery_closer' => 'Register Closer',
    'total_cash' => 'Total Amount (Cash)',
    'cash' => 'Cash',
    'card' => 'Card',
    'total_card' => 'Total Amount (Card)',
    'not_reconciled' => 'Not Reconciled',
    'not_reconciled_plural' => 'Not Reconciled',
    'reconciled_plural' => 'Reconciled',
    'reconciled' => 'Reconciled',
    'sale_registeries' => 'Sale Registers',
    'sale_registry_reconciliation' => 'Sale Register Reconciliation',
    'sale_registry_reconciliation_first_message' => 'Sale register reconciliation information will appear here.',
    'sale_registry_reconciliation_first_message_note' => 'To start the sale, please click ',
    'entered' => 'Entered',
    'actual' => 'Actual',
    'transaction' => 'Transaction',
    'sales_last_45_days' => 'Sales Last 45 Days',
    'sales_last_45_days_desc' => 'Chart shows total sales and returns for the last 45 days.',
    'dashboard_sales_tooltip' => 'Total sales minus returns for the selected period',
    'pos_add_customer' => 'Add Customer to Invoice',
    'dashboard_return_tooltip' => 'Total returns for the selected period (inclusive of tax)',
    'dashboard_sale_transactions_tooltip' => 'Total sales transactions for the selected period',
    'dashboard_return_transactions_tooltip' => 'Total return transactions for the selected period',
    'dashboard_sales_payment_methods_tooltip' => 'Total sales minus returns for each payment method for the selected period (inclusive of tax)',
    'dashboard_expenses_tooltip' => 'Total expenses for the selected period (inclusive of tax)',
    'summary' => 'Summary',
    'your_details' => 'Your Store Information',
    'number_of_months' => 'Number of Months',
    'monthly_price' => 'Monthly Price',
    'month' => 'Month',
    'recommended' => 'Recommended',
    'bank_transfer' => 'Bank Transfer',
    'visa_mastercard_mada' => 'Mada, Visa, Mastercard',
    'bank_account_info' => 'Bank Account Information',
    'bank_name' => 'Bank Name',
    'bank_account_name' => 'Account Holder Name',
    'bank_account_number' => 'Account Number',
    'bank_account_iban' => 'Account IBAN',
    'plan' => 'Plan',
    'yearly' => 'Yearly',
    'subscription_success' => 'Subscription successful',
    'subscription_failed' => 'Payment failed',
    'redirect_homepage' => 'To go to the homepage, please click ',
    'subscription_failed_desc' => 'Please check the payment method and try again on',
    'paid_annually' => 'Paid Annually',
    'choose_your_plan' => 'Choose Your Plan',
    'choose_your_plan_description' => 'Choose the plan that suits your needs and business',
    'already_subscribed_in_better_plan' => 'Cannot subscribe to a lower plan',
    'success_reset_password' => 'Password Has Been Reset Successfully',
    'reset_email_sent' => 'Reset Password Email Has Been Sent, Please Check Your Email',
    'reset_email_exists' => 'Reset Password Email Has Been Sent Previously, Please Check Your Email',
    'reset_password_token_expired' => 'Reset Password Token Expired, Please Reset Your Password Again',

    'email_confirmation_succeed' => 'Email Confirmation Has Been Done Successfully',
    'email_confirmation_failed' => 'There Is Issue While Confirm Your Email. Please Contact The Support Team',
    'login' => 'Login',
    'login_message' => 'Manage your inventory, generate invoices, and sell your products effortlessly with the Point system.',
    'forgot_password' => 'Forgot Password?',
    'password_form' => 'Password',
    'password_form_repeat' => 'Repeat Password',
    'email' => 'Email',
    'new_register' => "Don't have an account? Register ",
    'terms_confirmation' => 'By registering, you agree to the ',
    'terms_conditions' => 'Terms and Conditions',
    'terms' => 'Terms and Conditions',
    'already_membered' => 'Already registered? Log in ',
    'register_thanks' => 'Successfully registered, please log in',
    'net_profit' => 'Net Profit',
    'open_registry_message' => 'Please place the available amount in the register before opening it.',
    'delete_confirmation_message' => 'Are you sure you want to delete',
    'non_taxable_expense' => 'Non-Taxable Expense',
    'reports' => 'Reports',
    'sales_summary' => 'Sales Summary',
    'sales_by_product' => 'Sales by Product',
    'tax_declaration' => 'Tax Declaration',
    'refunds_summary' => 'Refunds Summary',
    'transactions' => 'Transactions',
    'total_sales' => 'Total Sales',
    'total_sales_tax_exclusive' => 'Total Sales (Tax Exclusive)',
    'total_tax_sales' => 'Total Tax Sales',
    'total_tax_expenses' => 'Total Tax Expenses',
    'net_tax' => 'Net Tax',
    'total_tax_return' => 'Total Tax Returns',
    'total_return_tax_exclusive' => 'Total Returns (Tax Exclusive)',
    'total_tax' => 'Total Taxes',
    'net_sales_tax_exclusive' => 'Net Sales (Tax Exclusive)',
    'cogs' => 'Cost of Goods Sold (Tax Exclusive)',
    'gross_profit' => 'Gross Profit (Tax Exclusive)',
    'avg_transaction_value' => 'Average Transaction Value (Tax Exclusive)',
    'gross_profit_margin' => 'Gross Profit Margin %',
    'quantity_sold' => 'Quantity Sold',
    'quantity_returned' => 'Quantity Returned',
    'payment_transactions' => 'Payment Transactions',
    'return_transactions' => 'Return Transactions',
    'net_amount' => 'Net Amount',
    'total_paid' => 'Total Paid',
    'total_expenses_tax_exclusive' => 'Total Expenses (Tax Exclusive)',
    'total_returned' => 'Total Returned',
    'distribution_percentage' => 'Distribution Percentage',
    'tax_inclusive' => 'Tax Inclusive',
    'tax_exclusive' => 'Tax Exclusive',
    'num_transaction_return' => 'Number of Return Transactions',
    'branches_registeries' => 'Branches and Registers',
    'show_branches' => 'Show Branches',
    'show_registers' => 'Show Registers',
    'add_branch' => 'Add Branch',
    'add_registers' => 'Add Sales Registers',
    'add_registers_desc' => 'Minimum number of registers to add is one',
    'branch_details' => 'Branch Details',
    'branch_details_desc' => 'Add branch information and address',
    'register_details' => 'Sales Register Details',
    'register_name' => 'Register Name',
    'register_details_desc' => 'Add sales registers for the branch',
    'registers' => 'Registers',
    'branch_name' => 'Branch Name',
    'branch_name_tootltip' => 'Branch name should not be already in use',
    'branch_address' => 'Branch Address',
    'branch_address_tooltip' => 'Please write the address in full',
    'next' => 'Next',
    'back' => 'Back',

    'register' => 'Register',
    'thebranch' => 'The Branch',
    'customers_suppliers' => 'Customers',
    'customers' => 'Customers',
    'customer_name' => 'Customer Name',
    'notes' => 'Notes',
    'null' => 'None',
    'customers_show' => 'Show Customers',
    'customers_search' => 'Search for Customer',
    'customers_add' => 'Add Customer',
    'customers_first' => 'All customers will appear here',
    'customers_first_note' => 'Please add a new customer from',
    'choose_customer_header' => 'Please choose the customer to add to the invoice',
    'choose_cart_header' => 'Please choose the cart to retrieve',
    'suppliers' => 'Suppliers',
    'suppliers_show' => 'Show Suppliers',
    'apply_discount' => 'Discount',
    'close' => 'Close',
    'product_details' => 'Product Details and Quantities',
    'percentage' => 'Percentage',
    'amount' => 'Amount',
    'discount_amount' => 'Discount Amount',
    'is_it_cash' => 'Is it Cash Payment?',
    'is_it_cash_desc' => 'Choosing cash payment will activate the amount paid and returned in the cash register',
    'recieve_cash' => 'Receive Amount',
    'recieve_cash_desc' => 'Please input the amount received from the customer to calculate the refund amount',
    'receive_amount' => 'Received Amount',
    'paid_amount' => 'Paid Amount',
    'return_amount' => 'Remaining Amount for Customer',
    'multipay' => 'Multiple Payments',
    'receive_payment' => 'Receive Payment',
    'only_one_tax' => 'The added product does not match the tax for the products in the cart',
    'discounted_invoice_return_message' => 'Due to a discount on the invoice, the entire invoice must be returned',
    'remaining_amount' => 'Remaining Amount: ',
    'sale_screen' => 'Sales Screen',
    'sale_screen_desc' => 'The sales screen that will appear in the sales register with images or not',
    'sale_images' => 'Image-based Sales Screen',
    'sale_default' => 'Main Sales Screen',

    'suppliers_search' => 'Search for Supplier',
    'suppliers_add' => 'Add Supplier',
    'suppliers_first' => 'No suppliers added yet',
    'suppliers_first_note' => 'To add a new supplier, please click',
    'supplier_name' => 'Supplier Name',
    'supplier_display' => 'View Supplier',
    'customer_display' => 'View Customer',
    'purchase_invoices' => 'Purchase Invoices',

    'get_amount_message' => 'To receive the amount, please click',
    'invoice_remaining_amount_message' => 'The invoice contains an outstanding amount that has not been paid. Please pay the amount before returning the invoice.',
    'needs_subscription_page_first' => 'Your subscription does not support this feature',
    'needs_subscription_page_first_note' => 'To upgrade to a higher package, please click',
    'premium' => 'Premium',
    'subscription_settings_desc' => 'From here, you can subscribe, upgrade packages, and add new services such as branches and registers',
    'addons' => 'Add-ons',
    'subscription_note_desc' => 'When adding a branch, you will get additional employees and registers',
    'subscription_note_desc_add' => 'You will be able to review the order and subscribe in the next step',
    'member' => 'User',
    'branch' => 'Branch',
    'date_start' => 'Start Date',
    'date_expired' => 'End Date',
    'number_of_days' => 'Number of Days',
    'day' => 'Day',
    'next_invoice' => 'Next Invoice',
    'invoices_total' => 'Total Invoices',
    'branch_numbers' => 'Number of Branches',
    'register_numbers' => 'Number of Registers',
    'member_numbers' => 'Number of Users',
    'stock_control' => 'Stock Control',
    'purchase_order' => 'Purchase Orders',
    'purchase_order_single' => 'Purchase Order',
    'purchase_order_add' => 'Add Purchase Order',
    'purchase_order_add_msg' => 'Add products to the purchase order',
    'purchase_order_list' => 'Purchase Order List',
    'purchase_order_details' => 'Purchase Order Details',
    'purchase_order_id' => 'Purchase Order ID',
    'search_purchase_orders' => 'Search Purchase Order',
    'invoice_date' => 'Invoice Date',
    'supplier' => 'Supplier',
    'choose_branch' => 'Choose Branch',
    'choose_branch_meg' => 'Quantities will be added to the selected branch',
    'choose_supplier' => 'Choose Supplier',
    'choose_supplier_msg' => 'Please select the supplier for the added products',
    'select_state' => 'Select State',
    'all_states' => 'All States',
    'customer_screen' => 'Customer Display',
    'welcome_customer' => 'Welcome',
    'welcome_to_our_store' => 'Welcome to',
    'customer_display_idle_message' => 'Thank you for shopping with us',
    'your_items' => 'Your Items',
    'no_items_in_cart' => 'No items in cart',
    'last_updated' => 'Last Updated',
    'current_quantity' => 'Current Quantity',
    'current_price' => 'Current Price',
    'current_cost' => 'Current Cost Price',
    'cost_price' => 'Cost Price',
    'purchased_quantity' => 'Purchased Quantity',
    'cost_after_new_price' => 'Cost After New Price',
    'quantity_after_new_purchase' => 'Total Quantity',
    'total_cost' => 'Total Cost',
    'total_change' => 'Total Quantity Change',
    'add_branch_please' => 'Please add a branch first',
    'change_in_quantity' => 'Change in Quantity',
    'change_in_cost' => 'Change in Value',
    'counted_quantity' => 'Quantity After Count',
    'total_changed_quantity' => 'Total Changed Quantity',
    'total_changed_cost' => 'Total Changed Value',
    'stock_count_detials' => 'Stock Count Details',
    'avaliable_quantity_to_transfer' => 'Available Quantity to Transfer',
    'quantity_to_transfer' => 'Quantity Transferred',
    'total_quantity_transferd_branch' => 'Total Quantity in Transferred Branch',
    'branch_from' => 'Transferred From Branch',
    'branch_to' => 'Transferred To Branch',
    'add_branches_please' => 'Please add branches',
    'transfer_order_details' => 'Transfer Order Details',
    'transfer_order_id' => 'Transfer Order ID',
    'add_transfer_order' => 'Add Transfer Order',
    'total_transfered' => 'Total Transferred',
    'quantity_before_transfer' => 'Quantity Before Transfer',
    'error_same_branches' => 'Transferred from and transferred to branches must be different',
    'remove_order_id' => 'Remove Order ID',
    'add_remove_order' => 'Add Remove Order',
    'total_removed' => 'Total Removed',
    'remove_order_detials' => 'Remove Order Details',
    'removed_quantity' => 'Removed Quantity',
    'after_removed_quantity' => 'Quantity After Removal',
    'transfer_orders' => 'Transfer Orders',
    'transfer_order' => 'Transfer Order',
    'remove_orders' => 'Remove Orders',
    'stock_count_orders' => 'Stock Count Orders',
    'count_order' => 'Count Order',
    'search_remove_orders' => 'Search Remove Orders',
    'add_remove_order_single' => 'Add Remove Order',
    'remove_orders_list_path' => 'Remove Orders List',
    'search_count_orders' => 'Search Count Orders',
    'add_count_order_single' => 'Add Count Order',
    'product_list_path' => 'Product List',
    'stock_order_id' => 'Stock Order ID',
    'transfer_list_path' => 'Transfer Orders List',
    'add_transfer_order_single' => 'Add Transfer Order',
    'tranfer_order_list_path' => 'Transfer Orders List',
    'transfer_order_add' => 'Add Transfer Order',
    'remove_order_add' => 'Add Remove Order',
    'remove_order_list' => 'Remove Orders List',

    'search_transfer_orders' => 'Search Transfer Orders',
    'add_purchase_order_single' => 'Add Purchase Order',
    'count_order_details' => 'Count Order Details',
    'quantity_before_count' => 'Quantity Before Count',
    'quantity_after_count' => 'Quantity After Count',
    'quantity_before_remove' => 'Quantity Before Removal',
    'quantity_after_remove' => 'Quantity After Removal',
    'count_list_path' => 'Count Orders List',
    'remove_list_path' => 'Remove Orders List',
    'remove_order_details' => 'Remove Order Details',
    'remove_order' => 'Remove Order',
    'employees_groups' => 'Employees and Permissions',
    'employees' => 'Employees',
    'employee_permissions' => 'Employee Permissions',
    'add_cash' => 'Add Cash',
    'withdraw_cash' => 'Withdraw Cash',
    'cash_transactions' => 'Cash Add/Withdraw',
    'note' => 'Note',
    'enter_amount' => 'Enter amount',
    'enter_note' => 'Enter note (optional)',
    'save' => 'Save',
    'update' => 'Update',
    'cash_added_successfully' => 'Cash added successfully',
    'cash_withdrawn_successfully' => 'Cash withdrawn successfully',
    'added_cash' => 'Added Cash',
    'withdrawn_cash' => 'Withdrawn Cash',
    'no_cash_available_to_withdraw' => 'No cash available to withdraw',
    'register_not_open' => 'Register is not open',
    'insufficient_cash_available' => 'Insufficient cash available. Maximum amount you can withdraw is :available SR',
    'employee_permissions_search' => 'Search Employee Permissions',
    'employee_permissions_add' => 'Add Employee Permissions',
    'dashboard_stats' => 'Dashboard Stats',
    'cashier_perm' => 'Cashier Permissions',
    'reports_perm' => 'Reports Permissions',
    'products_perm' => 'Products and Categories Permissions',
    'close_register' => 'Close Register',
    'wholesale_sale' => 'Wholesale Sale',
    'discount' => 'Discount',
    'max_discount_number' => 'Maximum Discount Amount',
    'max_discount_percentage' => 'Maximum Discount Percentage',
    'products_show' => 'View Products',
    'show' => 'Show',
    'products_status_change' => 'Change Product Status',
    'products_wholesale_change' => 'Change Wholesale Price',
    'products_cost' => 'Product Cost',
    'products_categories' => 'Product Categories',
    'customers_invoices' => 'Customer Invoices',
    'branches' => 'Branches',
    'theregisters' => 'Registers',
    'register_settings' => 'Register Settings',
    'select_all' => 'Select All',
    'role_name' => 'Role Name',
    'permissions' => 'Permissions',
    'all_permissions' => 'All Permissions',
    'enable' => 'Enable',
    'add' => 'Add',
    'recieve_post' => 'Receive Postponed Amount',
    'postpay' => 'Postpaid Sale',
    'show_customer_numbers' => 'View Customer Numbers',
    'show_supplier_numbers' => 'View Supplier Numbers',
    'show_edit' => 'View and Edit',
    'show_add_edit' => 'View, Add, and Edit',
    'date_modified' => 'Last Modified',

    'employee_add' => 'Add Employee',
    'employee_search' => 'Employee Search',
    'employee_edit' => 'Edit Employee',
    'add_new_employee' => 'Add New Employee',
    'warning_main_account' => 'For security reasons, main account information cannot be updated. Please contact technical support for updates.',
    'employees_perm_set' => 'Set Employee Permissions',
    'perm_branches' => 'Permitted Branches',
    'choose_branches' => 'Choose Branches',
    'employee_permission_add' => 'Add Employee Permission',

    'permited_branches_required' => 'Permitted branches are required',
    'stock_control_perms' => 'Stock Control Permissions',
    'register_open_and_close_enable' => 'Enable Register Open and Close',
    'add_cat_msg' => 'You haven\'t added any product categories.',
    'add_cat_msg2' => 'Please add categories and assign products to them.',
    'add_cat_msg3' => 'You haven\'t added any products to the category.',
    'remove_orders_first' => 'You haven\'t added any removal orders.',
    'remove_orders_first_note' => 'Please add a removal order from',
    'purchase_orders_first' => 'You haven\'t added any purchase orders.',
    'purchase_orders_first_note' => 'Please add a purchase order from',
    'transfer_orders_first' => 'You haven\'t added any transfer orders.',
    'transfer_orders_first_note' => 'Please add a transfer order from',
    'count_orders_first' => 'You haven\'t added any count orders.',
    'count_orders_first_note' => 'Please add a count order from',
    'employees_first' => 'All employees will appear here.',
    'employees_first_note' => 'Please add a new employee from',
    'groups_first' => 'You haven\'t added any employee permissions.',
    'groups_first_note' => 'Please add a new permission group from',

    'group_percnetage_message' => 'Percentage discount is linked to amount discount. If the percentage result is higher than the amount, the amount will be used.',
    'max_discount_amount' => 'Maximum allowed discount amount is',
    'number_of_products' => 'Number of allowed products',
    'number_of_invoices' => 'Number of allowed invoices',
    'number_of_employees' => 'Number of allowed employees',
    'employee_permissions_control' => 'Employee Permissions Control',
    'open_close_register' => 'Open and Close Sales Registers',
    'unlimited' => 'Unlimited',
    'all_reports_feature' => 'All Reports',
    'product_cat_page_first' => 'All product categories will appear here.',
    'product_cat_page_first_note' => 'To add a new category, please click',
    'arabic_letters_only' => ":attribute should not have any letters than arabic",
    'english_letters_only' => ":attribute should not have any letters than english",
    'start_end_3' => "Vat number must start and end with the number 3",
    "zatca_unavailable" => 'Sorry, Zatca is not available currently, please retry later. if this problem occurs frequently, please contact the support',
    "invoice_error" => 'Sorry, There is an error, please contact the support',
    "zero_price_purchase_warning" => 'Cannot submit purchase order with zero price. Please enter a valid price for all products.',
    "zero_quantity_purchase_warning" => 'Cannot submit purchase order with zero quantity. Please enter a valid quantity for all products.',

    // Fulfillment related messages
    "fulfillment_orders" => "Fulfillment Orders",
    "view_fulfillment" => "View Fulfillment",
    "update_fulfillment" => "Update Fulfillment",
    "fulfillment_details" => "Fulfillment Details",
    "fulfillment_id" => "Fulfillment ID",
    "source_branch" => "Source Branch",
    "fulfillment_branch" => "Fulfillment Branch",
    "fulfillment_type" => "Fulfillment Type",
    "branch_transfer" => "Branch Transfer",
    "delivery" => "Delivery",
    "requested_quantity" => "Requested Quantity",
    "fulfilled_quantity" => "Fulfilled Quantity",
    "search_fulfillments" => "Search Fulfillments",
    "all_statuses" => "All Statuses",
    "all_branches" => "All Branches",
    "date_range" => "Date Range",
    "pending" => "Pending",
    "partially_fulfilled" => "Partially Fulfilled",
    "fulfilled" => "Fulfilled",
    "cancelled" => "Cancelled",
    "back_to_list" => "Back to List",
    "view_details" => "View Details",
    "no_fulfillments_found" => "No fulfillments found",
    "fulfillment_updated" => "Fulfillment updated successfully",
    "fulfillment_update_error" => "Error updating fulfillment",
    "fulfillment_confirmed" => "Fulfillment confirmed",
    "delivery_scheduled" => "Delivery scheduled",
    "please_select_customer_for_fulfillment" => "Please select a customer for fulfillment",
    "no_branches_with_stock" => "No branches with available stock",
    "customer_has_no_address" => "Customer has no address",
    "please_complete_all_fields" => "Please complete all fields",

    ZatcaZeroVatCategory::MEDICINES->value => 'Medicines and medical equipment',
    ZatcaZeroVatCategory::METALS->value => 'Qualifying metals',
    ZatcaZeroVatCategory::MILITARY->value => 'supply of qualified military goods',
    ZatcaZeroVatCategory::LOCAL_TRANSPORT->value => 'Any services relating to Goods or passenger transportation, as defined in article twenty five of these Regulations',
    ZatcaZeroVatCategory::QUALIFYING_MEANS_OF_TRANSPORT->value => 'Supply of a qualifying means of transport',
    ZatcaZeroVatCategory::SUPPLY_OF_PASSENGER_INTERNATIONAL_TRANSPORT->value => 'Services directly connected and incidental to a Supply of international passenger transport',
    ZatcaZeroVatCategory::PASSENGER_INTERNATIONAL_TRANSPORT->value => 'international transport of passengers',
    ZatcaZeroVatCategory::GOODS_INTERNATIONAL_TRANSPORT->value => 'The international transport of Goods',

    'cannot_add_non_taxable_product' => 'Cannot add product without vat if you enabled zatca',
    'cannot_add_customer' => 'Cannot Add this customer. Please complete his data before add him to the invoice',
    'customers_have_issues' => 'The Customers who are not match zatca integration specifications. please update vat number and address',
    'products_have_issues' => 'The Products that are not match zatca integration specifications. please update tax type and product english name',
    'reset' => 'Reset Password',

    'marketplace' => "Marketplace",
    'installed_apps' => "Installed Apps",
    'printa4' => "Print A4",
    'today' => "Today",
    'yesterday' => "Yesterday",
    'last_7_days' => "Last 7 Days",
    'last_30_days' => "Last 30 Days",
    'this_month' => "This Month",
    'last_month' => "Last Month",
    'custom_range' => "Custom Range",
    'remove_discount' => "Remove Discount",
    'cashier_settings' => "Cashier Settings",
    'cashier_settings_desc' => "Here you can adjust the general cashier settings that will be applied to all branches and registers",
    'edit_cashier_settings' => "Edit Cashier Settings",
    'street_name' => "Street Name",
    'building_number' => "Building Number",
    'province' => "Province",
    'select_province' => "Select Province",
    'district' => "District",
    'district_name' => "District Name",
    'district_name_msg' => "Here you can add the district name to comply with",
    'city' => "City",
    'select_city' => "Select City",
    'postal_code' => "Postal Code",
    'CR' => "Commercial Registration",
    'CR_msg' => "Please enter the commercial registration to use it on the invoice",
    'CR_10' => "The commercial registration must be 10 digits",
    'VAT_15' => "The VAT number must be 15 digits",
    'address_issues' => "Please verify the accuracy of the address registered on the platform",
    'choose_permitted_payment_methods' => "Allowed Payment Methods",
    'choose_permitted_payment_methods_desc' => "Here you can specify the allowed payment methods for the selected register",
    'business_category' => "Here you can specify the type of business, e.g., grocery store",
    'invoice_settings_desc' => "Here you can adjust the invoice settings that will apply to all invoices",
    'edit_invoice_settings' => "Edit Invoice Notes",
    'invoice_notes' => "Invoice Notes",
    'invoice_notes_updated' => "Invoice notes updated successfully",
    'error_loading_paused_carts' => "Error loading paused carts. Please try again.",
    'no_paused_carts' => "No paused carts found.",

    'customers_edit' => "Edit Customer",
    'notifications' => 'Notifications',
    'notifications_read_all' => 'Read All',
    'loading' => 'Loading',
    'no_notifications' => 'No Notifications',
    'subscription_invoice_first' => 'All subscription invoices will appear here',
    'subscription_invoice_first_note' => 'To browse subscriptions, please click',
    'subscription_invoices' => 'Subscription Invoices',
    'non_taxable_product' => 'Non-taxable Product',
    "choose_section_to_show_product" => 'Please choose a section to start showing the products',
    "able_to_edit_returnable_product" => 'Enable to alter returnable product selection',
    "apps" => 'Apps',
    "price_range" => "Price Range",


    'get_data' => 'Resource fetched successfully.',
    'create_data' => 'Resource created successfully.',
    'update_data' => 'Resource updated successfully.',
    'delete_data' => 'Resource deleted successfully.',
    'OR' => 'OR',
    'main_currency' => 'Main Currency',
    'default_currency_desc' => "Once you enable the default currency, it will be used across transactions. You can specify different currencies for different branches.",
    'product_settings' => "Product Settings",
    'edit_product_settings' => 'Edit Product Settings',
    'product_settings_desc' => 'Here, you can change the product settings',
    'enable_weighted_product_type' => 'Enable wieghted product type',
    'extra_product_types' => 'Extra Product Types',
    'closed' => 'Closed',
    'add_single_branch' => 'Add a Branch',
    'add_single_register' => 'Add a Register',
    'branch_settings' => 'Branch Settings',
    'select_currency' => 'Select Currency',

    'error_creating_register' => 'Error creating register',
    'error_updating_branch' => 'Error updating branch',
    'add_register' => 'Add Register',
    'select_branch' => 'Select Branch',
    'select_branch_tooltip' => 'Please select the branch to add the register',
    'register_name_tooltip' => 'Please enter the register name',
    'register_name_required' => 'Register name is required',
    'register_name_max' => 'Register name must be less than 255 characters',
    'register_name_unique' => 'Register name must be unique',
    'register_name_regex' => 'Register name must be alphanumeric',
    'enter_register_name' => 'Please enter the register name',
    'register_setting' => 'Register Settings',
    'register_max_open_hours' => 'Maximum Register Open Hours',
    'register_max_open_hours_desc' => 'Set the maximum number of hours a register can remain open before showing a warning message',
    'register_open_too_long_title' => 'Register Has Been Open Too Long',
    'register_open_too_long_message' => 'This register has been open for :hours hours. It is recommended to close registers after :max_hours hours of operation.',

    'simple_product' => 'Simple',
    'weighted_product' => 'Weighted',
    'service_product' => 'Service',
    'add_weighted_product' => 'Add Weighted Product',
    'create_product' => 'Add Product',
    'service_product_add_error' => 'Cannot add service product',
    'transliterate' => 'Transliterate',
    'returnable' => 'Returnable',
    'returnable_product' => 'Returnable Product',
    'returnable_product_msg' => 'When not selected, the product cannot be returned.',
    'advanced_options' => 'Advanced Options',
    'select_branch_currency' => 'Select Branch Currency',
    'product_sell_purchase' => 'Sell and Purchase Product',
    'sellable_product' => 'Sellable Product',
    'sellable_product_msg' => 'When not selected, the product cannot be sold.',
    'purchaseable_product' => 'Purchaseable Product',
    'purchaseable_product_msg' => 'When not selected, the product cannot be purchased.',
    'tax_cost' => 'Tax and Cost',
    'non_purchaseable_product_error' => 'Cannot purchase this product',
    'non_sellable_product_error' => 'Cannot sell this product',
    'composite_product' => 'Composite',
    'variant_product' => 'Variant',
    'calculated_from_components' => 'Calculated from components',
    'max_possible' => 'Maximum possible',
    'composite_possible' => 'max possible',
    'component' => 'Component',
    'per_unit' => 'Per unit',
    'composite_quantity_exceeds_max' => 'The quantity for :product exceeds the maximum possible (:max) based on available components.',
    'tags' => 'Tags',
    'add_tag' => 'Add Tag',
    'tags_desc' => 'Here you can add tags to the products',
    'tag_name' => 'Tag Name',
    'edit_tag' => 'Edit Tag',

    'set_product_tags' => 'Add a tag to use in statistics and other features',
    'no_tags_found' => 'No Tags Found',
    'no_tags_description' => 'You haven\'t created any tags yet. Tags help you organize and categorize your products.',
    'your_businesses' => 'Your Businesses',
    'create_new_business' => 'Create New Business',
    'no_businesses' => 'No Businesses',
    'create_first_business' => 'Create your first business to get started.',
    'please_contact_administrator' => 'Please contact your administrator.',
    'switch_to' => 'Switch to',
    'set_as_default' => 'Set as Default',
    'manage_employee_access' => 'Manage Employee Access',
    'default' => 'Default',
    'current' => 'Current',
    'manage_businesses' => 'Manage Businesses',
    'business_name' => 'Business Name',
    'create_new' => '(Create New)',
    'variant_instructions' => 'Variant Selection',
    'variant_instructions_desc' => 'Select from existing variants or type to create new ones. Newly created items will be automatically selected.',
    'variant_created_successfully' => 'Variant created successfully',
    'option_created_successfully' => 'Option created successfully',
    'assign_employee_to_business' => 'Assign Employee to Business',
    'confirm_remove_access' => 'Are you sure you want to remove this business access?',
    'current_assignments' => 'Current Business Assignments',
    'employee' => 'Employee',
    'enter_business_description' => 'Enter business description',
    'enter_business_name' => 'Enter business name',
    'remove_access' => 'Remove Access',
    'select_business' => 'Select Business',
    'select_business_placeholder' => '-- Select Business --',
    'select_employee' => 'Select Employee',
    'select_employee_placeholder' => '-- Select Employee --',
    'select_group' => 'Select Group (Role)',
    'select_group_placeholder' => '-- Select Group --',
    'business' => 'Business',
    'owner_account' => 'Owner Account',
    'please_select_business' => 'Please select the business you want to access',
    'add_components_msg' => 'Products',
    'add_components_msg2' => 'Search for Products and add them here.',

    'simple' => 'Simple',
    'product_in_promotion' => "Promotion",
    'composite' => "Composite",
    'service' => "Service",
     'variant' => 'Variant',
    // New variant-related translations
    'clear_selection' => 'Clear Selection',
    'unselect_all' => 'Unselect All',
    'add_variant' => 'Add Variant',
    'new_variant_name' => 'New Variant Name',
    'new_option_name' => 'New Option Name',
    'select_variant' => 'Select Variant',
    'select_option' => 'Select Option',
    'variant_name' => 'Variant Name',
    'options' => 'Options',
    'variants' => 'Variants',
    'generated_products' => 'Generated Products',
    'add_variants_msg' => 'You haven\'t added any variants yet',
    'add_variants_msg2' => 'Click Add Variant to get started',
    'copy_from_first' => 'Copy from first product',
    'show_single_branch' => 'Show Single Branch',
    'show_all_branches' => 'Show All Branches',
    'duplicate_variant' => 'Duplicate variant name',
    'duplicate_option' => 'Duplicate option name',
    'success' => 'Success',
    'wholesale' => 'Wholesale',
    'export_started' => 'Export Started',
    'export_email_notification' => 'Your export is being processed and will be sent to your email when complete.',
    'select_customer' => 'Select Customer',
    'create_customer' => 'Create Customer',
    'customer_created_successfully' => 'Customer created successfully!',
    'recent_customers' => 'Recent Customers',
    'no_customers_found' => 'No customers found',
    'start_typing_to_search' => 'Start typing to search for customers',
    'selected_customer' => 'Selected Customer',
    'currently_selected' => 'Currently Selected',
    'remove_customer' => 'Remove Customer',
    'click_remove_to_select_different_customer' => 'Click the remove button to remove this customer',
    'edit_invoice_notes' => 'Edit Invoice Notes',
    'no_customer' => 'No customer assigned to this invoice',
    'customer_info' => 'Customer Info',
    'invoice_info' => "Invoice Info",
    'promotions' => 'Promotions',
    'add_promotion' => 'Add Promotion',
    'add_promotion_desc' => 'Add a new promotion to the system',
    'promotion_name' => 'Promotion Name',
    'promotion_type' => 'Promotion Type',
    'percentage_discount' => 'Percentage Discount',
    'fixed_discount' => 'Fixed Discount',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'discount_value' => 'Discount Value',
    'status' => 'Status',
    'inactive' => 'Inactive',
    'promotion_details' => 'Promotion Details',
    'promotion_details_desc' => 'Here you can edit the promotion details',
    'promotion_details_edit' => 'Edit Promotion Details',
    'promotion_details_delete' => 'Delete Promotion Details',
    'promotion_details_add' => 'Add New Promotion Details',
    'edit_promotion' => 'Edit Promotion Details',
    'discount_percentage' => 'Discount Percentage',
    'select_branches' => 'Select Branches',
    'included_products' => 'Included Products',
    'price_before_discount' => 'Price Before Discount',
    'price_after_discount' => 'Price After Discount',


    // Fulfillment status labels
    'available' => 'Available',
    'partially_fulfillable' => 'Partially Fulfillable',
    'not_available' => 'Not Available',
    'requested' => 'Requested',
    'fulfilled' => 'Fulfilled',
    'fulfillment_arranged' => 'Fulfillment has been arranged',

    // Fulfillment options
    'fulfillment_options' => 'Fulfillment Options',
    'other_branch' => 'Other Branch',
    'delivery' => 'Delivery',
    'delivery_fulfillment_note' => 'Delivery Fulfillment',
    'delivery_inventory_note' => 'Note: Delivery fulfillments do not deduct from inventory as they are handled outside of the system.',
    'select_customer_for_delivery' => 'Select Customer for Delivery',
    'select_customer_for_delivery_note' => 'Please select a customer to continue with delivery fulfillment.',
    'select_customer_for_branch' => 'Select Customer for Branch Fulfillment',
    'select_customer_for_branch_note' => 'Please select a customer to continue with branch fulfillment.',

    // Branch fulfillment modal
    'fulfill_from_another_branch' => 'Fulfill from Another Branch',
    'available_in_current_branch' => 'Available in Current Branch',
    'quantity_to_fulfill' => 'Quantity to Fulfill',
    'maximum_available' => 'Maximum Available',
    'transfer_type' => 'Transfer Type',
    'immediate_transfer' => 'Immediate Transfer',
    'scheduled_transfer' => 'Scheduled Transfer',
    'confirm_fulfillment' => 'Confirm Fulfillment',

    // Delivery fulfillment modal
    'fulfill_by_delivery' => 'Fulfill by Delivery',
    'delivery_address' => 'Delivery Address',
    'enter_delivery_address' => 'Enter the delivery address...',
    'delivery_date' => 'Delivery Date',
    'delivery_time' => 'Delivery Time',
    'delivery_notes' => 'Delivery Notes',
    'delivery_notes_placeholder' => 'Optional notes for delivery...',
    'morning' => 'Morning',
    'afternoon' => 'Afternoon',
    'evening' => 'Evening',
    'schedule_delivery' => 'Schedule Delivery',

    // Fulfillment messages
    'please_complete_all_fields' => 'Please complete all required fields',
    'please_select_customer' => 'Please select a customer first',
    'fulfillment_confirmed' => 'Fulfillment from branch confirmed',
    'delivery_scheduled' => 'Delivery has been scheduled',
    'no_branches_with_stock' => 'No other branches have stock available for this product',
    'please_select_customer_for_fulfillment' => 'Please select a customer before proceeding with fulfillment',
    'customer_has_no_address' => 'The selected customer has no address in their profile',
    'active_promotions' => 'Active Promotions',
    'fulfillment_reset_customer_changed' => 'Fulfillment arrangements have been reset due to customer change',
    'fulfillment_reset_customer_removed' => 'Fulfillment arrangements have been reset due to customer removal',
    'unfulfilled_products_in_cart' => 'Please arrange fulfillment for all unavailable products before proceeding',

    // Fulfillment management
    'fulfillment_orders' => 'Fulfillment Orders',
    'view_fulfillment' => 'View Fulfillment',
    'update_fulfillment' => 'Update Fulfillment',
    'cancel_fulfillment' => 'Cancel Fulfillment',
    'fulfillment_details' => 'Fulfillment Details',
    'fulfillment_id' => 'Fulfillment ID',
    'source_branch' => 'Source Branch',
    'fulfillment_branch' => 'Fulfillment Branch',
    'fulfillment_type' => 'Fulfillment Type',
    'branch_transfer' => 'Branch Transfer',
    'requested_quantity' => 'Requested Quantity',
    'fulfilled_quantity' => 'Fulfilled Quantity',
    'search_fulfillments' => 'Search Fulfillments',
    'all_statuses' => 'All Statuses',
    'all_branches' => 'All Branches',
    'date_range' => 'Date Range',
    'pending' => 'Pending',
    'partially_fulfilled' => 'Partially Fulfilled',
    'cancelled' => 'Cancelled',
    'cancel_fulfillment_warning' => 'Cancelling this fulfillment will mark all products as cancelled and restore any remaining inventory to the fulfillment branch. For branch transfers, this will add the unfulfilled quantities back to the branch inventory. This action cannot be undone.',
    'cancellation_reason' => 'Cancellation Reason',
    'cancellation_reason_help' => 'Please provide a reason for cancelling this fulfillment (optional).',
    'confirm_cancellation' => 'Confirm Cancellation',
    'fulfillment_cancelled_successfully' => 'Fulfillment cancelled successfully',
    'fulfillment_cancellation_failed' => 'Failed to cancel fulfillment',
    'process_fulfillment' => 'Process Fulfillment',
    'processing' => 'Processing',
    'fulfillment_processed_successfully' => 'Fulfillment processed successfully',
    'fulfillment_processing_failed' => 'Failed to process fulfillment',
    'fulfilled_quantity_exceeds_requested' => 'Fulfilled quantity cannot exceed requested quantity',
    'cannot_cancel_fulfilled_or_cancelled_fulfillment' => 'Cannot cancel a fulfillment that is already fulfilled or cancelled',
    "returned" => "Returned",
    "weighted" => "Weighted",


];
