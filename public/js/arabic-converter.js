/**
 * Simple Arabic Numeral Converter
 * Automatically converts Arabic-Indic numerals (٠-٩) to English numerals (0-9)
 * Only processes fields containing purely numeric content
 * Works with Laravel Blade, Livewire, Bootstrap, and Alpine.js
 */
class SimpleArabicConverter {
    constructor() {
        this.arabicMap = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        };

        // Performance optimization
        this.processedInputs = new WeakSet();
        this.conversionCache = new Map();
        this.scanTimeout = null;
        this.enabled = true;

        // Debug mode
        this.debug = false;

        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupGlobalHandlers());
        } else {
            this.setupGlobalHandlers();
        }
    }

    setupGlobalHandlers() {
        if (this.debug) console.log('Arabic Converter: Setting up global handlers');

        // Global event delegation - captures ALL input events
        document.addEventListener('input', this.handleInput.bind(this), true);
        document.addEventListener('paste', this.handlePaste.bind(this), true);

        // Framework compatibility
        this.setupLivewireCompatibility();
        this.setupBootstrapCompatibility();
        this.setupMutationObserver();

        // Initial scan for existing inputs with Arabic numerals
        this.scanForNewInputs();
    }

    handleInput(e) {
        if (!this.enabled) return;

        const element = e.target;

        if (this.shouldAutoConvert(element)) {
            if (this.debug) console.log('Arabic Converter: Processing input', element);
            this.convertInput(element);
        }
    }

    handlePaste(e) {
        if (!this.enabled) return;

        const element = e.target;

        if (this.shouldAutoConvert(element)) {
            if (this.debug) console.log('Arabic Converter: Processing paste', element);
            // Small delay to let paste complete
            setTimeout(() => this.convertInput(element), 10);
        }
    }

    /**
     * Simple and safe detection - only process purely numeric content
     */
    shouldAutoConvert(element) {
        // Skip if not an input element
        if (!element || element.tagName !== 'INPUT') return false;

        // Skip if already processed in this event cycle
        if (this.processedInputs.has(element)) return false;

        // Skip if input is empty
        if (!element.value || element.value.trim() === '') {
            return false;
        }

        // ONLY process if the input contains ONLY numeric content
        return this.isOnlyNumericContent(element.value);
    }

    /**
     * Check if the value contains only numeric content (digits, Arabic numerals, decimal point, minus)
     */
    isOnlyNumericContent(value) {
        // Allow only: English digits (0-9), Arabic digits (٠-٩), decimal point (.), minus sign (-)
        const numericOnlyPattern = /^[0-9٠-٩.\-]+$/;

        // Must match the pattern AND contain at least one digit
        const isNumericOnly = numericOnlyPattern.test(value);
        const hasDigits = /[0-9٠-٩]/.test(value);

        return isNumericOnly && hasDigits;
    }

    convertInput(element) {
        // Mark as processed to avoid infinite loops
        this.processedInputs.add(element);

        const originalValue = element.value;
        const cursorStart = element.selectionStart;
        const cursorEnd = element.selectionEnd;

        // Simple conversion: only convert Arabic numerals to English
        let convertedValue = this.conversionCache.get(originalValue);

        if (convertedValue === undefined) {
            // Only convert Arabic numerals to English, keep everything else as-is
            convertedValue = originalValue;
            for (let arabic in this.arabicMap) {
                convertedValue = convertedValue.replace(new RegExp(arabic, 'g'), this.arabicMap[arabic]);
            }

            // Cache the result
            if (this.conversionCache.size > 500) {
                this.conversionCache.clear();
            }
            this.conversionCache.set(originalValue, convertedValue);
        }

        // Only update if value actually changed
        if (convertedValue !== originalValue) {
            element.value = convertedValue;

            // Restore cursor position intelligently
            const lengthDiff = originalValue.length - convertedValue.length;
            const newCursorStart = Math.max(0, cursorStart - lengthDiff);
            const newCursorEnd = Math.max(0, cursorEnd - lengthDiff);

            // Restore selection
            try {
                element.setSelectionRange(newCursorStart, newCursorEnd);
            } catch (e) {
                // Some input types don't support selection
            }

            // Trigger events for framework compatibility
            this.triggerCompatibilityEvents(element);

            if (this.debug) console.log('Arabic Converter: Converted', originalValue, '→', convertedValue);
        }

        // Remove from processed set after a short delay
        setTimeout(() => this.processedInputs.delete(element), 100);
    }

    triggerCompatibilityEvents(element) {
        // Standard input event
        element.dispatchEvent(new Event('input', {
            bubbles: true,
            cancelable: true
        }));

        // Change event for some frameworks
        element.dispatchEvent(new Event('change', {
            bubbles: true,
            cancelable: true
        }));

        // Custom event for advanced frameworks
        element.dispatchEvent(new CustomEvent('arabic-converted', {
            bubbles: true,
            detail: { originalValue: element.value }
        }));
    }

    /**
     * Livewire Compatibility
     */
    setupLivewireCompatibility() {
        // Listen for Livewire updates
        document.addEventListener('livewire:updated', () => {
            if (this.debug) console.log('Arabic Converter: Livewire updated, scanning for new inputs');
            this.scanForNewInputs();
        });

        // Listen for Livewire navigation (Livewire 3)
        document.addEventListener('livewire:navigated', () => {
            if (this.debug) console.log('Arabic Converter: Livewire navigated, scanning for new inputs');
            this.scanForNewInputs();
        });

        // Handle wire:model inputs specifically
        document.addEventListener('input', (e) => {
            if (e.target.hasAttribute('wire:model') ||
                e.target.hasAttribute('wire:model.defer') ||
                e.target.hasAttribute('wire:model.lazy')) {
                // Ensure Livewire gets the converted value
                setTimeout(() => {
                    if (this.shouldAutoConvert(e.target)) {
                        // Force Livewire to sync the converted value
                        e.target.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                }, 50);
            }
        });
    }

    /**
     * Bootstrap Compatibility
     */
    setupBootstrapCompatibility() {
        // Handle Bootstrap modals
        document.addEventListener('shown.bs.modal', () => {
            if (this.debug) console.log('Arabic Converter: Bootstrap modal shown, scanning for new inputs');
            this.scanForNewInputs();
        });

        // Handle Bootstrap tabs
        document.addEventListener('shown.bs.tab', () => {
            if (this.debug) console.log('Arabic Converter: Bootstrap tab shown, scanning for new inputs');
            this.scanForNewInputs();
        });

        // Handle Bootstrap collapse
        document.addEventListener('shown.bs.collapse', () => {
            if (this.debug) console.log('Arabic Converter: Bootstrap collapse shown, scanning for new inputs');
            this.scanForNewInputs();
        });
    }

    /**
     * Dynamic Content Handler
     */
    setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            let hasNewInputs = false;

            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        if (node.tagName === 'INPUT') {
                            hasNewInputs = true;
                        } else if (node.querySelectorAll && node.querySelectorAll('input').length > 0) {
                            hasNewInputs = true;
                        }
                    }
                });
            });

            if (hasNewInputs) {
                // Debounce the scan
                clearTimeout(this.scanTimeout);
                this.scanTimeout = setTimeout(() => {
                    if (this.debug) console.log('Arabic Converter: New inputs detected, scanning');
                    this.scanForNewInputs();
                }, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    scanForNewInputs() {
        // Scan for inputs that already contain Arabic numerals
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            if (this.shouldAutoConvert(input)) {
                this.convertInput(input);
            }
        });
    }

    /**
     * Public API for manual control
     */
    convertElement(element) {
        if (element && element.tagName === 'INPUT') {
            this.convertInput(element);
        }
    }

    convertValue(value) {
        // Only convert if value contains only numeric content
        if (this.isOnlyNumericContent(value)) {
            let result = value;
            for (let arabic in this.arabicMap) {
                result = result.replace(new RegExp(arabic, 'g'), this.arabicMap[arabic]);
            }
            return result;
        }
        return value; // Return unchanged if not purely numeric
    }

    convertValueKeepText(value) {
        // Only convert Arabic numerals, keep everything else (legacy function)
        let result = value;
        for (let arabic in this.arabicMap) {
            result = result.replace(new RegExp(arabic, 'g'), this.arabicMap[arabic]);
        }
        return result;
    }

    // Enable/disable globally
    enable() {
        this.enabled = true;
        if (this.debug) console.log('Arabic Converter: Enabled');
    }

    disable() {
        this.enabled = false;
        if (this.debug) console.log('Arabic Converter: Disabled');
    }

    // Enable/disable debug mode
    setDebug(enabled) {
        this.debug = enabled;
        console.log('Arabic Converter: Debug mode', enabled ? 'enabled' : 'disabled');
    }
}

// Auto-initialize when script loads
(function() {
    // Ensure we don't initialize multiple times
    if (window.arabicConverter) {
        console.warn('Arabic Converter: Already initialized');
        return;
    }

    // Initialize the converter
    window.arabicConverter = new SimpleArabicConverter();

    // Expose utility functions to global scope
    if (typeof window !== 'undefined') {
        // Convert value (only if purely numeric)
        window.convertArabicNumerals = (value) => window.arabicConverter.convertValue(value);

        // Convert value but keep text (legacy function)
        window.convertArabicNumeralsKeepText = (value) => window.arabicConverter.convertValueKeepText(value);

        // Manual element conversion
        window.convertElementArabicNumerals = (element) => window.arabicConverter.convertElement(element);

        // Debug control
        window.enableArabicConverterDebug = () => window.arabicConverter.setDebug(true);
        window.disableArabicConverterDebug = () => window.arabicConverter.setDebug(false);
    }

    console.log('Arabic Converter: Initialized successfully');
})();